# Insurance Claims Data Generator

This project demonstrates **Next.js build-time data generation** using **@faker-js/faker** to create 300 realistic insurance claim records.

## 🏗️ Implementation Overview

### Build-Time Data Generation

The application leverages Next.js's build process to generate dummy data at build time rather than runtime, providing several advantages:

#### ✅ **Performance Benefits**
- **Zero runtime computation overhead** - data is pre-generated
- **Instant page loads** - no loading states or delays
- **Better user experience** - immediate data availability
- **Optimized bundle size** - data is included as static assets

#### ✅ **Development Advantages**
- **Consistent data across environments** - same dataset in dev, staging, and production
- **Reproducible testing** - predictable data for unit and integration tests
- **Reliable demos** - consistent presentation data for showcases
- **Easy debugging** - same data structure every time

#### ✅ **Next.js Integration**
- **Static Site Generation (SSG)** compatible
- **Static exports** - works with `next export`
- **CDN friendly** - can be served from any static host
- **No server required** - fully static deployment possible

## 📊 Generated Data Structure

Each insurance claim record includes:

```typescript
interface ClaimRowData {
  id: string;                    // Unique claim identifier
  patient: {
    name: string;               // Full patient name
    id: string;                 // Patient ID
  };
  serviceDate: string;          // Date of service
  insuranceCarrier: {
    carrierName: string;        // Insurance company name
    planCategory: 'Primary' | 'Secondary';
  };
  amount: CurrencyAmount;       // Claim amount ($xxx.xx format)
  status: ClaimStatus;          // PAID, PENDING, DENIED, etc.
  lastUpdated: {
    date: string;               // Last update date
    time: TimeString;           // Time in 12-hour format
  };
  user: string;                 // User initials (2 letters)
  dateSent: string;             // Date claim was sent
  dateSentOrig: string;         // Original send date
  pmsSyncStatus: {
    status: SyncStatus;         // Sync status with PMS
    description: string;        // Status description
    isSynced: boolean;         // Boolean sync indicator
  };
  provider: {
    name: string;               // Healthcare provider name
    id: string;                 // Provider ID
  };
}
```

## 🚀 Build Process Integration

### Package.json Scripts

```json
{
  "scripts": {
    "prebuild": "node scripts/generate-data.js",
    "build": "next build --turbopack",
    "generate:data": "node scripts/generate-data.js"
  }
}
```

### Build Flow

1. **`npm run build`** triggers the `prebuild` script
2. **`scripts/generate-data.js`** generates 300 unique records
3. **Data saved** to `src/data/insurance-claims.json`
4. **Next.js build** processes the static data
5. **Static pages** generated with pre-populated data

## 📁 File Structure

```
src/
├── data/
│   ├── insurance-claims.json     # Generated data (300 records)
│   └── insurance-data.ts         # Data utility functions
├── components/
│   ├── ClaimRow.tsx              # Individual claim row component
│   ├── DataTable.tsx             # Main table component
│   ├── TableFooter.tsx           # Pagination component
│   └── InsuranceDataDemo.tsx     # Demo dashboard component
└── app/
    └── page.tsx                  # Main page using generated data

scripts/
├── generate-data.js              # Node.js data generation script
└── generate-insurance-data.ts    # TypeScript version (alternative)
```

## 🔧 Data Generation Features

### Realistic Distributions

- **15 major insurance carriers** (Aetna, BCBS, Cigna, UnitedHealth, etc.)
- **5 claim statuses** with realistic probabilities
- **4 sync statuses** representing real-world PMS integration
- **12 medical specialties** for diverse provider types
- **Monetary amounts** ranging from $25 to $15,000
- **Recent dates** within the last 6 months

### Reproducible Results

- **Seeded random generation** (seed: 42) ensures consistency
- **Same dataset** generated across all builds
- **Version tracking** in metadata for data lineage

### Performance Optimized

- **Efficient generation** with progress indicators
- **JSON file output** optimized for Next.js import
- **Chunked processing** for large datasets

## 📚 Data Utility Functions

The `src/data/insurance-data.ts` file provides several utility functions:

```typescript
// Get all claims data
const allClaims = getAllClaims();

// Paginated data for tables
const { claims, totalPages, currentPage, totalItems } = 
  getPaginatedClaims(page, itemsPerPage);

// Dataset metadata
const metadata = getDatasetMetadata();

// Search and filter functions
const paidClaims = getClaimsByStatus('PAID');
const aetnaClaims = getClaimsByCarrier('Aetna');
const syncedClaims = getClaimsBySyncStatus('Synced');
const searchResults = searchClaims('John Doe');

// Statistical overview
const stats = getClaimsStats();
```

## 🎯 Usage Examples

### Basic Table Implementation

```tsx
import { getAllClaims } from '@/data/insurance-data';

export default function ClaimsTable() {
  const claims = getAllClaims();
  
  return (
    <DataTable columns={columns} data={claims} />
  );
}
```

### Paginated Dashboard

```tsx
import { getPaginatedClaims } from '@/data/insurance-data';

export default function Dashboard() {
  const [page, setPage] = useState(1);
  const { claims, totalPages } = getPaginatedClaims(page, 25);
  
  return (
    <div>
      <ClaimsTable data={claims} />
      <Pagination page={page} totalPages={totalPages} onChange={setPage} />
    </div>
  );
}
```

## 🏃‍♂️ Getting Started

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Generate data**:
   ```bash
   npm run generate:data
   ```

3. **Run development server**:
   ```bash
   npm run dev
   ```

4. **Build for production**:
   ```bash
   npm run build
   ```

## 🔍 Key Benefits Summary

| Aspect | Build-Time Generation | Runtime Generation |
|--------|---------------------|-------------------|
| **Performance** | ⚡ Instant load | 🐌 Loading time |
| **Consistency** | ✅ Same data always | ❌ Random variations |
| **Testing** | ✅ Predictable | ❌ Flaky tests |
| **Deployment** | ✅ Static hosting | ❌ Server required |
| **Caching** | ✅ Perfect caching | ❌ Complex caching |
| **Bundle Size** | ✅ Optimized | ❌ Runtime overhead |

This implementation showcases how **build-time data generation** with **Next.js** and **@faker-js/faker** creates a more performant, reliable, and maintainable insurance claims application.
