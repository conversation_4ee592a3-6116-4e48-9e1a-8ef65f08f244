/**
 * Test file to verify date formatting functionality
 * This can be run to ensure the date formatter works correctly
 */

import { formatDisplayDate } from '../src/utils/dateFormatter';

// Test cases for date formatting
const testCases = [
  { input: '01/15/2024', expected: 'January 15, 2024' },
  { input: '12/31/2023', expected: 'December 31, 2023' },
  { input: '07/04/2025', expected: 'July 4, 2025' },
  { input: '03/01/2024', expected: 'March 1, 2024' },
  { input: '', expected: '' },
  { input: 'invalid', expected: 'invalid' },
];

console.log('🧪 Testing Date Formatter\n');

testCases.forEach(({ input, expected }, index) => {
  const result = formatDisplayDate(input);
  const passed = result === expected;
  
  console.log(`Test ${index + 1}: ${passed ? '✅' : '❌'}`);
  console.log(`  Input: "${input}"`);
  console.log(`  Expected: "${expected}"`);
  console.log(`  Result: "${result}"`);
  console.log('');
});

console.log('Date formatting test completed!');
