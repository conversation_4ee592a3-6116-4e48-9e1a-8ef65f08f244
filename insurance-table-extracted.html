<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DNTEL Insurance Claims Dashboard</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        /* CSS Custom Properties for theming */
        :root {
            --color-primary-dark: #112A24;
            --color-primary-green: #1A6444;
            --color-secondary-green: #196443;
            --color-light-green: #E0FEEF;
            --color-gray-primary: #546661;
            --color-gray-secondary: #74827F;
            --color-gray-light: #B3B3B3;
            --color-gray-border: rgba(23, 83, 59, 0.04);
            --color-gray-badge: #EAEAEA;
            --color-gray-medium: #838580;
            --color-blue: #23A9EB;
            --color-blue-light: #EBF9FE;
            --color-white: #FFFFFF;
            --color-shadow: rgba(17, 42, 36, 0.12);
            --color-shadow-light: rgba(23, 83, 59, 0.12);
            
            /* Fonts */
            --font-primary: 'PolySans', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            --font-secondary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: var(--font-primary);
            background-color: #f5f7fa;
            color: var(--color-primary-dark);
            line-height: 1.4;
        }

        .container {
            max-width: 1280px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .page-header {
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 600;
            color: var(--color-primary-dark);
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: var(--color-gray-primary);
            font-size: 1rem;
        }

        /* Table Container */
        .table-container {
            background: var(--color-white);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0px 1px 1px var(--color-shadow);
            width: 100%;
            max-width: 1234px;
        }

        /* Table Header */
        .table-header {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            padding: 20px 24px;
            gap: 20px;
            background: var(--color-white);
            box-shadow: 0px 1px 1px var(--color-shadow);
            border-radius: 20px 20px 0px 0px;
            height: 72px;
        }

        .header-cell {
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            padding: 0px;
            gap: 10px;
            font-family: var(--font-primary);
            font-weight: 400;
            font-size: 14px;
            line-height: 14px;
            color: var(--color-gray-primary);
        }

        .header-patient { width: 72px; }
        .header-service-date { width: 92px; }
        .header-insurance { width: 121px; }
        .header-amount { width: 49px; }
        .header-status { width: 126px; }
        .header-updated { width: 92px; }
        .header-user { width: 30px; }
        .header-date-sent { width: 92px; }
        .header-date-orig { width: 91px; }
        .header-pms { width: 111px; }
        .header-provider { width: 91px; }

        /* Table Body */
        .table-body {
            background: var(--color-white);
        }

        /* Table Row */
        .table-row {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            padding: 20px 24px;
            gap: 20px;
            background: var(--color-white);
            border-bottom: 1px solid var(--color-gray-border);
            box-shadow: 0px 1px 1px var(--color-shadow-light);
            height: 96px;
            transition: background-color 0.2s ease;
        }

        .table-row:hover {
            background-color: #f8f9fa;
        }

        /* Patient Column */
        .cell-patient {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            padding: 0px;
            gap: 4px;
            width: 72px;
            height: 30px;
        }

        .patient-name {
            font-family: var(--font-primary);
            font-weight: 400;
            font-size: 14px;
            line-height: 14px;
            color: var(--color-primary-dark);
            width: 72px;
            height: 14px;
        }

        .patient-id {
            font-family: var(--font-primary);
            font-weight: 600;
            font-size: 12px;
            line-height: 12px;
            color: var(--color-gray-light);
            width: 72px;
            height: 12px;
        }

        /* Service Date Column */
        .cell-service-date {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            padding: 0px;
            gap: 10px;
            width: 92px;
            height: 14px;
        }

        .service-date {
            font-family: var(--font-primary);
            font-weight: 400;
            font-size: 14px;
            line-height: 14px;
            color: var(--color-primary-dark);
            width: 92px;
            height: 14px;
        }

        /* Insurance Carrier Column */
        .cell-insurance {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            padding: 0px;
            gap: 8px;
            width: 121px;
            height: 56px;
        }

        .insurance-name {
            font-family: var(--font-primary);
            font-weight: 400;
            font-size: 12px;
            line-height: 12px;
            color: var(--color-primary-dark);
            width: 121px;
            height: 24px;
        }

        .insurance-badge {
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            padding: 4px;
            gap: 10px;
            width: 121px;
            height: 24px;
            background: var(--color-blue-light);
            border-radius: 4px;
        }

        .insurance-badge-text {
            font-family: var(--font-primary);
            font-weight: 600;
            font-size: 12px;
            line-height: 16px;
            text-align: center;
            color: var(--color-blue);
            width: 121px;
            height: 16px;
        }

        /* Amount Column */
        .cell-amount {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            padding: 0px;
            gap: 10px;
            width: 49px;
            height: 12px;
        }

        .amount-text {
            font-family: var(--font-primary);
            font-weight: 400;
            font-size: 12px;
            line-height: 12px;
            color: var(--color-primary-dark);
            width: 49px;
            height: 12px;
        }

        /* Status Column */
        .cell-status {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            padding: 0px;
            gap: 10px;
            width: 126px;
            height: 12px;
        }

        .status-text {
            font-family: var(--font-primary);
            font-weight: 400;
            font-size: 12px;
            line-height: 12px;
            color: var(--color-primary-dark);
            width: 126px;
            height: 12px;
        }

        /* Last Updated Column */
        .cell-updated {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            padding: 0px;
            gap: 8px;
            width: 92px;
            height: 33px;
        }

        .updated-date {
            font-family: var(--font-primary);
            font-weight: 400;
            font-size: 14px;
            line-height: 14px;
            color: var(--color-primary-dark);
            width: 92px;
            height: 14px;
        }

        .updated-time {
            font-family: var(--font-primary);
            font-weight: 600;
            font-size: 11px;
            line-height: 11px;
            color: var(--color-gray-secondary);
            width: 92px;
            height: 11px;
        }

        /* User Column */
        .cell-user {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 7px 3px;
            gap: 10px;
            width: 30px;
            height: 28px;
            background: var(--color-light-green);
            border-radius: 14px;
        }

        .user-avatar {
            font-family: var(--font-primary);
            font-weight: 600;
            font-size: 14px;
            line-height: 14px;
            color: var(--color-secondary-green);
            width: 20px;
            height: 14px;
            text-align: center;
        }

        /* Date Sent Columns */
        .cell-date-sent,
        .cell-date-orig {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            padding: 0px;
            gap: 10px;
            height: 14px;
        }

        .cell-date-sent {
            width: 92px;
        }

        .cell-date-orig {
            width: 91px;
        }

        .date-sent,
        .date-orig {
            font-family: var(--font-primary);
            font-weight: 400;
            font-size: 14px;
            line-height: 14px;
            color: var(--color-primary-dark);
            height: 14px;
        }

        .date-sent {
            width: 92px;
        }

        .date-orig {
            width: 91px;
        }

        /* PMS Sync Status Column */
        .cell-pms {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            padding: 0px;
            gap: 4px;
            width: 111px;
            height: 44px;
        }

        .pms-badge {
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            padding: 2px 10px 2px 8px;
            gap: 6px;
            width: 111px;
            height: 24px;
            background: var(--color-gray-badge);
            border-radius: 4px;
        }

        .pms-icon {
            width: 15px;
            height: 15px;
        }

        .pms-status-text {
            font-family: var(--font-primary);
            font-weight: 600;
            font-size: 12px;
            line-height: 16px;
            text-align: center;
            color: var(--color-gray-medium);
            width: 64px;
            height: 16px;
        }

        .pms-modified {
            font-family: var(--font-secondary);
            font-weight: 500;
            font-size: 10px;
            line-height: 16px;
            text-align: center;
            color: var(--color-gray-primary);
            width: 111px;
            height: 16px;
        }

        /* Provider Column */
        .cell-provider {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            padding: 0px;
            gap: 4px;
            width: 91px;
            height: 34px;
        }

        .provider-name {
            font-family: var(--font-primary);
            font-weight: 400;
            font-size: 14px;
            line-height: 14px;
            color: var(--color-primary-dark);
            width: 84px;
            height: 14px;
        }

        .provider-id {
            font-family: var(--font-primary);
            font-weight: 600;
            font-size: 12px;
            line-height: 16px;
            text-align: center;
            color: var(--color-gray-light);
            width: 91px;
            height: 16px;
        }

        /* Pagination */
        .pagination {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            padding: 20px 24px;
            gap: 20px;
            background: var(--color-white);
            border-top: 1px solid var(--color-gray-border);
            height: 72px;
        }

        .pagination-left {
            display: flex;
            flex-direction: row;
            align-items: flex-end;
            padding: 0px;
            gap: 3px;
            width: 37px;
            height: 16px;
        }

        .pagination-number {
            font-family: var(--font-primary);
            font-weight: 400;
            font-size: 14px;
            line-height: 14px;
            color: var(--color-primary-green);
            width: 18px;
            height: 14px;
        }

        .pagination-rows-text {
            font-family: var(--font-primary);
            font-weight: 400;
            font-size: 14px;
            line-height: 17px;
            color: var(--color-gray-primary);
            width: 97px;
            height: 17px;
        }

        .pagination-center {
            display: flex;
            flex-direction: row;
            align-items: center;
            padding: 0px;
            gap: 13px;
            width: 246px;
            height: 34px;
        }

        .page-info {
            font-family: var(--font-primary);
            font-weight: 400;
            font-size: 14px;
            line-height: 14px;
            color: var(--color-gray-primary);
            width: 72px;
            height: 14px;
        }

        .pagination-controls {
            display: flex;
            flex-direction: row;
            align-items: center;
            padding: 0px;
            gap: 8px;
            width: 161px;
            height: 34px;
        }

        .pagination-button {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            padding: 6px 8px;
            gap: 10px;
            width: 34px;
            height: 34px;
            border: 1px solid rgba(23, 83, 59, 0.08);
            filter: drop-shadow(0px 1px 1px var(--color-shadow));
            border-radius: 10px;
            background: var(--color-white);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .pagination-button:hover:not(:disabled) {
            background-color: #f1f5f9;
            border-color: rgba(23, 83, 59, 0.12);
        }

        .pagination-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination-icon {
            width: 18px;
            height: 18px;
            color: var(--color-primary-green);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 0 0.5rem;
            }

            .table-container {
                overflow-x: auto;
            }

            .table-header,
            .table-row {
                min-width: 1200px;
            }

            .pagination {
                flex-direction: column;
                gap: 1rem;
                height: auto;
                padding: 1rem;
            }

            .pagination-center {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="page-header">
            <h1 class="page-title">DNTEL Insurance Claims Dashboard</h1>
            <p class="page-subtitle">Manage and track insurance claims with real-time status updates</p>
        </header>

        <div class="table-container">
            <!-- Table Header -->
            <div class="table-header">
                <div class="header-cell header-patient">Patient</div>
                <div class="header-cell header-service-date">Service Date</div>
                <div class="header-cell header-insurance">Insurance Carrier</div>
                <div class="header-cell header-amount">Amount</div>
                <div class="header-cell header-status">Status</div>
                <div class="header-cell header-updated">Last Updated</div>
                <div class="header-cell header-user">User</div>
                <div class="header-cell header-date-sent">Date Sent</div>
                <div class="header-cell header-date-orig">Date Sent Orig</div>
                <div class="header-cell header-pms">PMS Sync Status</div>
                <div class="header-cell header-provider">Provider</div>
            </div>

            <!-- Table Body -->
            <div class="table-body">
                <!-- Row 1 -->
                <div class="table-row">
                    <div class="cell-patient">
                        <div class="patient-name">First Last</div>
                        <div class="patient-id">ID: 11060</div>
                    </div>
                    <div class="cell-service-date">
                        <div class="service-date">July 00, 2025</div>
                    </div>
                    <div class="cell-insurance">
                        <div class="insurance-name">BCBS OF COLORADO FEP PPO INN</div>
                        <div class="insurance-badge">
                            <div class="insurance-badge-text">Primary</div>
                        </div>
                    </div>
                    <div class="cell-amount">
                        <div class="amount-text">$00,000</div>
                    </div>
                    <div class="cell-status">
                        <div class="status-text">NCOF - RESUBMITTED</div>
                    </div>
                    <div class="cell-updated">
                        <div class="updated-date">May 28, 2030</div>
                        <div class="updated-time">11:36 AM</div>
                    </div>
                    <div class="cell-user">
                        <div class="user-avatar">AA</div>
                    </div>
                    <div class="cell-date-sent">
                        <div class="date-sent">Aug 28, 2025</div>
                    </div>
                    <div class="cell-date-orig">
                        <div class="date-orig">Aug 28, 2025</div>
                    </div>
                    <div class="cell-pms">
                        <div class="pms-badge">
                            <svg class="pms-icon" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/>
                            </svg>
                            <div class="pms-status-text">Not synced</div>
                        </div>
                        <div class="pms-modified">Status modified today</div>
                    </div>
                    <div class="cell-provider">
                        <div class="provider-name">Dr. First Last</div>
                        <div class="provider-id">ID:56712349911</div>
                    </div>
                </div>

                <!-- Row 2 -->
                <div class="table-row">
                    <div class="cell-patient">
                        <div class="patient-name">John Smith</div>
                        <div class="patient-id">ID: 11061</div>
                    </div>
                    <div class="cell-service-date">
                        <div class="service-date">June 22, 2025</div>
                    </div>
                    <div class="cell-insurance">
                        <div class="insurance-name">AETNA PPO STANDARD</div>
                        <div class="insurance-badge">
                            <div class="insurance-badge-text">Primary</div>
                        </div>
                    </div>
                    <div class="cell-amount">
                        <div class="amount-text">$1,825</div>
                    </div>
                    <div class="cell-status">
                        <div class="status-text">PAID</div>
                    </div>
                    <div class="cell-updated">
                        <div class="updated-date">May 29, 2030</div>
                        <div class="updated-time">9:15 AM</div>
                    </div>
                    <div class="cell-user">
                        <div class="user-avatar">BB</div>
                    </div>
                    <div class="cell-date-sent">
                        <div class="date-sent">Aug 29, 2025</div>
                    </div>
                    <div class="cell-date-orig">
                        <div class="date-orig">Aug 29, 2025</div>
                    </div>
                    <div class="cell-pms">
                        <div class="pms-badge">
                            <svg class="pms-icon" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                            </svg>
                            <div class="pms-status-text">Synced</div>
                        </div>
                        <div class="pms-modified">Status modified yesterday</div>
                    </div>
                    <div class="cell-provider">
                        <div class="provider-name">Dr. Emily Davis</div>
                        <div class="provider-id">ID:56712349912</div>
                    </div>
                </div>

                <!-- Row 3 -->
                <div class="table-row">
                    <div class="cell-patient">
                        <div class="patient-name">Maria Rodriguez</div>
                        <div class="patient-id">ID: 11062</div>
                    </div>
                    <div class="cell-service-date">
                        <div class="service-date">July 10, 2025</div>
                    </div>
                    <div class="cell-insurance">
                        <div class="insurance-name">CIGNA HMO NETWORK</div>
                        <div class="insurance-badge">
                            <div class="insurance-badge-text">Secondary</div>
                        </div>
                    </div>
                    <div class="cell-amount">
                        <div class="amount-text">$3,200</div>
                    </div>
                    <div class="cell-status">
                        <div class="status-text">PENDING REVIEW</div>
                    </div>
                    <div class="cell-updated">
                        <div class="updated-date">May 30, 2030</div>
                        <div class="updated-time">2:45 PM</div>
                    </div>
                    <div class="cell-user">
                        <div class="user-avatar">CC</div>
                    </div>
                    <div class="cell-date-sent">
                        <div class="date-sent">Aug 30, 2025</div>
                    </div>
                    <div class="cell-date-orig">
                        <div class="date-orig">Aug 30, 2025</div>
                    </div>
                    <div class="cell-pms">
                        <div class="pms-badge">
                            <svg class="pms-icon" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/>
                            </svg>
                            <div class="pms-status-text">Not synced</div>
                        </div>
                        <div class="pms-modified">Status modified today</div>
                    </div>
                    <div class="cell-provider">
                        <div class="provider-name">Dr. Robert Chen</div>
                        <div class="provider-id">ID:56712349913</div>
                    </div>
                </div>

                <!-- Row 4 -->
                <div class="table-row">
                    <div class="cell-patient">
                        <div class="patient-name">David Thompson</div>
                        <div class="patient-id">ID: 11063</div>
                    </div>
                    <div class="cell-service-date">
                        <div class="service-date">August 5, 2025</div>
                    </div>
                    <div class="cell-insurance">
                        <div class="insurance-name">UNITED HEALTHCARE CHOICE</div>
                        <div class="insurance-badge">
                            <div class="insurance-badge-text">Primary</div>
                        </div>
                    </div>
                    <div class="cell-amount">
                        <div class="amount-text">$4,750</div>
                    </div>
                    <div class="cell-status">
                        <div class="status-text">DENIED</div>
                    </div>
                    <div class="cell-updated">
                        <div class="updated-date">June 1, 2030</div>
                        <div class="updated-time">4:20 PM</div>
                    </div>
                    <div class="cell-user">
                        <div class="user-avatar">DD</div>
                    </div>
                    <div class="cell-date-sent">
                        <div class="date-sent">Sep 1, 2025</div>
                    </div>
                    <div class="cell-date-orig">
                        <div class="date-orig">Sep 1, 2025</div>
                    </div>
                    <div class="cell-pms">
                        <div class="pms-badge">
                            <svg class="pms-icon" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                            </svg>
                            <div class="pms-status-text">Synced</div>
                        </div>
                        <div class="pms-modified">Status modified 2 days ago</div>
                    </div>
                    <div class="cell-provider">
                        <div class="provider-name">Dr. Jennifer Wilson</div>
                        <div class="provider-id">ID:56712349914</div>
                    </div>
                </div>

                <!-- Row 5 -->
                <div class="table-row">
                    <div class="cell-patient">
                        <div class="patient-name">Lisa Williams</div>
                        <div class="patient-id">ID: 11064</div>
                    </div>
                    <div class="cell-service-date">
                        <div class="service-date">September 12, 2025</div>
                    </div>
                    <div class="cell-insurance">
                        <div class="insurance-name">HUMANA PPO ADVANTAGE</div>
                        <div class="insurance-badge">
                            <div class="insurance-badge-text">Primary</div>
                        </div>
                    </div>
                    <div class="cell-amount">
                        <div class="amount-text">$1,950</div>
                    </div>
                    <div class="cell-status">
                        <div class="status-text">APPROVED</div>
                    </div>
                    <div class="cell-updated">
                        <div class="updated-date">June 2, 2030</div>
                        <div class="updated-time">10:30 AM</div>
                    </div>
                    <div class="cell-user">
                        <div class="user-avatar">EE</div>
                    </div>
                    <div class="cell-date-sent">
                        <div class="date-sent">Sep 2, 2025</div>
                    </div>
                    <div class="cell-date-orig">
                        <div class="date-orig">Sep 2, 2025</div>
                    </div>
                    <div class="cell-pms">
                        <div class="pms-badge">
                            <svg class="pms-icon" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/>
                            </svg>
                            <div class="pms-status-text">Not synced</div>
                        </div>
                        <div class="pms-modified">Status modified today</div>
                    </div>
                    <div class="cell-provider">
                        <div class="provider-name">Dr. Kevin Martinez</div>
                        <div class="provider-id">ID:56712349915</div>
                    </div>
                </div>

                <!-- Row 6 -->
                <div class="table-row">
                    <div class="cell-patient">
                        <div class="patient-name">Robert Taylor</div>
                        <div class="patient-id">ID: 11065</div>
                    </div>
                    <div class="cell-service-date">
                        <div class="service-date">October 8, 2025</div>
                    </div>
                    <div class="cell-insurance">
                        <div class="insurance-name">KAISER PERMANENTE HMO</div>
                        <div class="insurance-badge">
                            <div class="insurance-badge-text">Primary</div>
                        </div>
                    </div>
                    <div class="cell-amount">
                        <div class="amount-text">$5,120</div>
                    </div>
                    <div class="cell-status">
                        <div class="status-text">IN REVIEW</div>
                    </div>
                    <div class="cell-updated">
                        <div class="updated-date">June 3, 2030</div>
                        <div class="updated-time">3:15 PM</div>
                    </div>
                    <div class="cell-user">
                        <div class="user-avatar">FF</div>
                    </div>
                    <div class="cell-date-sent">
                        <div class="date-sent">Sep 3, 2025</div>
                    </div>
                    <div class="cell-date-orig">
                        <div class="date-orig">Sep 3, 2025</div>
                    </div>
                    <div class="cell-pms">
                        <div class="pms-badge">
                            <svg class="pms-icon" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                            </svg>
                            <div class="pms-status-text">Synced</div>
                        </div>
                        <div class="pms-modified">Status modified yesterday</div>
                    </div>
                    <div class="cell-provider">
                        <div class="provider-name">Dr. Amanda Brown</div>
                        <div class="provider-id">ID:56712349916</div>
                    </div>
                </div>

                <!-- Row 7 -->
                <div class="table-row">
                    <div class="cell-patient">
                        <div class="patient-name">Jennifer White</div>
                        <div class="patient-id">ID: 11066</div>
                    </div>
                    <div class="cell-service-date">
                        <div class="service-date">November 3, 2025</div>
                    </div>
                    <div class="cell-insurance">
                        <div class="insurance-name">ANTHEM BCBS PPO</div>
                        <div class="insurance-badge">
                            <div class="insurance-badge-text">Secondary</div>
                        </div>
                    </div>
                    <div class="cell-amount">
                        <div class="amount-text">$890</div>
                    </div>
                    <div class="cell-status">
                        <div class="status-text">RESUBMITTED</div>
                    </div>
                    <div class="cell-updated">
                        <div class="updated-date">June 4, 2030</div>
                        <div class="updated-time">8:45 AM</div>
                    </div>
                    <div class="cell-user">
                        <div class="user-avatar">GG</div>
                    </div>
                    <div class="cell-date-sent">
                        <div class="date-sent">Sep 4, 2025</div>
                    </div>
                    <div class="cell-date-orig">
                        <div class="date-orig">Sep 4, 2025</div>
                    </div>
                    <div class="cell-pms">
                        <div class="pms-badge">
                            <svg class="pms-icon" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/>
                            </svg>
                            <div class="pms-status-text">Not synced</div>
                        </div>
                        <div class="pms-modified">Status modified today</div>
                    </div>
                    <div class="cell-provider">
                        <div class="provider-name">Dr. Christopher Lee</div>
                        <div class="provider-id">ID:56712349917</div>
                    </div>
                </div>
            </div>

            <!-- Pagination -->
            <div class="pagination">
                <div class="pagination-left">
                    <div class="pagination-number">20</div>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M8 4L6 6L8 8L10 6L8 4Z" fill="#1A6444"/>
                        <path d="M8 8L6 10L8 12L10 10L8 8Z" fill="#1A6444"/>
                    </svg>
                </div>
                <div class="pagination-rows-text">Rows per page</div>
                
                <div class="pagination-center">
                    <div class="page-info">Page 1 of 8</div>
                    <div class="pagination-controls">
                        <button class="pagination-button" disabled>
                            <svg class="pagination-icon" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6 1.41-1.41z"/>
                                <path d="M6 6h2v12H6z"/>
                            </svg>
                        </button>
                        <button class="pagination-button" disabled>
                            <svg class="pagination-icon" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"/>
                            </svg>
                        </button>
                        <button class="pagination-button">
                            <svg class="pagination-icon" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M8.59 16.59L13.17 12L8.59 7.41L10 6l6 6-6 6-1.41-1.41z"/>
                            </svg>
                        </button>
                        <button class="pagination-button">
                            <svg class="pagination-icon" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6-1.41 1.41z"/>
                                <path d="M16 6h2v12h-2z"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
