import { InsuranceRecord } from '@/types/insurance';

export const mockInsuranceData: InsuranceRecord[] = [
  {
    id: '1',
    patient: { name: '<PERSON>', id: 'ID: 11060' },
    serviceDate: 'July 15, 2025',
    insuranceCarrier: { name: 'BCBS OF COLORADO FEP PPO INN', type: 'Primary' },
    amount: '$2,450',
    status: 'NCOF - RESUBMITTED',
    lastUpdated: { date: 'May 28, 2030', time: '11:36 AM' },
    user: 'AA',
    dateSent: 'Aug 28, 2025',
    dateSentOrig: 'Aug 28, 2025',
    pmsSync: { 
      status: 'Not synced', 
      statusText: 'Status modified today',
      isModifiedToday: true
    },
    provider: { name: 'Dr. <PERSON>', id: 'ID:56712349911' }
  },
  {
    id: '2',
    patient: { name: '<PERSON>', id: 'ID: 11061' },
    serviceDate: 'June 22, 2025',
    insuranceCarrier: { name: 'AETNA PPO STANDARD', type: 'Primary' },
    amount: '$1,825',
    status: 'PAID',
    lastUpdated: { date: 'May 29, 2030', time: '9:15 AM' },
    user: 'BB',
    dateSent: 'Aug 29, 2025',
    dateSentOrig: 'Aug 29, 2025',
    pmsSync: { 
      status: 'Synced', 
      statusText: 'Status modified yesterday',
      isModifiedToday: false
    },
    provider: { name: 'Dr. Emily Davis', id: 'ID:56712349912' }
  },
  {
    id: '3',
    patient: { name: 'Maria Rodriguez', id: 'ID: 11062' },
    serviceDate: 'July 10, 2025',
    insuranceCarrier: { name: 'CIGNA HMO NETWORK', type: 'Secondary' },
    amount: '$3,200',
    status: 'PENDING REVIEW',
    lastUpdated: { date: 'May 30, 2030', time: '2:45 PM' },
    user: 'CC',
    dateSent: 'Aug 30, 2025',
    dateSentOrig: 'Aug 30, 2025',
    pmsSync: { 
      status: 'Not synced', 
      statusText: 'Status modified today',
      isModifiedToday: true
    },
    provider: { name: 'Dr. Robert Chen', id: 'ID:56712349913' }
  },
  {
    id: '4',
    patient: { name: 'David Thompson', id: 'ID: 11063' },
    serviceDate: 'August 5, 2025',
    insuranceCarrier: { name: 'UNITED HEALTHCARE CHOICE', type: 'Primary' },
    amount: '$4,750',
    status: 'DENIED',
    lastUpdated: { date: 'June 1, 2030', time: '4:20 PM' },
    user: 'DD',
    dateSent: 'Sep 1, 2025',
    dateSentOrig: 'Sep 1, 2025',
    pmsSync: { 
      status: 'Synced', 
      statusText: 'Status modified 2 days ago',
      isModifiedToday: false
    },
    provider: { name: 'Dr. Jennifer Wilson', id: 'ID:56712349914' }
  },
  {
    id: '5',
    patient: { name: 'Lisa Williams', id: 'ID: 11064' },
    serviceDate: 'September 12, 2025',
    insuranceCarrier: { name: 'HUMANA PPO ADVANTAGE', type: 'Primary' },
    amount: '$1,950',
    status: 'APPROVED',
    lastUpdated: { date: 'June 2, 2030', time: '10:30 AM' },
    user: 'EE',
    dateSent: 'Sep 2, 2025',
    dateSentOrig: 'Sep 2, 2025',
    pmsSync: { 
      status: 'Not synced', 
      statusText: 'Status modified today',
      isModifiedToday: true
    },
    provider: { name: 'Dr. Kevin Martinez', id: 'ID:56712349915' }
  },
  {
    id: '6',
    patient: { name: 'Robert Taylor', id: 'ID: 11065' },
    serviceDate: 'October 8, 2025',
    insuranceCarrier: { name: 'KAISER PERMANENTE HMO', type: 'Primary' },
    amount: '$5,120',
    status: 'IN REVIEW',
    lastUpdated: { date: 'June 3, 2030', time: '3:15 PM' },
    user: 'FF',
    dateSent: 'Sep 3, 2025',
    dateSentOrig: 'Sep 3, 2025',
    pmsSync: { 
      status: 'Synced', 
      statusText: 'Status modified yesterday',
      isModifiedToday: false
    },
    provider: { name: 'Dr. Amanda Brown', id: 'ID:56712349916' }
  },
  {
    id: '7',
    patient: { name: 'Jennifer White', id: 'ID: 11066' },
    serviceDate: 'November 3, 2025',
    insuranceCarrier: { name: 'ANTHEM BCBS PPO', type: 'Secondary' },
    amount: '$890',
    status: 'RESUBMITTED',
    lastUpdated: { date: 'June 4, 2030', time: '8:45 AM' },
    user: 'GG',
    dateSent: 'Sep 4, 2025',
    dateSentOrig: 'Sep 4, 2025',
    pmsSync: { 
      status: 'Not synced', 
      statusText: 'Status modified today',
      isModifiedToday: true
    },
    provider: { name: 'Dr. Christopher Lee', id: 'ID:56712349917' }
  }
];
