{"metadata": {"generatedAt": "2025-09-02T13:56:18.695Z", "totalRecords": 300, "fakerSeed": 42, "version": "1.0.0"}, "claims": [{"id": "CLAIM-000001", "patient": {"name": "<PERSON>", "id": "PAT-879558"}, "serviceDate": "08/25/2025", "insuranceCarrier": {"carrierName": "State Farm", "planCategory": "Secondary"}, "amount": "$333.25", "status": "PROCESSING", "lastUpdated": {"date": "08/25/2025", "time": "3:10 AM"}, "user": "EK", "dateSent": "06/25/2025", "dateSentOrig": "04/28/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Neurology Associates", "id": "PROV-5104"}}, {"id": "CLAIM-000002", "patient": {"name": "<PERSON>", "id": "PAT-158546"}, "serviceDate": "04/11/2025", "insuranceCarrier": {"carrierName": "Farmers", "planCategory": "Secondary"}, "amount": "$12130.75", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "07/08/2025", "time": "6:07 AM"}, "user": "AG", "dateSent": "05/01/2025", "dateSentOrig": "06/13/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Dermatology Associates", "id": "PROV-9726"}}, {"id": "CLAIM-000003", "patient": {"name": "<PERSON>", "id": "PAT-140704"}, "serviceDate": "08/22/2025", "insuranceCarrier": {"carrierName": "UnitedHealth", "planCategory": "Primary"}, "amount": "$4088.45", "status": "PROCESSING", "lastUpdated": {"date": "04/26/2025", "time": "7:08 PM"}, "user": "AR", "dateSent": "07/31/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Emergency Medicine Associates", "id": "PROV-7941"}}, {"id": "CLAIM-000004", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-379884"}, "serviceDate": "05/10/2025", "insuranceCarrier": {"carrierName": "UnitedHealth", "planCategory": "Secondary"}, "amount": "$9572.42", "status": "PROCESSING", "lastUpdated": {"date": "03/28/2025", "time": "9:45 PM"}, "user": "OL", "dateSent": "03/11/2025", "dateSentOrig": "03/12/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Radiology Associates", "id": "PROV-3829"}}, {"id": "CLAIM-000005", "patient": {"name": "<PERSON>", "id": "PAT-360776"}, "serviceDate": "08/17/2025", "insuranceCarrier": {"carrierName": "Blue Cross Blue Shield", "planCategory": "Secondary"}, "amount": "$12126.61", "status": "DENIED", "lastUpdated": {"date": "08/17/2025", "time": "10:11 PM"}, "user": "KT", "dateSent": "03/26/2025", "dateSentOrig": "05/22/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Surgery Associates", "id": "PROV-8746"}}, {"id": "CLAIM-000006", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-566911"}, "serviceDate": "06/06/2025", "insuranceCarrier": {"carrierName": "Progressive", "planCategory": "Primary"}, "amount": "$14577.44", "status": "PROCESSING", "lastUpdated": {"date": "06/04/2025", "time": "4:17 AM"}, "user": "LB", "dateSent": "08/17/2025", "dateSentOrig": "04/01/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Oncology Associates", "id": "PROV-9870"}}, {"id": "CLAIM-000007", "patient": {"name": "<PERSON>", "id": "PAT-670176"}, "serviceDate": "07/05/2025", "insuranceCarrier": {"carrierName": "Allstate", "planCategory": "Primary"}, "amount": "$12533.66", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "03/14/2025", "time": "8:40 AM"}, "user": "JO", "dateSent": "07/09/2025", "dateSentOrig": "08/22/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Cardiology Associates", "id": "PROV-4069"}}, {"id": "CLAIM-000008", "patient": {"name": "<PERSON>", "id": "PAT-576685"}, "serviceDate": "08/20/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Primary"}, "amount": "$13460.81", "status": "PROCESSING", "lastUpdated": {"date": "05/06/2025", "time": "5:43 PM"}, "user": "SN", "dateSent": "04/04/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Radiology Associates", "id": "PROV-1082"}}, {"id": "CLAIM-000009", "patient": {"name": "<PERSON>", "id": "PAT-301842"}, "serviceDate": "07/04/2025", "insuranceCarrier": {"carrierName": "Progressive", "planCategory": "Primary"}, "amount": "$4897.86", "status": "DENIED", "lastUpdated": {"date": "08/06/2025", "time": "8:34 AM"}, "user": "FF", "dateSent": "", "dateSentOrig": "08/14/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Radiology Associates", "id": "PROV-8153"}}, {"id": "CLAIM-000010", "patient": {"name": "<PERSON>", "id": "PAT-680925"}, "serviceDate": "06/18/2025", "insuranceCarrier": {"carrierName": "Blue Cross Blue Shield", "planCategory": "Secondary"}, "amount": "$14310.08", "status": "PROCESSING", "lastUpdated": {"date": "03/09/2025", "time": "12:25 PM"}, "user": "VG", "dateSent": "08/07/2025", "dateSentOrig": "04/06/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Psychiatry Associates", "id": "PROV-9425"}}, {"id": "CLAIM-000011", "patient": {"name": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "id": "PAT-727314"}, "serviceDate": "06/17/2025", "insuranceCarrier": {"carrierName": "Progressive", "planCategory": "Primary"}, "amount": "$4421.54", "status": "PROCESSING", "lastUpdated": {"date": "06/17/2025", "time": "11:54 PM"}, "user": "JO", "dateSent": "", "dateSentOrig": "", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Pediatrics Associates", "id": "PROV-4041"}}, {"id": "CLAIM-000012", "patient": {"name": "<PERSON><PERSON><PERSON>", "id": "PAT-631749"}, "serviceDate": "03/23/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Primary"}, "amount": "$12343.45", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "06/08/2025", "time": "10:12 PM"}, "user": "AL", "dateSent": "06/29/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "OB/GYN Associates", "id": "PROV-5646"}}, {"id": "CLAIM-000013", "patient": {"name": "<PERSON>", "id": "PAT-726376"}, "serviceDate": "07/28/2025", "insuranceCarrier": {"carrierName": "Kaiser Permanente", "planCategory": "Primary"}, "amount": "$2367.64", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "07/13/2025", "time": "8:16 PM"}, "user": "MM", "dateSent": "04/20/2025", "dateSentOrig": "07/21/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Primary Care Associates", "id": "PROV-2044"}}, {"id": "CLAIM-000014", "patient": {"name": "<PERSON>", "id": "PAT-526124"}, "serviceDate": "03/14/2025", "insuranceCarrier": {"carrierName": "Blue Cross Blue Shield", "planCategory": "Primary"}, "amount": "$5992.61", "status": "DENIED", "lastUpdated": {"date": "03/15/2025", "time": "5:37 PM"}, "user": "RC", "dateSent": "06/30/2025", "dateSentOrig": "06/20/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "OB/GYN Associates", "id": "PROV-6179"}}, {"id": "CLAIM-000015", "patient": {"name": "<PERSON><PERSON><PERSON>", "id": "PAT-276212"}, "serviceDate": "06/30/2025", "insuranceCarrier": {"carrierName": "Anthem", "planCategory": "Primary"}, "amount": "$297.87", "status": "PAID", "lastUpdated": {"date": "03/19/2025", "time": "4:50 AM"}, "user": "RC", "dateSent": "06/28/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Emergency Medicine Associates", "id": "PROV-8231"}}, {"id": "CLAIM-000016", "patient": {"name": "Dr. <PERSON><PERSON><PERSON><PERSON><PERSON>", "id": "PAT-937681"}, "serviceDate": "04/07/2025", "insuranceCarrier": {"carrierName": "Liberty Mutual", "planCategory": "Primary"}, "amount": "$11269.30", "status": "DENIED", "lastUpdated": {"date": "08/16/2025", "time": "7:49 AM"}, "user": "SA", "dateSent": "", "dateSentOrig": "05/03/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "OB/GYN Associates", "id": "PROV-9555"}}, {"id": "CLAIM-000017", "patient": {"name": "<PERSON><PERSON><PERSON>", "id": "PAT-812421"}, "serviceDate": "06/28/2025", "insuranceCarrier": {"carrierName": "GEICO", "planCategory": "Primary"}, "amount": "$7428.94", "status": "PAID", "lastUpdated": {"date": "05/25/2025", "time": "11:21 AM"}, "user": "BM", "dateSent": "03/22/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Primary Care Associates", "id": "PROV-8396"}}, {"id": "CLAIM-000018", "patient": {"name": "Ms. <PERSON>", "id": "PAT-987400"}, "serviceDate": "03/21/2025", "insuranceCarrier": {"carrierName": "GEICO", "planCategory": "Primary"}, "amount": "$1275.42", "status": "DENIED", "lastUpdated": {"date": "05/22/2025", "time": "11:06 AM"}, "user": "AB", "dateSent": "03/28/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Emergency Medicine Associates", "id": "PROV-6250"}}, {"id": "CLAIM-000019", "patient": {"name": "<PERSON>", "id": "PAT-972890"}, "serviceDate": "05/13/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Secondary"}, "amount": "$7927.32", "status": "PROCESSING", "lastUpdated": {"date": "06/14/2025", "time": "12:31 PM"}, "user": "MM", "dateSent": "08/16/2025", "dateSentOrig": "04/26/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "OB/GYN Associates", "id": "PROV-9012"}}, {"id": "CLAIM-000020", "patient": {"name": "<PERSON>", "id": "PAT-169961"}, "serviceDate": "06/26/2025", "insuranceCarrier": {"carrierName": "Farmers", "planCategory": "Secondary"}, "amount": "$10479.97", "status": "PENDING", "lastUpdated": {"date": "07/31/2025", "time": "9:09 PM"}, "user": "RR", "dateSent": "05/21/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Pediatrics Associates", "id": "PROV-1406"}}, {"id": "CLAIM-000021", "patient": {"name": "<PERSON>", "id": "PAT-972922"}, "serviceDate": "05/13/2025", "insuranceCarrier": {"carrierName": "Liberty Mutual", "planCategory": "Secondary"}, "amount": "$7043.68", "status": "PENDING", "lastUpdated": {"date": "03/17/2025", "time": "11:48 PM"}, "user": "ZR", "dateSent": "", "dateSentOrig": "", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Dermatology Associates", "id": "PROV-5054"}}, {"id": "CLAIM-000022", "patient": {"name": "<PERSON>", "id": "PAT-202201"}, "serviceDate": "08/25/2025", "insuranceCarrier": {"carrierName": "Progressive", "planCategory": "Secondary"}, "amount": "$11590.47", "status": "PENDING", "lastUpdated": {"date": "08/25/2025", "time": "7:33 PM"}, "user": "GB", "dateSent": "", "dateSentOrig": "", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Emergency Medicine Associates", "id": "PROV-2916"}}, {"id": "CLAIM-000023", "patient": {"name": "Lora Kertzmann II", "id": "PAT-562590"}, "serviceDate": "03/09/2025", "insuranceCarrier": {"carrierName": "GEICO", "planCategory": "Primary"}, "amount": "$9340.75", "status": "PROCESSING", "lastUpdated": {"date": "03/09/2025", "time": "2:55 AM"}, "user": "DW", "dateSent": "05/05/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Dermatology Associates", "id": "PROV-1682"}}, {"id": "CLAIM-000024", "patient": {"name": "<PERSON>", "id": "PAT-907109"}, "serviceDate": "03/29/2025", "insuranceCarrier": {"carrierName": "MetLife", "planCategory": "Secondary"}, "amount": "$2605.49", "status": "PAID", "lastUpdated": {"date": "04/06/2025", "time": "4:10 AM"}, "user": "BD", "dateSent": "06/05/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Primary Care Associates", "id": "PROV-8194"}}, {"id": "CLAIM-000025", "patient": {"name": "Mr. <PERSON>", "id": "PAT-266068"}, "serviceDate": "03/21/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Primary"}, "amount": "$7280.73", "status": "DENIED", "lastUpdated": {"date": "05/29/2025", "time": "9:02 AM"}, "user": "ML", "dateSent": "03/26/2025", "dateSentOrig": "06/10/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Dermatology Associates", "id": "PROV-3423"}}, {"id": "CLAIM-000026", "patient": {"name": "<PERSON>", "id": "PAT-711192"}, "serviceDate": "03/10/2025", "insuranceCarrier": {"carrierName": "GEICO", "planCategory": "Primary"}, "amount": "$1326.63", "status": "PENDING", "lastUpdated": {"date": "07/19/2025", "time": "6:07 AM"}, "user": "FM", "dateSent": "08/31/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Dermatology Associates", "id": "PROV-1916"}}, {"id": "CLAIM-000027", "patient": {"name": "<PERSON>.", "id": "PAT-572060"}, "serviceDate": "04/20/2025", "insuranceCarrier": {"carrierName": "Kaiser Permanente", "planCategory": "Secondary"}, "amount": "$1702.78", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "04/20/2025", "time": "11:49 AM"}, "user": "CW", "dateSent": "06/17/2025", "dateSentOrig": "07/23/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Dermatology Associates", "id": "PROV-3913"}}, {"id": "CLAIM-000028", "patient": {"name": "<PERSON>", "id": "PAT-238926"}, "serviceDate": "06/06/2025", "insuranceCarrier": {"carrierName": "MetLife", "planCategory": "Secondary"}, "amount": "$801.05", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "03/18/2025", "time": "12:19 PM"}, "user": "DR", "dateSent": "05/30/2025", "dateSentOrig": "05/08/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "OB/GYN Associates", "id": "PROV-8475"}}, {"id": "CLAIM-000029", "patient": {"name": "<PERSON>", "id": "PAT-617025"}, "serviceDate": "03/29/2025", "insuranceCarrier": {"carrierName": "Liberty Mutual", "planCategory": "Primary"}, "amount": "$11934.13", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "04/05/2025", "time": "10:39 PM"}, "user": "FH", "dateSent": "", "dateSentOrig": "05/13/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Oncology Associates", "id": "PROV-3712"}}, {"id": "CLAIM-000030", "patient": {"name": "Stella Little V", "id": "PAT-205208"}, "serviceDate": "06/05/2025", "insuranceCarrier": {"carrierName": "Farmers", "planCategory": "Secondary"}, "amount": "$5040.21", "status": "PAID", "lastUpdated": {"date": "06/26/2025", "time": "7:53 PM"}, "user": "CF", "dateSent": "", "dateSentOrig": "06/17/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Surgery Associates", "id": "PROV-8890"}}, {"id": "CLAIM-000031", "patient": {"name": "Dr. <PERSON>", "id": "PAT-306326"}, "serviceDate": "08/01/2025", "insuranceCarrier": {"carrierName": "Progressive", "planCategory": "Secondary"}, "amount": "$9626.19", "status": "DENIED", "lastUpdated": {"date": "04/21/2025", "time": "5:10 PM"}, "user": "KK", "dateSent": "", "dateSentOrig": "06/20/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Psychiatry Associates", "id": "PROV-6503"}}, {"id": "CLAIM-000032", "patient": {"name": "<PERSON>", "id": "PAT-237285"}, "serviceDate": "08/10/2025", "insuranceCarrier": {"carrierName": "Allstate", "planCategory": "Secondary"}, "amount": "$6376.36", "status": "DENIED", "lastUpdated": {"date": "08/10/2025", "time": "12:27 AM"}, "user": "WC", "dateSent": "", "dateSentOrig": "", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Psychiatry Associates", "id": "PROV-6321"}}, {"id": "CLAIM-000033", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-583656"}, "serviceDate": "03/16/2025", "insuranceCarrier": {"carrierName": "Nationwide", "planCategory": "Primary"}, "amount": "$5220.62", "status": "DENIED", "lastUpdated": {"date": "04/16/2025", "time": "6:08 AM"}, "user": "JV", "dateSent": "06/19/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Primary Care Associates", "id": "PROV-6971"}}, {"id": "CLAIM-000034", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-635846"}, "serviceDate": "08/26/2025", "insuranceCarrier": {"carrierName": "Anthem", "planCategory": "Secondary"}, "amount": "$3168.33", "status": "PROCESSING", "lastUpdated": {"date": "04/10/2025", "time": "1:28 PM"}, "user": "AK", "dateSent": "05/25/2025", "dateSentOrig": "06/15/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Cardiology Associates", "id": "PROV-2637"}}, {"id": "CLAIM-000035", "patient": {"name": "Elsa <PERSON>", "id": "PAT-240537"}, "serviceDate": "08/24/2025", "insuranceCarrier": {"carrierName": "Progressive", "planCategory": "Secondary"}, "amount": "$430.76", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "07/05/2025", "time": "1:06 PM"}, "user": "CF", "dateSent": "04/19/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Pediatrics Associates", "id": "PROV-8471"}}, {"id": "CLAIM-000036", "patient": {"name": "<PERSON><PERSON><PERSON>", "id": "PAT-287097"}, "serviceDate": "07/05/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Primary"}, "amount": "$8756.05", "status": "PENDING", "lastUpdated": {"date": "07/05/2025", "time": "10:20 AM"}, "user": "GG", "dateSent": "05/19/2025", "dateSentOrig": "05/24/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Orthopedics Associates", "id": "PROV-9536"}}, {"id": "CLAIM-000037", "patient": {"name": "<PERSON>", "id": "PAT-120222"}, "serviceDate": "04/01/2025", "insuranceCarrier": {"carrierName": "UnitedHealth", "planCategory": "Secondary"}, "amount": "$9511.34", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "08/04/2025", "time": "12:31 AM"}, "user": "DV", "dateSent": "06/18/2025", "dateSentOrig": "06/14/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Radiology Associates", "id": "PROV-8467"}}, {"id": "CLAIM-000038", "patient": {"name": "<PERSON>", "id": "PAT-698533"}, "serviceDate": "03/08/2025", "insuranceCarrier": {"carrierName": "Blue Cross Blue Shield", "planCategory": "Secondary"}, "amount": "$6296.09", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "03/15/2025", "time": "2:44 AM"}, "user": "LH", "dateSent": "05/09/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Orthopedics Associates", "id": "PROV-6097"}}, {"id": "CLAIM-000039", "patient": {"name": "<PERSON>", "id": "PAT-635570"}, "serviceDate": "07/04/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Primary"}, "amount": "$8962.72", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "03/26/2025", "time": "1:43 AM"}, "user": "MF", "dateSent": "", "dateSentOrig": "", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Emergency Medicine Associates", "id": "PROV-3449"}}, {"id": "CLAIM-000040", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-266619"}, "serviceDate": "05/10/2025", "insuranceCarrier": {"carrierName": "Allstate", "planCategory": "Secondary"}, "amount": "$10990.07", "status": "PROCESSING", "lastUpdated": {"date": "07/09/2025", "time": "11:14 AM"}, "user": "DW", "dateSent": "", "dateSentOrig": "", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Dermatology Associates", "id": "PROV-6111"}}, {"id": "CLAIM-000041", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-875838"}, "serviceDate": "03/12/2025", "insuranceCarrier": {"carrierName": "Liberty Mutual", "planCategory": "Primary"}, "amount": "$12438.01", "status": "PAID", "lastUpdated": {"date": "04/17/2025", "time": "2:04 PM"}, "user": "EB", "dateSent": "06/11/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Orthopedics Associates", "id": "PROV-6633"}}, {"id": "CLAIM-000042", "patient": {"name": "Mrs. <PERSON>", "id": "PAT-945160"}, "serviceDate": "06/25/2025", "insuranceCarrier": {"carrierName": "GEICO", "planCategory": "Secondary"}, "amount": "$5276.91", "status": "DENIED", "lastUpdated": {"date": "06/01/2025", "time": "8:52 PM"}, "user": "OJ", "dateSent": "", "dateSentOrig": "03/26/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Neurology Associates", "id": "PROV-3585"}}, {"id": "CLAIM-000043", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-296428"}, "serviceDate": "04/17/2025", "insuranceCarrier": {"carrierName": "Farmers", "planCategory": "Secondary"}, "amount": "$1363.93", "status": "PENDING", "lastUpdated": {"date": "04/17/2025", "time": "12:28 PM"}, "user": "CF", "dateSent": "06/24/2025", "dateSentOrig": "04/17/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Dermatology Associates", "id": "PROV-2984"}}, {"id": "CLAIM-000044", "patient": {"name": "<PERSON>. <PERSON>-<PERSON>", "id": "PAT-782414"}, "serviceDate": "07/25/2025", "insuranceCarrier": {"carrierName": "Progressive", "planCategory": "Primary"}, "amount": "$12242.10", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "06/11/2025", "time": "8:34 AM"}, "user": "SC", "dateSent": "", "dateSentOrig": "", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Pediatrics Associates", "id": "PROV-8285"}}, {"id": "CLAIM-000045", "patient": {"name": "<PERSON>", "id": "PAT-546288"}, "serviceDate": "06/14/2025", "insuranceCarrier": {"carrierName": "Anthem", "planCategory": "Primary"}, "amount": "$10256.80", "status": "PAID", "lastUpdated": {"date": "06/14/2025", "time": "6:28 PM"}, "user": "RP", "dateSent": "04/23/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Surgery Associates", "id": "PROV-6926"}}, {"id": "CLAIM-000046", "patient": {"name": "<PERSON>", "id": "PAT-777444"}, "serviceDate": "08/09/2025", "insuranceCarrier": {"carrierName": "Blue Cross Blue Shield", "planCategory": "Secondary"}, "amount": "$13080.86", "status": "PAID", "lastUpdated": {"date": "08/09/2025", "time": "2:20 PM"}, "user": "CS", "dateSent": "03/08/2025", "dateSentOrig": "06/25/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "OB/GYN Associates", "id": "PROV-6686"}}, {"id": "CLAIM-000047", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-966692"}, "serviceDate": "06/28/2025", "insuranceCarrier": {"carrierName": "Humana", "planCategory": "Secondary"}, "amount": "$13982.13", "status": "PAID", "lastUpdated": {"date": "06/28/2025", "time": "9:04 AM"}, "user": "MM", "dateSent": "06/26/2025", "dateSentOrig": "08/10/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "OB/GYN Associates", "id": "PROV-9719"}}, {"id": "CLAIM-000048", "patient": {"name": "<PERSON>", "id": "PAT-793366"}, "serviceDate": "03/30/2025", "insuranceCarrier": {"carrierName": "Progressive", "planCategory": "Primary"}, "amount": "$4122.56", "status": "PROCESSING", "lastUpdated": {"date": "05/27/2025", "time": "2:47 PM"}, "user": "DP", "dateSent": "04/25/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Cardiology Associates", "id": "PROV-4891"}}, {"id": "CLAIM-000049", "patient": {"name": "<PERSON>", "id": "PAT-904689"}, "serviceDate": "05/21/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Primary"}, "amount": "$491.23", "status": "DENIED", "lastUpdated": {"date": "08/09/2025", "time": "6:58 AM"}, "user": "SR", "dateSent": "", "dateSentOrig": "", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Radiology Associates", "id": "PROV-2181"}}, {"id": "CLAIM-000050", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-717008"}, "serviceDate": "08/19/2025", "insuranceCarrier": {"carrierName": "Kaiser Permanente", "planCategory": "Primary"}, "amount": "$7386.63", "status": "PAID", "lastUpdated": {"date": "04/24/2025", "time": "10:18 AM"}, "user": "AH", "dateSent": "06/22/2025", "dateSentOrig": "07/09/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Pediatrics Associates", "id": "PROV-8018"}}, {"id": "CLAIM-000051", "patient": {"name": "<PERSON>-<PERSON><PERSON>", "id": "PAT-732218"}, "serviceDate": "06/01/2025", "insuranceCarrier": {"carrierName": "Allstate", "planCategory": "Primary"}, "amount": "$4913.76", "status": "PENDING", "lastUpdated": {"date": "05/08/2025", "time": "1:04 AM"}, "user": "BP", "dateSent": "", "dateSentOrig": "04/06/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Cardiology Associates", "id": "PROV-6727"}}, {"id": "CLAIM-000052", "patient": {"name": "Mr. <PERSON>", "id": "PAT-821555"}, "serviceDate": "03/12/2025", "insuranceCarrier": {"carrierName": "Humana", "planCategory": "Secondary"}, "amount": "$1224.72", "status": "PROCESSING", "lastUpdated": {"date": "07/31/2025", "time": "6:38 PM"}, "user": "MB", "dateSent": "04/04/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Dermatology Associates", "id": "PROV-9782"}}, {"id": "CLAIM-000053", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-305609"}, "serviceDate": "04/08/2025", "insuranceCarrier": {"carrierName": "Farmers", "planCategory": "Secondary"}, "amount": "$9763.63", "status": "PAID", "lastUpdated": {"date": "03/19/2025", "time": "1:15 AM"}, "user": "SR", "dateSent": "05/08/2025", "dateSentOrig": "08/31/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Primary Care Associates", "id": "PROV-8803"}}, {"id": "CLAIM-000054", "patient": {"name": "<PERSON>", "id": "PAT-349145"}, "serviceDate": "05/24/2025", "insuranceCarrier": {"carrierName": "Allstate", "planCategory": "Secondary"}, "amount": "$3179.66", "status": "DENIED", "lastUpdated": {"date": "07/16/2025", "time": "2:42 PM"}, "user": "CW", "dateSent": "08/07/2025", "dateSentOrig": "04/20/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Pediatrics Associates", "id": "PROV-5007"}}, {"id": "CLAIM-000055", "patient": {"name": "<PERSON>", "id": "PAT-691062"}, "serviceDate": "05/10/2025", "insuranceCarrier": {"carrierName": "UnitedHealth", "planCategory": "Secondary"}, "amount": "$1984.83", "status": "PROCESSING", "lastUpdated": {"date": "04/18/2025", "time": "1:10 PM"}, "user": "EK", "dateSent": "06/25/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Dermatology Associates", "id": "PROV-2508"}}, {"id": "CLAIM-000056", "patient": {"name": "<PERSON>", "id": "PAT-881444"}, "serviceDate": "07/17/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Primary"}, "amount": "$4064.50", "status": "PAID", "lastUpdated": {"date": "08/22/2025", "time": "1:07 AM"}, "user": "TL", "dateSent": "04/02/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "OB/GYN Associates", "id": "PROV-1044"}}, {"id": "CLAIM-000057", "patient": {"name": "<PERSON>", "id": "PAT-225686"}, "serviceDate": "06/29/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Primary"}, "amount": "$10577.60", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "08/28/2025", "time": "2:40 AM"}, "user": "SP", "dateSent": "", "dateSentOrig": "", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Emergency Medicine Associates", "id": "PROV-5474"}}, {"id": "CLAIM-000058", "patient": {"name": "<PERSON>", "id": "PAT-294107"}, "serviceDate": "08/10/2025", "insuranceCarrier": {"carrierName": "State Farm", "planCategory": "Primary"}, "amount": "$9777.22", "status": "PAID", "lastUpdated": {"date": "09/02/2025", "time": "1:58 AM"}, "user": "SM", "dateSent": "", "dateSentOrig": "", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Neurology Associates", "id": "PROV-3943"}}, {"id": "CLAIM-000059", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-590040"}, "serviceDate": "07/30/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Primary"}, "amount": "$5512.76", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "05/15/2025", "time": "6:14 AM"}, "user": "DM", "dateSent": "06/26/2025", "dateSentOrig": "05/14/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Pediatrics Associates", "id": "PROV-5671"}}, {"id": "CLAIM-000060", "patient": {"name": "<PERSON>", "id": "PAT-647114"}, "serviceDate": "03/11/2025", "insuranceCarrier": {"carrierName": "GEICO", "planCategory": "Secondary"}, "amount": "$10778.90", "status": "PROCESSING", "lastUpdated": {"date": "04/11/2025", "time": "1:38 PM"}, "user": "DB", "dateSent": "", "dateSentOrig": "03/25/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Oncology Associates", "id": "PROV-3310"}}, {"id": "CLAIM-000061", "patient": {"name": "<PERSON>", "id": "PAT-345361"}, "serviceDate": "05/01/2025", "insuranceCarrier": {"carrierName": "Kaiser Permanente", "planCategory": "Primary"}, "amount": "$2737.71", "status": "DENIED", "lastUpdated": {"date": "06/09/2025", "time": "9:06 PM"}, "user": "DK", "dateSent": "03/14/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "OB/GYN Associates", "id": "PROV-6414"}}, {"id": "CLAIM-000062", "patient": {"name": "Ms. <PERSON><PERSON>", "id": "PAT-418079"}, "serviceDate": "08/13/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Secondary"}, "amount": "$4140.22", "status": "PROCESSING", "lastUpdated": {"date": "07/05/2025", "time": "2:37 AM"}, "user": "KR", "dateSent": "", "dateSentOrig": "03/24/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Primary Care Associates", "id": "PROV-6777"}}, {"id": "CLAIM-000063", "patient": {"name": "Dr. <PERSON>", "id": "PAT-463886"}, "serviceDate": "06/13/2025", "insuranceCarrier": {"carrierName": "Kaiser Permanente", "planCategory": "Secondary"}, "amount": "$10394.23", "status": "PROCESSING", "lastUpdated": {"date": "03/25/2025", "time": "9:34 AM"}, "user": "AT", "dateSent": "05/04/2025", "dateSentOrig": "05/09/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Primary Care Associates", "id": "PROV-5671"}}, {"id": "CLAIM-000064", "patient": {"name": "Krystal O'Hara", "id": "PAT-391900"}, "serviceDate": "07/28/2025", "insuranceCarrier": {"carrierName": "UnitedHealth", "planCategory": "Secondary"}, "amount": "$14912.24", "status": "DENIED", "lastUpdated": {"date": "07/16/2025", "time": "6:03 PM"}, "user": "VP", "dateSent": "06/11/2025", "dateSentOrig": "05/26/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Surgery Associates", "id": "PROV-4128"}}, {"id": "CLAIM-000065", "patient": {"name": "Mr. <PERSON><PERSON>", "id": "PAT-857829"}, "serviceDate": "07/28/2025", "insuranceCarrier": {"carrierName": "MetLife", "planCategory": "Secondary"}, "amount": "$1359.63", "status": "DENIED", "lastUpdated": {"date": "08/02/2025", "time": "10:42 AM"}, "user": "EH", "dateSent": "08/22/2025", "dateSentOrig": "04/30/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Neurology Associates", "id": "PROV-5024"}}, {"id": "CLAIM-000066", "patient": {"name": "<PERSON>-<PERSON><PERSON> Sr.", "id": "PAT-835704"}, "serviceDate": "06/07/2025", "insuranceCarrier": {"carrierName": "Humana", "planCategory": "Secondary"}, "amount": "$13024.15", "status": "PROCESSING", "lastUpdated": {"date": "04/30/2025", "time": "1:24 AM"}, "user": "MW", "dateSent": "08/01/2025", "dateSentOrig": "08/09/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "OB/GYN Associates", "id": "PROV-5383"}}, {"id": "CLAIM-000067", "patient": {"name": "<PERSON>", "id": "PAT-161355"}, "serviceDate": "07/22/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Primary"}, "amount": "$14447.65", "status": "PENDING", "lastUpdated": {"date": "07/22/2025", "time": "6:18 PM"}, "user": "IN", "dateSent": "06/26/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Cardiology Associates", "id": "PROV-1552"}}, {"id": "CLAIM-000068", "patient": {"name": "<PERSON><PERSON><PERSON>", "id": "PAT-228024"}, "serviceDate": "05/28/2025", "insuranceCarrier": {"carrierName": "Anthem", "planCategory": "Primary"}, "amount": "$1538.16", "status": "DENIED", "lastUpdated": {"date": "06/06/2025", "time": "12:48 PM"}, "user": "EL", "dateSent": "03/30/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Oncology Associates", "id": "PROV-2751"}}, {"id": "CLAIM-000069", "patient": {"name": "<PERSON>", "id": "PAT-533209"}, "serviceDate": "05/20/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Primary"}, "amount": "$11410.34", "status": "PAID", "lastUpdated": {"date": "04/14/2025", "time": "1:14 PM"}, "user": "PK", "dateSent": "05/27/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Cardiology Associates", "id": "PROV-5394"}}, {"id": "CLAIM-000070", "patient": {"name": "<PERSON>", "id": "PAT-806514"}, "serviceDate": "05/04/2025", "insuranceCarrier": {"carrierName": "Nationwide", "planCategory": "Secondary"}, "amount": "$11808.88", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "05/24/2025", "time": "4:50 AM"}, "user": "SN", "dateSent": "08/14/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Radiology Associates", "id": "PROV-1599"}}, {"id": "CLAIM-000071", "patient": {"name": "<PERSON>", "id": "PAT-744691"}, "serviceDate": "04/02/2025", "insuranceCarrier": {"carrierName": "Anthem", "planCategory": "Primary"}, "amount": "$206.32", "status": "PROCESSING", "lastUpdated": {"date": "05/09/2025", "time": "4:20 PM"}, "user": "MD", "dateSent": "07/03/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Orthopedics Associates", "id": "PROV-1188"}}, {"id": "CLAIM-000072", "patient": {"name": "<PERSON>", "id": "PAT-209247"}, "serviceDate": "08/28/2025", "insuranceCarrier": {"carrierName": "State Farm", "planCategory": "Secondary"}, "amount": "$9667.47", "status": "PENDING", "lastUpdated": {"date": "08/28/2025", "time": "10:17 PM"}, "user": "LR", "dateSent": "", "dateSentOrig": "", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Cardiology Associates", "id": "PROV-4044"}}, {"id": "CLAIM-000073", "patient": {"name": "<PERSON>", "id": "PAT-209351"}, "serviceDate": "04/16/2025", "insuranceCarrier": {"carrierName": "Blue Cross Blue Shield", "planCategory": "Secondary"}, "amount": "$9696.02", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "04/16/2025", "time": "11:50 PM"}, "user": "DK", "dateSent": "", "dateSentOrig": "", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Surgery Associates", "id": "PROV-6905"}}, {"id": "CLAIM-000074", "patient": {"name": "<PERSON>", "id": "PAT-753381"}, "serviceDate": "06/12/2025", "insuranceCarrier": {"carrierName": "Liberty Mutual", "planCategory": "Primary"}, "amount": "$7600.14", "status": "PROCESSING", "lastUpdated": {"date": "06/12/2025", "time": "8:30 AM"}, "user": "KS", "dateSent": "07/21/2025", "dateSentOrig": "05/28/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Primary Care Associates", "id": "PROV-3220"}}, {"id": "CLAIM-000075", "patient": {"name": "<PERSON>", "id": "PAT-759764"}, "serviceDate": "09/01/2025", "insuranceCarrier": {"carrierName": "State Farm", "planCategory": "Primary"}, "amount": "$5346.88", "status": "DENIED", "lastUpdated": {"date": "03/07/2025", "time": "10:02 PM"}, "user": "CH", "dateSent": "04/30/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "OB/GYN Associates", "id": "PROV-4314"}}, {"id": "CLAIM-000076", "patient": {"name": "<PERSON>", "id": "PAT-102896"}, "serviceDate": "07/14/2025", "insuranceCarrier": {"carrierName": "State Farm", "planCategory": "Primary"}, "amount": "$11918.11", "status": "PAID", "lastUpdated": {"date": "06/01/2025", "time": "8:03 PM"}, "user": "KM", "dateSent": "07/29/2025", "dateSentOrig": "08/02/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Oncology Associates", "id": "PROV-1693"}}, {"id": "CLAIM-000077", "patient": {"name": "<PERSON>", "id": "PAT-232707"}, "serviceDate": "05/06/2025", "insuranceCarrier": {"carrierName": "UnitedHealth", "planCategory": "Primary"}, "amount": "$1351.79", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "07/17/2025", "time": "12:55 PM"}, "user": "HS", "dateSent": "05/19/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Neurology Associates", "id": "PROV-6146"}}, {"id": "CLAIM-000078", "patient": {"name": "Miss <PERSON><PERSON>", "id": "PAT-566270"}, "serviceDate": "04/09/2025", "insuranceCarrier": {"carrierName": "UnitedHealth", "planCategory": "Secondary"}, "amount": "$3907.97", "status": "PENDING", "lastUpdated": {"date": "04/09/2025", "time": "11:11 PM"}, "user": "JC", "dateSent": "07/15/2025", "dateSentOrig": "05/24/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Emergency Medicine Associates", "id": "PROV-7889"}}, {"id": "CLAIM-000079", "patient": {"name": "<PERSON>", "id": "PAT-151349"}, "serviceDate": "06/24/2025", "insuranceCarrier": {"carrierName": "UnitedHealth", "planCategory": "Primary"}, "amount": "$3723.50", "status": "PROCESSING", "lastUpdated": {"date": "04/24/2025", "time": "10:26 PM"}, "user": "AB", "dateSent": "08/17/2025", "dateSentOrig": "06/10/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Neurology Associates", "id": "PROV-4126"}}, {"id": "CLAIM-000080", "patient": {"name": "<PERSON>", "id": "PAT-476913"}, "serviceDate": "03/10/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Secondary"}, "amount": "$14719.93", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "04/11/2025", "time": "7:27 PM"}, "user": "LB", "dateSent": "04/16/2025", "dateSentOrig": "08/06/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Psychiatry Associates", "id": "PROV-5710"}}, {"id": "CLAIM-000081", "patient": {"name": "<PERSON>", "id": "PAT-512470"}, "serviceDate": "08/08/2025", "insuranceCarrier": {"carrierName": "MetLife", "planCategory": "Primary"}, "amount": "$14186.83", "status": "PROCESSING", "lastUpdated": {"date": "05/29/2025", "time": "5:39 PM"}, "user": "AS", "dateSent": "03/16/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Dermatology Associates", "id": "PROV-3327"}}, {"id": "CLAIM-000082", "patient": {"name": "<PERSON><PERSON><PERSON>", "id": "PAT-314916"}, "serviceDate": "05/05/2025", "insuranceCarrier": {"carrierName": "Kaiser Permanente", "planCategory": "Secondary"}, "amount": "$11585.89", "status": "PAID", "lastUpdated": {"date": "05/29/2025", "time": "11:32 AM"}, "user": "BS", "dateSent": "07/24/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Dermatology Associates", "id": "PROV-5709"}}, {"id": "CLAIM-000083", "patient": {"name": "Lyle <PERSON> IV", "id": "PAT-242159"}, "serviceDate": "07/27/2025", "insuranceCarrier": {"carrierName": "Progressive", "planCategory": "Secondary"}, "amount": "$4768.50", "status": "PROCESSING", "lastUpdated": {"date": "07/27/2025", "time": "4:59 AM"}, "user": "CT", "dateSent": "04/03/2025", "dateSentOrig": "06/25/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Cardiology Associates", "id": "PROV-8941"}}, {"id": "CLAIM-000084", "patient": {"name": "<PERSON>-<PERSON>", "id": "PAT-893734"}, "serviceDate": "06/05/2025", "insuranceCarrier": {"carrierName": "Kaiser Permanente", "planCategory": "Primary"}, "amount": "$213.79", "status": "PENDING", "lastUpdated": {"date": "07/14/2025", "time": "11:04 PM"}, "user": "CS", "dateSent": "07/12/2025", "dateSentOrig": "05/30/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Surgery Associates", "id": "PROV-5133"}}, {"id": "CLAIM-000085", "patient": {"name": "Dr. <PERSON>", "id": "PAT-315886"}, "serviceDate": "06/03/2025", "insuranceCarrier": {"carrierName": "Blue Cross Blue Shield", "planCategory": "Primary"}, "amount": "$6769.01", "status": "DENIED", "lastUpdated": {"date": "06/26/2025", "time": "5:50 AM"}, "user": "WC", "dateSent": "05/04/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Primary Care Associates", "id": "PROV-2573"}}, {"id": "CLAIM-000086", "patient": {"name": "<PERSON><PERSON><PERSON><PERSON>", "id": "PAT-374779"}, "serviceDate": "04/16/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Primary"}, "amount": "$4576.60", "status": "DENIED", "lastUpdated": {"date": "04/16/2025", "time": "11:45 PM"}, "user": "MU", "dateSent": "06/15/2025", "dateSentOrig": "05/26/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Oncology Associates", "id": "PROV-8781"}}, {"id": "CLAIM-000087", "patient": {"name": "<PERSON>", "id": "PAT-926268"}, "serviceDate": "05/14/2025", "insuranceCarrier": {"carrierName": "UnitedHealth", "planCategory": "Secondary"}, "amount": "$4400.97", "status": "PROCESSING", "lastUpdated": {"date": "07/29/2025", "time": "1:57 PM"}, "user": "EG", "dateSent": "", "dateSentOrig": "07/11/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Psychiatry Associates", "id": "PROV-1868"}}, {"id": "CLAIM-000088", "patient": {"name": "<PERSON><PERSON><PERSON>", "id": "PAT-768873"}, "serviceDate": "03/09/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Secondary"}, "amount": "$12787.66", "status": "DENIED", "lastUpdated": {"date": "08/14/2025", "time": "3:04 AM"}, "user": "PM", "dateSent": "", "dateSentOrig": "", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Psychiatry Associates", "id": "PROV-1572"}}, {"id": "CLAIM-000089", "patient": {"name": "Ms. <PERSON>", "id": "PAT-614595"}, "serviceDate": "06/22/2025", "insuranceCarrier": {"carrierName": "MetLife", "planCategory": "Primary"}, "amount": "$4771.27", "status": "PENDING", "lastUpdated": {"date": "06/23/2025", "time": "11:26 PM"}, "user": "LP", "dateSent": "06/07/2025", "dateSentOrig": "05/15/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Oncology Associates", "id": "PROV-6870"}}, {"id": "CLAIM-000090", "patient": {"name": "<PERSON>", "id": "PAT-425667"}, "serviceDate": "06/22/2025", "insuranceCarrier": {"carrierName": "MetLife", "planCategory": "Primary"}, "amount": "$2121.86", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "06/22/2025", "time": "4:28 AM"}, "user": "LD", "dateSent": "03/07/2025", "dateSentOrig": "04/15/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Primary Care Associates", "id": "PROV-1972"}}, {"id": "CLAIM-000091", "patient": {"name": "<PERSON>", "id": "PAT-178383"}, "serviceDate": "07/29/2025", "insuranceCarrier": {"carrierName": "State Farm", "planCategory": "Primary"}, "amount": "$5194.72", "status": "PENDING", "lastUpdated": {"date": "07/29/2025", "time": "6:42 PM"}, "user": "BH", "dateSent": "05/19/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Emergency Medicine Associates", "id": "PROV-6471"}}, {"id": "CLAIM-000092", "patient": {"name": "<PERSON>", "id": "PAT-331846"}, "serviceDate": "08/02/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Secondary"}, "amount": "$10306.69", "status": "PAID", "lastUpdated": {"date": "05/27/2025", "time": "5:07 AM"}, "user": "NB", "dateSent": "08/23/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Emergency Medicine Associates", "id": "PROV-4257"}}, {"id": "CLAIM-000093", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-208502"}, "serviceDate": "03/08/2025", "insuranceCarrier": {"carrierName": "Nationwide", "planCategory": "Primary"}, "amount": "$8021.16", "status": "PAID", "lastUpdated": {"date": "05/21/2025", "time": "4:29 PM"}, "user": "AM", "dateSent": "07/16/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Pediatrics Associates", "id": "PROV-7232"}}, {"id": "CLAIM-000094", "patient": {"name": "Ms. <PERSON>", "id": "PAT-871806"}, "serviceDate": "07/06/2025", "insuranceCarrier": {"carrierName": "Nationwide", "planCategory": "Primary"}, "amount": "$4800.03", "status": "PENDING", "lastUpdated": {"date": "06/23/2025", "time": "9:41 AM"}, "user": "CC", "dateSent": "", "dateSentOrig": "04/03/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Primary Care Associates", "id": "PROV-6812"}}, {"id": "CLAIM-000095", "patient": {"name": "<PERSON>", "id": "PAT-428134"}, "serviceDate": "05/07/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Secondary"}, "amount": "$6823.67", "status": "DENIED", "lastUpdated": {"date": "06/02/2025", "time": "1:30 PM"}, "user": "GB", "dateSent": "04/04/2025", "dateSentOrig": "07/20/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Oncology Associates", "id": "PROV-3603"}}, {"id": "CLAIM-000096", "patient": {"name": "Mr. <PERSON>", "id": "PAT-359564"}, "serviceDate": "03/27/2025", "insuranceCarrier": {"carrierName": "Farmers", "planCategory": "Primary"}, "amount": "$5798.04", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "04/03/2025", "time": "7:32 PM"}, "user": "RG", "dateSent": "04/10/2025", "dateSentOrig": "05/07/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Orthopedics Associates", "id": "PROV-9697"}}, {"id": "CLAIM-000097", "patient": {"name": "<PERSON>", "id": "PAT-461600"}, "serviceDate": "05/08/2025", "insuranceCarrier": {"carrierName": "Farmers", "planCategory": "Secondary"}, "amount": "$9360.51", "status": "PENDING", "lastUpdated": {"date": "04/12/2025", "time": "5:02 AM"}, "user": "KW", "dateSent": "", "dateSentOrig": "", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Psychiatry Associates", "id": "PROV-8829"}}, {"id": "CLAIM-000098", "patient": {"name": "<PERSON><PERSON><PERSON>.", "id": "PAT-516408"}, "serviceDate": "07/27/2025", "insuranceCarrier": {"carrierName": "Farmers", "planCategory": "Primary"}, "amount": "$12956.63", "status": "PENDING", "lastUpdated": {"date": "04/27/2025", "time": "3:45 AM"}, "user": "JM", "dateSent": "", "dateSentOrig": "", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Neurology Associates", "id": "PROV-8447"}}, {"id": "CLAIM-000099", "patient": {"name": "<PERSON> Stehr", "id": "PAT-862271"}, "serviceDate": "06/18/2025", "insuranceCarrier": {"carrierName": "Nationwide", "planCategory": "Primary"}, "amount": "$13254.89", "status": "PENDING", "lastUpdated": {"date": "06/27/2025", "time": "12:01 AM"}, "user": "MP", "dateSent": "04/29/2025", "dateSentOrig": "07/29/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Orthopedics Associates", "id": "PROV-2896"}}, {"id": "CLAIM-000100", "patient": {"name": "<PERSON>", "id": "PAT-764082"}, "serviceDate": "03/08/2025", "insuranceCarrier": {"carrierName": "Liberty Mutual", "planCategory": "Secondary"}, "amount": "$2164.18", "status": "DENIED", "lastUpdated": {"date": "07/03/2025", "time": "10:50 PM"}, "user": "AB", "dateSent": "05/19/2025", "dateSentOrig": "05/08/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Cardiology Associates", "id": "PROV-8274"}}, {"id": "CLAIM-000101", "patient": {"name": "<PERSON>", "id": "PAT-815063"}, "serviceDate": "03/19/2025", "insuranceCarrier": {"carrierName": "Progressive", "planCategory": "Primary"}, "amount": "$1116.70", "status": "PENDING", "lastUpdated": {"date": "04/17/2025", "time": "4:48 PM"}, "user": "GH", "dateSent": "04/16/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "OB/GYN Associates", "id": "PROV-6431"}}, {"id": "CLAIM-000102", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-662305"}, "serviceDate": "07/01/2025", "insuranceCarrier": {"carrierName": "Anthem", "planCategory": "Secondary"}, "amount": "$3647.92", "status": "DENIED", "lastUpdated": {"date": "07/01/2025", "time": "10:33 PM"}, "user": "BG", "dateSent": "04/27/2025", "dateSentOrig": "06/28/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Primary Care Associates", "id": "PROV-7737"}}, {"id": "CLAIM-000103", "patient": {"name": "<PERSON>", "id": "PAT-855404"}, "serviceDate": "03/06/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Primary"}, "amount": "$11120.04", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "05/10/2025", "time": "4:50 AM"}, "user": "PK", "dateSent": "", "dateSentOrig": "08/27/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Cardiology Associates", "id": "PROV-7183"}}, {"id": "CLAIM-000104", "patient": {"name": "<PERSON>", "id": "PAT-699746"}, "serviceDate": "05/15/2025", "insuranceCarrier": {"carrierName": "Farmers", "planCategory": "Secondary"}, "amount": "$14262.83", "status": "DENIED", "lastUpdated": {"date": "03/25/2025", "time": "10:38 AM"}, "user": "FK", "dateSent": "04/13/2025", "dateSentOrig": "05/12/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Primary Care Associates", "id": "PROV-7908"}}, {"id": "CLAIM-000105", "patient": {"name": "<PERSON>", "id": "PAT-710031"}, "serviceDate": "08/01/2025", "insuranceCarrier": {"carrierName": "State Farm", "planCategory": "Primary"}, "amount": "$2070.59", "status": "DENIED", "lastUpdated": {"date": "05/02/2025", "time": "8:23 PM"}, "user": "CG", "dateSent": "03/17/2025", "dateSentOrig": "06/07/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Orthopedics Associates", "id": "PROV-8519"}}, {"id": "CLAIM-000106", "patient": {"name": "<PERSON>", "id": "PAT-463860"}, "serviceDate": "05/14/2025", "insuranceCarrier": {"carrierName": "MetLife", "planCategory": "Secondary"}, "amount": "$173.60", "status": "PENDING", "lastUpdated": {"date": "05/14/2025", "time": "7:40 AM"}, "user": "OL", "dateSent": "", "dateSentOrig": "03/09/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Surgery Associates", "id": "PROV-8264"}}, {"id": "CLAIM-000107", "patient": {"name": "<PERSON><PERSON><PERSON>", "id": "PAT-434565"}, "serviceDate": "04/23/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Primary"}, "amount": "$9705.72", "status": "PROCESSING", "lastUpdated": {"date": "04/23/2025", "time": "1:53 PM"}, "user": "OJ", "dateSent": "03/18/2025", "dateSentOrig": "04/20/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Oncology Associates", "id": "PROV-7726"}}, {"id": "CLAIM-000108", "patient": {"name": "<PERSON>", "id": "PAT-966491"}, "serviceDate": "03/17/2025", "insuranceCarrier": {"carrierName": "Anthem", "planCategory": "Primary"}, "amount": "$3400.08", "status": "PAID", "lastUpdated": {"date": "06/26/2025", "time": "10:34 AM"}, "user": "RH", "dateSent": "04/22/2025", "dateSentOrig": "08/19/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Orthopedics Associates", "id": "PROV-5229"}}, {"id": "CLAIM-000109", "patient": {"name": "<PERSON>", "id": "PAT-220847"}, "serviceDate": "07/12/2025", "insuranceCarrier": {"carrierName": "Farmers", "planCategory": "Secondary"}, "amount": "$3647.35", "status": "PENDING", "lastUpdated": {"date": "03/20/2025", "time": "4:48 AM"}, "user": "KB", "dateSent": "05/26/2025", "dateSentOrig": "03/15/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Pediatrics Associates", "id": "PROV-9830"}}, {"id": "CLAIM-000110", "patient": {"name": "Mrs. <PERSON> DVM", "id": "PAT-612585"}, "serviceDate": "08/07/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Primary"}, "amount": "$8913.58", "status": "PAID", "lastUpdated": {"date": "08/30/2025", "time": "3:47 PM"}, "user": "TL", "dateSent": "", "dateSentOrig": "05/31/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Emergency Medicine Associates", "id": "PROV-3226"}}, {"id": "CLAIM-000111", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-108134"}, "serviceDate": "07/04/2025", "insuranceCarrier": {"carrierName": "Humana", "planCategory": "Secondary"}, "amount": "$3449.43", "status": "DENIED", "lastUpdated": {"date": "05/03/2025", "time": "11:43 AM"}, "user": "HK", "dateSent": "03/28/2025", "dateSentOrig": "05/17/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "OB/GYN Associates", "id": "PROV-9939"}}, {"id": "CLAIM-000112", "patient": {"name": "<PERSON>", "id": "PAT-421409"}, "serviceDate": "07/21/2025", "insuranceCarrier": {"carrierName": "GEICO", "planCategory": "Primary"}, "amount": "$8472.01", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "04/19/2025", "time": "3:55 AM"}, "user": "JB", "dateSent": "04/07/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Orthopedics Associates", "id": "PROV-9554"}}, {"id": "CLAIM-000113", "patient": {"name": "<PERSON>", "id": "PAT-904684"}, "serviceDate": "05/24/2025", "insuranceCarrier": {"carrierName": "Farmers", "planCategory": "Secondary"}, "amount": "$6306.23", "status": "DENIED", "lastUpdated": {"date": "06/04/2025", "time": "3:35 AM"}, "user": "ED", "dateSent": "03/19/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Psychiatry Associates", "id": "PROV-4684"}}, {"id": "CLAIM-000114", "patient": {"name": "<PERSON>", "id": "PAT-888294"}, "serviceDate": "08/04/2025", "insuranceCarrier": {"carrierName": "Allstate", "planCategory": "Primary"}, "amount": "$6392.79", "status": "PAID", "lastUpdated": {"date": "07/22/2025", "time": "1:24 PM"}, "user": "AF", "dateSent": "04/17/2025", "dateSentOrig": "07/16/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "OB/GYN Associates", "id": "PROV-3018"}}, {"id": "CLAIM-000115", "patient": {"name": "<PERSON>", "id": "PAT-835101"}, "serviceDate": "07/18/2025", "insuranceCarrier": {"carrierName": "Blue Cross Blue Shield", "planCategory": "Primary"}, "amount": "$13531.56", "status": "PROCESSING", "lastUpdated": {"date": "07/18/2025", "time": "10:23 PM"}, "user": "ML", "dateSent": "", "dateSentOrig": "07/25/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Radiology Associates", "id": "PROV-9569"}}, {"id": "CLAIM-000116", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-562469"}, "serviceDate": "04/23/2025", "insuranceCarrier": {"carrierName": "MetLife", "planCategory": "Primary"}, "amount": "$13995.55", "status": "PROCESSING", "lastUpdated": {"date": "07/13/2025", "time": "6:55 PM"}, "user": "MG", "dateSent": "", "dateSentOrig": "05/08/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Neurology Associates", "id": "PROV-9806"}}, {"id": "CLAIM-000117", "patient": {"name": "<PERSON>", "id": "PAT-246731"}, "serviceDate": "07/11/2025", "insuranceCarrier": {"carrierName": "GEICO", "planCategory": "Secondary"}, "amount": "$5780.72", "status": "PAID", "lastUpdated": {"date": "07/11/2025", "time": "1:42 PM"}, "user": "HP", "dateSent": "", "dateSentOrig": "08/27/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Primary Care Associates", "id": "PROV-5543"}}, {"id": "CLAIM-000118", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-878766"}, "serviceDate": "08/09/2025", "insuranceCarrier": {"carrierName": "Blue Cross Blue Shield", "planCategory": "Primary"}, "amount": "$4368.43", "status": "PROCESSING", "lastUpdated": {"date": "07/28/2025", "time": "4:37 PM"}, "user": "RA", "dateSent": "03/16/2025", "dateSentOrig": "04/27/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Orthopedics Associates", "id": "PROV-4176"}}, {"id": "CLAIM-000119", "patient": {"name": "<PERSON>", "id": "PAT-489490"}, "serviceDate": "05/06/2025", "insuranceCarrier": {"carrierName": "Progressive", "planCategory": "Primary"}, "amount": "$872.37", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "05/06/2025", "time": "8:40 PM"}, "user": "CR", "dateSent": "04/25/2025", "dateSentOrig": "05/23/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "OB/GYN Associates", "id": "PROV-1607"}}, {"id": "CLAIM-000120", "patient": {"name": "<PERSON>", "id": "PAT-480455"}, "serviceDate": "04/08/2025", "insuranceCarrier": {"carrierName": "Humana", "planCategory": "Primary"}, "amount": "$3982.53", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "04/24/2025", "time": "4:39 PM"}, "user": "LB", "dateSent": "", "dateSentOrig": "03/23/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Radiology Associates", "id": "PROV-6612"}}, {"id": "CLAIM-000121", "patient": {"name": "<PERSON>", "id": "PAT-649006"}, "serviceDate": "04/24/2025", "insuranceCarrier": {"carrierName": "UnitedHealth", "planCategory": "Primary"}, "amount": "$8975.08", "status": "PROCESSING", "lastUpdated": {"date": "04/24/2025", "time": "2:09 PM"}, "user": "MD", "dateSent": "07/11/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Neurology Associates", "id": "PROV-1138"}}, {"id": "CLAIM-000122", "patient": {"name": "<PERSON>", "id": "PAT-705214"}, "serviceDate": "04/21/2025", "insuranceCarrier": {"carrierName": "Farmers", "planCategory": "Primary"}, "amount": "$149.59", "status": "PENDING", "lastUpdated": {"date": "07/19/2025", "time": "11:26 AM"}, "user": "HA", "dateSent": "06/25/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Dermatology Associates", "id": "PROV-7426"}}, {"id": "CLAIM-000123", "patient": {"name": "<PERSON>", "id": "PAT-343725"}, "serviceDate": "04/11/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Primary"}, "amount": "$6897.63", "status": "DENIED", "lastUpdated": {"date": "07/05/2025", "time": "1:57 PM"}, "user": "QW", "dateSent": "04/11/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Emergency Medicine Associates", "id": "PROV-7641"}}, {"id": "CLAIM-000124", "patient": {"name": "<PERSON>", "id": "PAT-431604"}, "serviceDate": "03/19/2025", "insuranceCarrier": {"carrierName": "Nationwide", "planCategory": "Secondary"}, "amount": "$11816.12", "status": "PENDING", "lastUpdated": {"date": "04/20/2025", "time": "9:25 AM"}, "user": "LB", "dateSent": "", "dateSentOrig": "05/25/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Primary Care Associates", "id": "PROV-3709"}}, {"id": "CLAIM-000125", "patient": {"name": "<PERSON>", "id": "PAT-689656"}, "serviceDate": "03/16/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Primary"}, "amount": "$2427.92", "status": "PAID", "lastUpdated": {"date": "07/04/2025", "time": "11:48 PM"}, "user": "BH", "dateSent": "06/07/2025", "dateSentOrig": "05/10/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Neurology Associates", "id": "PROV-8457"}}, {"id": "CLAIM-000126", "patient": {"name": "<PERSON>", "id": "PAT-947008"}, "serviceDate": "05/01/2025", "insuranceCarrier": {"carrierName": "Nationwide", "planCategory": "Primary"}, "amount": "$5906.14", "status": "PENDING", "lastUpdated": {"date": "04/10/2025", "time": "11:40 PM"}, "user": "RK", "dateSent": "04/04/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Dermatology Associates", "id": "PROV-7245"}}, {"id": "CLAIM-000127", "patient": {"name": "<PERSON>", "id": "PAT-245226"}, "serviceDate": "03/28/2025", "insuranceCarrier": {"carrierName": "Anthem", "planCategory": "Primary"}, "amount": "$2507.08", "status": "PROCESSING", "lastUpdated": {"date": "05/09/2025", "time": "2:13 PM"}, "user": "LB", "dateSent": "04/18/2025", "dateSentOrig": "08/26/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Radiology Associates", "id": "PROV-6011"}}, {"id": "CLAIM-000128", "patient": {"name": "<PERSON>", "id": "PAT-470762"}, "serviceDate": "05/23/2025", "insuranceCarrier": {"carrierName": "Nationwide", "planCategory": "Secondary"}, "amount": "$7999.25", "status": "PAID", "lastUpdated": {"date": "03/27/2025", "time": "5:08 PM"}, "user": "CT", "dateSent": "06/06/2025", "dateSentOrig": "03/10/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Surgery Associates", "id": "PROV-1621"}}, {"id": "CLAIM-000129", "patient": {"name": "<PERSON>", "id": "PAT-582090"}, "serviceDate": "06/15/2025", "insuranceCarrier": {"carrierName": "Liberty Mutual", "planCategory": "Secondary"}, "amount": "$12653.81", "status": "PENDING", "lastUpdated": {"date": "07/27/2025", "time": "6:11 PM"}, "user": "SB", "dateSent": "07/29/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Dermatology Associates", "id": "PROV-1602"}}, {"id": "CLAIM-000130", "patient": {"name": "<PERSON>", "id": "PAT-437922"}, "serviceDate": "05/15/2025", "insuranceCarrier": {"carrierName": "Liberty Mutual", "planCategory": "Primary"}, "amount": "$14957.81", "status": "PENDING", "lastUpdated": {"date": "04/15/2025", "time": "5:22 AM"}, "user": "FR", "dateSent": "07/12/2025", "dateSentOrig": "04/15/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Radiology Associates", "id": "PROV-5355"}}, {"id": "CLAIM-000131", "patient": {"name": "<PERSON>", "id": "PAT-803763"}, "serviceDate": "07/29/2025", "insuranceCarrier": {"carrierName": "GEICO", "planCategory": "Secondary"}, "amount": "$7782.17", "status": "PAID", "lastUpdated": {"date": "06/26/2025", "time": "9:10 AM"}, "user": "SU", "dateSent": "", "dateSentOrig": "07/02/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Pediatrics Associates", "id": "PROV-6209"}}, {"id": "CLAIM-000132", "patient": {"name": "<PERSON>", "id": "PAT-317350"}, "serviceDate": "04/04/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Primary"}, "amount": "$13619.89", "status": "PENDING", "lastUpdated": {"date": "03/20/2025", "time": "9:22 PM"}, "user": "MG", "dateSent": "04/23/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Radiology Associates", "id": "PROV-3920"}}, {"id": "CLAIM-000133", "patient": {"name": "<PERSON>", "id": "PAT-833445"}, "serviceDate": "04/10/2025", "insuranceCarrier": {"carrierName": "GEICO", "planCategory": "Secondary"}, "amount": "$2836.14", "status": "PAID", "lastUpdated": {"date": "05/12/2025", "time": "6:01 AM"}, "user": "AF", "dateSent": "03/13/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Orthopedics Associates", "id": "PROV-9081"}}, {"id": "CLAIM-000134", "patient": {"name": "<PERSON>", "id": "PAT-619032"}, "serviceDate": "05/04/2025", "insuranceCarrier": {"carrierName": "Anthem", "planCategory": "Primary"}, "amount": "$14983.42", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "07/30/2025", "time": "11:59 AM"}, "user": "AC", "dateSent": "05/10/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Psychiatry Associates", "id": "PROV-3815"}}, {"id": "CLAIM-000135", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-853407"}, "serviceDate": "04/17/2025", "insuranceCarrier": {"carrierName": "State Farm", "planCategory": "Secondary"}, "amount": "$9378.60", "status": "PROCESSING", "lastUpdated": {"date": "06/19/2025", "time": "12:45 AM"}, "user": "MF", "dateSent": "08/20/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Neurology Associates", "id": "PROV-5749"}}, {"id": "CLAIM-000136", "patient": {"name": "<PERSON>", "id": "PAT-148013"}, "serviceDate": "09/02/2025", "insuranceCarrier": {"carrierName": "Kaiser Permanente", "planCategory": "Secondary"}, "amount": "$10171.04", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "09/02/2025", "time": "3:30 PM"}, "user": "MG", "dateSent": "06/29/2025", "dateSentOrig": "04/08/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Emergency Medicine Associates", "id": "PROV-5096"}}, {"id": "CLAIM-000137", "patient": {"name": "<PERSON>", "id": "PAT-854339"}, "serviceDate": "08/04/2025", "insuranceCarrier": {"carrierName": "Liberty Mutual", "planCategory": "Secondary"}, "amount": "$8050.63", "status": "PENDING", "lastUpdated": {"date": "04/03/2025", "time": "7:16 PM"}, "user": "KH", "dateSent": "03/12/2025", "dateSentOrig": "03/22/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Radiology Associates", "id": "PROV-8928"}}, {"id": "CLAIM-000138", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-357867"}, "serviceDate": "07/03/2025", "insuranceCarrier": {"carrierName": "Liberty Mutual", "planCategory": "Secondary"}, "amount": "$474.63", "status": "PROCESSING", "lastUpdated": {"date": "08/29/2025", "time": "6:50 AM"}, "user": "EW", "dateSent": "", "dateSentOrig": "", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "OB/GYN Associates", "id": "PROV-1765"}}, {"id": "CLAIM-000139", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-250741"}, "serviceDate": "03/19/2025", "insuranceCarrier": {"carrierName": "Blue Cross Blue Shield", "planCategory": "Secondary"}, "amount": "$11395.41", "status": "PROCESSING", "lastUpdated": {"date": "04/25/2025", "time": "10:19 AM"}, "user": "DG", "dateSent": "07/06/2025", "dateSentOrig": "04/13/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Surgery Associates", "id": "PROV-2322"}}, {"id": "CLAIM-000140", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-823424"}, "serviceDate": "08/09/2025", "insuranceCarrier": {"carrierName": "Blue Cross Blue Shield", "planCategory": "Secondary"}, "amount": "$7594.99", "status": "PAID", "lastUpdated": {"date": "04/25/2025", "time": "6:04 AM"}, "user": "EB", "dateSent": "05/28/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Psychiatry Associates", "id": "PROV-7004"}}, {"id": "CLAIM-000141", "patient": {"name": "<PERSON><PERSON> III", "id": "PAT-275649"}, "serviceDate": "08/09/2025", "insuranceCarrier": {"carrierName": "MetLife", "planCategory": "Secondary"}, "amount": "$7401.22", "status": "PENDING", "lastUpdated": {"date": "08/09/2025", "time": "5:51 AM"}, "user": "ER", "dateSent": "04/11/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Dermatology Associates", "id": "PROV-6109"}}, {"id": "CLAIM-000142", "patient": {"name": "<PERSON>", "id": "PAT-227101"}, "serviceDate": "06/03/2025", "insuranceCarrier": {"carrierName": "Allstate", "planCategory": "Primary"}, "amount": "$4197.68", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "08/02/2025", "time": "4:55 PM"}, "user": "KH", "dateSent": "", "dateSentOrig": "03/22/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Psychiatry Associates", "id": "PROV-8606"}}, {"id": "CLAIM-000143", "patient": {"name": "<PERSON>", "id": "PAT-336922"}, "serviceDate": "04/07/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Secondary"}, "amount": "$11894.11", "status": "DENIED", "lastUpdated": {"date": "04/03/2025", "time": "6:21 AM"}, "user": "JB", "dateSent": "04/13/2025", "dateSentOrig": "08/17/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Oncology Associates", "id": "PROV-5196"}}, {"id": "CLAIM-000144", "patient": {"name": "<PERSON>", "id": "PAT-844797"}, "serviceDate": "04/03/2025", "insuranceCarrier": {"carrierName": "Blue Cross Blue Shield", "planCategory": "Primary"}, "amount": "$3086.61", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "06/25/2025", "time": "11:30 PM"}, "user": "AL", "dateSent": "08/04/2025", "dateSentOrig": "03/11/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Oncology Associates", "id": "PROV-3137"}}, {"id": "CLAIM-000145", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-821301"}, "serviceDate": "07/15/2025", "insuranceCarrier": {"carrierName": "Humana", "planCategory": "Secondary"}, "amount": "$1904.15", "status": "DENIED", "lastUpdated": {"date": "07/06/2025", "time": "7:55 AM"}, "user": "AS", "dateSent": "07/24/2025", "dateSentOrig": "05/07/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Primary Care Associates", "id": "PROV-9653"}}, {"id": "CLAIM-000146", "patient": {"name": "<PERSON>. <PERSON><PERSON>", "id": "PAT-896369"}, "serviceDate": "08/21/2025", "insuranceCarrier": {"carrierName": "GEICO", "planCategory": "Primary"}, "amount": "$4242.20", "status": "PROCESSING", "lastUpdated": {"date": "07/13/2025", "time": "9:37 PM"}, "user": "EB", "dateSent": "04/22/2025", "dateSentOrig": "08/04/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "OB/GYN Associates", "id": "PROV-2671"}}, {"id": "CLAIM-000147", "patient": {"name": "<PERSON>", "id": "PAT-971268"}, "serviceDate": "08/21/2025", "insuranceCarrier": {"carrierName": "Kaiser Permanente", "planCategory": "Secondary"}, "amount": "$3718.71", "status": "PENDING", "lastUpdated": {"date": "07/20/2025", "time": "10:34 PM"}, "user": "RB", "dateSent": "04/14/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Neurology Associates", "id": "PROV-4621"}}, {"id": "CLAIM-000148", "patient": {"name": "<PERSON>", "id": "PAT-266169"}, "serviceDate": "08/15/2025", "insuranceCarrier": {"carrierName": "Humana", "planCategory": "Primary"}, "amount": "$12468.47", "status": "PAID", "lastUpdated": {"date": "06/10/2025", "time": "4:29 AM"}, "user": "LC", "dateSent": "06/13/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Neurology Associates", "id": "PROV-7597"}}, {"id": "CLAIM-000149", "patient": {"name": "<PERSON> Treutel", "id": "PAT-394115"}, "serviceDate": "08/21/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Secondary"}, "amount": "$12019.69", "status": "PAID", "lastUpdated": {"date": "05/29/2025", "time": "2:58 AM"}, "user": "AR", "dateSent": "07/03/2025", "dateSentOrig": "03/25/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Dermatology Associates", "id": "PROV-6111"}}, {"id": "CLAIM-000150", "patient": {"name": "<PERSON>", "id": "PAT-412766"}, "serviceDate": "08/22/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Primary"}, "amount": "$10387.87", "status": "PENDING", "lastUpdated": {"date": "06/25/2025", "time": "4:34 AM"}, "user": "RK", "dateSent": "05/11/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Orthopedics Associates", "id": "PROV-2881"}}, {"id": "CLAIM-000151", "patient": {"name": "Ms. <PERSON>", "id": "PAT-446149"}, "serviceDate": "05/29/2025", "insuranceCarrier": {"carrierName": "Kaiser Permanente", "planCategory": "Primary"}, "amount": "$13990.01", "status": "PAID", "lastUpdated": {"date": "05/29/2025", "time": "9:31 AM"}, "user": "JC", "dateSent": "08/28/2025", "dateSentOrig": "07/13/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Primary Care Associates", "id": "PROV-4759"}}, {"id": "CLAIM-000152", "patient": {"name": "<PERSON>", "id": "PAT-462886"}, "serviceDate": "06/25/2025", "insuranceCarrier": {"carrierName": "Liberty Mutual", "planCategory": "Secondary"}, "amount": "$9187.17", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "03/25/2025", "time": "5:24 PM"}, "user": "MH", "dateSent": "04/04/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Primary Care Associates", "id": "PROV-4772"}}, {"id": "CLAIM-000153", "patient": {"name": "<PERSON> I", "id": "PAT-897563"}, "serviceDate": "08/12/2025", "insuranceCarrier": {"carrierName": "Farmers", "planCategory": "Secondary"}, "amount": "$9540.33", "status": "DENIED", "lastUpdated": {"date": "07/20/2025", "time": "7:40 AM"}, "user": "MC", "dateSent": "", "dateSentOrig": "08/05/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Oncology Associates", "id": "PROV-4411"}}, {"id": "CLAIM-000154", "patient": {"name": "<PERSON><PERSON><PERSON>", "id": "PAT-392690"}, "serviceDate": "04/21/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Primary"}, "amount": "$120.52", "status": "PROCESSING", "lastUpdated": {"date": "04/21/2025", "time": "5:43 AM"}, "user": "MW", "dateSent": "07/25/2025", "dateSentOrig": "09/01/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Primary Care Associates", "id": "PROV-6431"}}, {"id": "CLAIM-000155", "patient": {"name": "<PERSON>", "id": "PAT-376494"}, "serviceDate": "07/08/2025", "insuranceCarrier": {"carrierName": "Allstate", "planCategory": "Primary"}, "amount": "$2424.37", "status": "PAID", "lastUpdated": {"date": "07/21/2025", "time": "11:05 AM"}, "user": "BO", "dateSent": "04/05/2025", "dateSentOrig": "03/11/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Pediatrics Associates", "id": "PROV-9772"}}, {"id": "CLAIM-000156", "patient": {"name": "<PERSON>", "id": "PAT-616554"}, "serviceDate": "04/17/2025", "insuranceCarrier": {"carrierName": "Humana", "planCategory": "Primary"}, "amount": "$8759.21", "status": "PAID", "lastUpdated": {"date": "05/01/2025", "time": "1:35 AM"}, "user": "WG", "dateSent": "", "dateSentOrig": "08/16/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Neurology Associates", "id": "PROV-5948"}}, {"id": "CLAIM-000157", "patient": {"name": "Mr. <PERSON>", "id": "PAT-185824"}, "serviceDate": "05/22/2025", "insuranceCarrier": {"carrierName": "Kaiser Permanente", "planCategory": "Primary"}, "amount": "$13063.56", "status": "PAID", "lastUpdated": {"date": "07/20/2025", "time": "1:29 AM"}, "user": "EL", "dateSent": "06/17/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Surgery Associates", "id": "PROV-2385"}}, {"id": "CLAIM-000158", "patient": {"name": "<PERSON>", "id": "PAT-452719"}, "serviceDate": "04/24/2025", "insuranceCarrier": {"carrierName": "Progressive", "planCategory": "Primary"}, "amount": "$9631.53", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "04/24/2025", "time": "11:17 AM"}, "user": "HP", "dateSent": "", "dateSentOrig": "", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Surgery Associates", "id": "PROV-4572"}}, {"id": "CLAIM-000159", "patient": {"name": "Dr. <PERSON> DVM", "id": "PAT-979551"}, "serviceDate": "07/17/2025", "insuranceCarrier": {"carrierName": "GEICO", "planCategory": "Secondary"}, "amount": "$8677.66", "status": "PROCESSING", "lastUpdated": {"date": "05/30/2025", "time": "8:24 AM"}, "user": "ES", "dateSent": "08/22/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Surgery Associates", "id": "PROV-6005"}}, {"id": "CLAIM-000160", "patient": {"name": "<PERSON>", "id": "PAT-289286"}, "serviceDate": "03/09/2025", "insuranceCarrier": {"carrierName": "GEICO", "planCategory": "Primary"}, "amount": "$12986.80", "status": "PAID", "lastUpdated": {"date": "08/30/2025", "time": "9:50 AM"}, "user": "MM", "dateSent": "06/18/2025", "dateSentOrig": "04/25/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Surgery Associates", "id": "PROV-3036"}}, {"id": "CLAIM-000161", "patient": {"name": "<PERSON>", "id": "PAT-645397"}, "serviceDate": "04/17/2025", "insuranceCarrier": {"carrierName": "Liberty Mutual", "planCategory": "Primary"}, "amount": "$11018.92", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "08/21/2025", "time": "4:27 AM"}, "user": "AB", "dateSent": "05/22/2025", "dateSentOrig": "04/07/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Orthopedics Associates", "id": "PROV-6974"}}, {"id": "CLAIM-000162", "patient": {"name": "<PERSON><PERSON><PERSON>", "id": "PAT-180889"}, "serviceDate": "03/15/2025", "insuranceCarrier": {"carrierName": "State Farm", "planCategory": "Primary"}, "amount": "$13761.09", "status": "PENDING", "lastUpdated": {"date": "07/13/2025", "time": "4:31 PM"}, "user": "SP", "dateSent": "08/24/2025", "dateSentOrig": "03/07/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Orthopedics Associates", "id": "PROV-2961"}}, {"id": "CLAIM-000163", "patient": {"name": "<PERSON>", "id": "PAT-600920"}, "serviceDate": "06/28/2025", "insuranceCarrier": {"carrierName": "Humana", "planCategory": "Primary"}, "amount": "$1438.92", "status": "PROCESSING", "lastUpdated": {"date": "06/28/2025", "time": "8:06 AM"}, "user": "MC", "dateSent": "07/13/2025", "dateSentOrig": "04/21/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Primary Care Associates", "id": "PROV-5862"}}, {"id": "CLAIM-000164", "patient": {"name": "<PERSON>", "id": "PAT-646421"}, "serviceDate": "08/26/2025", "insuranceCarrier": {"carrierName": "Humana", "planCategory": "Primary"}, "amount": "$1447.10", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "04/07/2025", "time": "12:26 PM"}, "user": "SM", "dateSent": "04/30/2025", "dateSentOrig": "07/05/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Emergency Medicine Associates", "id": "PROV-3837"}}, {"id": "CLAIM-000165", "patient": {"name": "<PERSON>", "id": "PAT-885573"}, "serviceDate": "06/28/2025", "insuranceCarrier": {"carrierName": "MetLife", "planCategory": "Secondary"}, "amount": "$9297.11", "status": "PAID", "lastUpdated": {"date": "04/27/2025", "time": "6:37 PM"}, "user": "CW", "dateSent": "06/01/2025", "dateSentOrig": "06/12/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Cardiology Associates", "id": "PROV-1513"}}, {"id": "CLAIM-000166", "patient": {"name": "<PERSON>", "id": "PAT-548316"}, "serviceDate": "06/20/2025", "insuranceCarrier": {"carrierName": "Progressive", "planCategory": "Primary"}, "amount": "$4633.87", "status": "DENIED", "lastUpdated": {"date": "06/20/2025", "time": "10:34 PM"}, "user": "FK", "dateSent": "07/11/2025", "dateSentOrig": "05/10/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Pediatrics Associates", "id": "PROV-1188"}}, {"id": "CLAIM-000167", "patient": {"name": "<PERSON>", "id": "PAT-780628"}, "serviceDate": "03/17/2025", "insuranceCarrier": {"carrierName": "Kaiser Permanente", "planCategory": "Primary"}, "amount": "$14022.58", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "06/20/2025", "time": "12:22 AM"}, "user": "EC", "dateSent": "09/02/2025", "dateSentOrig": "05/09/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Neurology Associates", "id": "PROV-2036"}}, {"id": "CLAIM-000168", "patient": {"name": "<PERSON>", "id": "PAT-832351"}, "serviceDate": "05/20/2025", "insuranceCarrier": {"carrierName": "Farmers", "planCategory": "Secondary"}, "amount": "$14319.63", "status": "PAID", "lastUpdated": {"date": "06/23/2025", "time": "7:24 AM"}, "user": "BC", "dateSent": "", "dateSentOrig": "03/17/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Psychiatry Associates", "id": "PROV-6357"}}, {"id": "CLAIM-000169", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-917216"}, "serviceDate": "06/18/2025", "insuranceCarrier": {"carrierName": "Humana", "planCategory": "Primary"}, "amount": "$2173.45", "status": "PAID", "lastUpdated": {"date": "06/22/2025", "time": "1:56 PM"}, "user": "KB", "dateSent": "06/28/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Surgery Associates", "id": "PROV-3493"}}, {"id": "CLAIM-000170", "patient": {"name": "<PERSON>", "id": "PAT-464880"}, "serviceDate": "03/29/2025", "insuranceCarrier": {"carrierName": "MetLife", "planCategory": "Secondary"}, "amount": "$14040.66", "status": "DENIED", "lastUpdated": {"date": "07/30/2025", "time": "12:01 PM"}, "user": "AG", "dateSent": "05/25/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Neurology Associates", "id": "PROV-1491"}}, {"id": "CLAIM-000171", "patient": {"name": "Ms. <PERSON>", "id": "PAT-629200"}, "serviceDate": "08/16/2025", "insuranceCarrier": {"carrierName": "Humana", "planCategory": "Secondary"}, "amount": "$8331.44", "status": "PROCESSING", "lastUpdated": {"date": "07/26/2025", "time": "8:24 PM"}, "user": "KE", "dateSent": "06/17/2025", "dateSentOrig": "03/17/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Radiology Associates", "id": "PROV-7873"}}, {"id": "CLAIM-000172", "patient": {"name": "<PERSON>", "id": "PAT-282064"}, "serviceDate": "04/16/2025", "insuranceCarrier": {"carrierName": "Humana", "planCategory": "Primary"}, "amount": "$5981.96", "status": "PENDING", "lastUpdated": {"date": "05/14/2025", "time": "11:08 PM"}, "user": "AM", "dateSent": "08/11/2025", "dateSentOrig": "08/26/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Primary Care Associates", "id": "PROV-1882"}}, {"id": "CLAIM-000173", "patient": {"name": "Dr. <PERSON>", "id": "PAT-169468"}, "serviceDate": "04/09/2025", "insuranceCarrier": {"carrierName": "Blue Cross Blue Shield", "planCategory": "Primary"}, "amount": "$1871.17", "status": "PENDING", "lastUpdated": {"date": "05/10/2025", "time": "12:41 PM"}, "user": "BL", "dateSent": "03/07/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Radiology Associates", "id": "PROV-4697"}}, {"id": "CLAIM-000174", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-755790"}, "serviceDate": "05/20/2025", "insuranceCarrier": {"carrierName": "UnitedHealth", "planCategory": "Primary"}, "amount": "$3572.93", "status": "PAID", "lastUpdated": {"date": "07/28/2025", "time": "9:32 AM"}, "user": "TG", "dateSent": "07/25/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Dermatology Associates", "id": "PROV-8868"}}, {"id": "CLAIM-000175", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-757783"}, "serviceDate": "04/16/2025", "insuranceCarrier": {"carrierName": "Kaiser Permanente", "planCategory": "Secondary"}, "amount": "$12493.36", "status": "PROCESSING", "lastUpdated": {"date": "04/16/2025", "time": "1:41 AM"}, "user": "HJ", "dateSent": "09/02/2025", "dateSentOrig": "07/06/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Neurology Associates", "id": "PROV-2197"}}, {"id": "CLAIM-000176", "patient": {"name": "<PERSON>-<PERSON><PERSON><PERSON>", "id": "PAT-535422"}, "serviceDate": "08/24/2025", "insuranceCarrier": {"carrierName": "Kaiser Permanente", "planCategory": "Primary"}, "amount": "$8916.87", "status": "PENDING", "lastUpdated": {"date": "05/30/2025", "time": "7:34 AM"}, "user": "NH", "dateSent": "", "dateSentOrig": "04/22/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Emergency Medicine Associates", "id": "PROV-6106"}}, {"id": "CLAIM-000177", "patient": {"name": "<PERSON>", "id": "PAT-639369"}, "serviceDate": "07/17/2025", "insuranceCarrier": {"carrierName": "MetLife", "planCategory": "Primary"}, "amount": "$121.80", "status": "PENDING", "lastUpdated": {"date": "07/17/2025", "time": "1:35 PM"}, "user": "CB", "dateSent": "06/13/2025", "dateSentOrig": "06/08/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Pediatrics Associates", "id": "PROV-2766"}}, {"id": "CLAIM-000178", "patient": {"name": "<PERSON>", "id": "PAT-774873"}, "serviceDate": "08/08/2025", "insuranceCarrier": {"carrierName": "Nationwide", "planCategory": "Secondary"}, "amount": "$2732.25", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "04/20/2025", "time": "4:33 PM"}, "user": "HB", "dateSent": "04/06/2025", "dateSentOrig": "04/14/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Orthopedics Associates", "id": "PROV-4602"}}, {"id": "CLAIM-000179", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-238405"}, "serviceDate": "03/09/2025", "insuranceCarrier": {"carrierName": "Nationwide", "planCategory": "Primary"}, "amount": "$8479.75", "status": "DENIED", "lastUpdated": {"date": "08/21/2025", "time": "12:43 AM"}, "user": "SF", "dateSent": "", "dateSentOrig": "04/15/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Cardiology Associates", "id": "PROV-8732"}}, {"id": "CLAIM-000180", "patient": {"name": "<PERSON>", "id": "PAT-288721"}, "serviceDate": "07/06/2025", "insuranceCarrier": {"carrierName": "Farmers", "planCategory": "Secondary"}, "amount": "$14019.80", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "07/22/2025", "time": "9:57 AM"}, "user": "BR", "dateSent": "04/13/2025", "dateSentOrig": "05/02/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Oncology Associates", "id": "PROV-6806"}}, {"id": "CLAIM-000181", "patient": {"name": "<PERSON><PERSON> III", "id": "PAT-776151"}, "serviceDate": "04/17/2025", "insuranceCarrier": {"carrierName": "Nationwide", "planCategory": "Secondary"}, "amount": "$2762.63", "status": "PROCESSING", "lastUpdated": {"date": "05/06/2025", "time": "1:29 AM"}, "user": "DS", "dateSent": "", "dateSentOrig": "08/08/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Pediatrics Associates", "id": "PROV-7881"}}, {"id": "CLAIM-000182", "patient": {"name": "<PERSON>", "id": "PAT-559513"}, "serviceDate": "07/09/2025", "insuranceCarrier": {"carrierName": "Anthem", "planCategory": "Primary"}, "amount": "$12040.88", "status": "PROCESSING", "lastUpdated": {"date": "08/25/2025", "time": "4:39 PM"}, "user": "RH", "dateSent": "", "dateSentOrig": "07/11/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Emergency Medicine Associates", "id": "PROV-7099"}}, {"id": "CLAIM-000183", "patient": {"name": "Miss <PERSON>", "id": "PAT-251450"}, "serviceDate": "08/11/2025", "insuranceCarrier": {"carrierName": "Liberty Mutual", "planCategory": "Secondary"}, "amount": "$2858.20", "status": "DENIED", "lastUpdated": {"date": "06/23/2025", "time": "6:49 PM"}, "user": "MS", "dateSent": "07/10/2025", "dateSentOrig": "08/12/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Emergency Medicine Associates", "id": "PROV-2013"}}, {"id": "CLAIM-000184", "patient": {"name": "<PERSON>", "id": "PAT-323912"}, "serviceDate": "08/13/2025", "insuranceCarrier": {"carrierName": "Farmers", "planCategory": "Primary"}, "amount": "$11638.19", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "03/08/2025", "time": "7:58 AM"}, "user": "FL", "dateSent": "08/12/2025", "dateSentOrig": "06/06/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Cardiology Associates", "id": "PROV-9844"}}, {"id": "CLAIM-000185", "patient": {"name": "Dr. <PERSON><PERSON>", "id": "PAT-838227"}, "serviceDate": "03/20/2025", "insuranceCarrier": {"carrierName": "State Farm", "planCategory": "Primary"}, "amount": "$6883.98", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "06/07/2025", "time": "4:35 PM"}, "user": "HH", "dateSent": "04/12/2025", "dateSentOrig": "04/15/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Primary Care Associates", "id": "PROV-9235"}}, {"id": "CLAIM-000186", "patient": {"name": "<PERSON>", "id": "PAT-207226"}, "serviceDate": "07/18/2025", "insuranceCarrier": {"carrierName": "MetLife", "planCategory": "Primary"}, "amount": "$5638.00", "status": "DENIED", "lastUpdated": {"date": "03/26/2025", "time": "1:59 AM"}, "user": "AB", "dateSent": "07/29/2025", "dateSentOrig": "05/25/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Emergency Medicine Associates", "id": "PROV-5580"}}, {"id": "CLAIM-000187", "patient": {"name": "<PERSON>", "id": "PAT-988206"}, "serviceDate": "08/09/2025", "insuranceCarrier": {"carrierName": "GEICO", "planCategory": "Primary"}, "amount": "$8087.47", "status": "PROCESSING", "lastUpdated": {"date": "08/09/2025", "time": "2:42 AM"}, "user": "AF", "dateSent": "", "dateSentOrig": "08/02/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Primary Care Associates", "id": "PROV-3770"}}, {"id": "CLAIM-000188", "patient": {"name": "<PERSON>", "id": "PAT-439180"}, "serviceDate": "04/17/2025", "insuranceCarrier": {"carrierName": "Nationwide", "planCategory": "Secondary"}, "amount": "$7407.03", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "08/25/2025", "time": "1:17 AM"}, "user": "ES", "dateSent": "06/29/2025", "dateSentOrig": "03/19/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Pediatrics Associates", "id": "PROV-2711"}}, {"id": "CLAIM-000189", "patient": {"name": "<PERSON>", "id": "PAT-868548"}, "serviceDate": "06/12/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Secondary"}, "amount": "$11547.41", "status": "PROCESSING", "lastUpdated": {"date": "06/12/2025", "time": "10:53 AM"}, "user": "EH", "dateSent": "06/12/2025", "dateSentOrig": "07/09/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Dermatology Associates", "id": "PROV-3792"}}, {"id": "CLAIM-000190", "patient": {"name": "<PERSON>", "id": "PAT-439626"}, "serviceDate": "07/28/2025", "insuranceCarrier": {"carrierName": "Kaiser Permanente", "planCategory": "Primary"}, "amount": "$4127.97", "status": "PROCESSING", "lastUpdated": {"date": "07/28/2025", "time": "5:02 AM"}, "user": "GC", "dateSent": "", "dateSentOrig": "08/30/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Neurology Associates", "id": "PROV-7977"}}, {"id": "CLAIM-000191", "patient": {"name": "<PERSON>", "id": "PAT-827441"}, "serviceDate": "06/27/2025", "insuranceCarrier": {"carrierName": "Anthem", "planCategory": "Primary"}, "amount": "$4722.70", "status": "DENIED", "lastUpdated": {"date": "04/02/2025", "time": "8:48 AM"}, "user": "BF", "dateSent": "06/30/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Emergency Medicine Associates", "id": "PROV-4262"}}, {"id": "CLAIM-000192", "patient": {"name": "Ramona Brekke II", "id": "PAT-715483"}, "serviceDate": "04/12/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Secondary"}, "amount": "$4758.62", "status": "PAID", "lastUpdated": {"date": "04/19/2025", "time": "5:04 AM"}, "user": "EV", "dateSent": "06/01/2025", "dateSentOrig": "05/01/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "OB/GYN Associates", "id": "PROV-2455"}}, {"id": "CLAIM-000193", "patient": {"name": "<PERSON>", "id": "PAT-275912"}, "serviceDate": "04/12/2025", "insuranceCarrier": {"carrierName": "Blue Cross Blue Shield", "planCategory": "Secondary"}, "amount": "$4934.38", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "05/06/2025", "time": "12:44 PM"}, "user": "SG", "dateSent": "04/16/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "OB/GYN Associates", "id": "PROV-4813"}}, {"id": "CLAIM-000194", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-785957"}, "serviceDate": "08/26/2025", "insuranceCarrier": {"carrierName": "Liberty Mutual", "planCategory": "Primary"}, "amount": "$6519.57", "status": "PROCESSING", "lastUpdated": {"date": "07/27/2025", "time": "12:37 PM"}, "user": "QH", "dateSent": "05/18/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Oncology Associates", "id": "PROV-7740"}}, {"id": "CLAIM-000195", "patient": {"name": "<PERSON>", "id": "PAT-773747"}, "serviceDate": "06/29/2025", "insuranceCarrier": {"carrierName": "MetLife", "planCategory": "Primary"}, "amount": "$14509.89", "status": "DENIED", "lastUpdated": {"date": "06/29/2025", "time": "6:28 PM"}, "user": "JF", "dateSent": "03/29/2025", "dateSentOrig": "03/07/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Primary Care Associates", "id": "PROV-9795"}}, {"id": "CLAIM-000196", "patient": {"name": "<PERSON>", "id": "PAT-758125"}, "serviceDate": "07/04/2025", "insuranceCarrier": {"carrierName": "GEICO", "planCategory": "Secondary"}, "amount": "$8614.11", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "03/19/2025", "time": "11:07 AM"}, "user": "RH", "dateSent": "08/07/2025", "dateSentOrig": "08/10/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Cardiology Associates", "id": "PROV-7991"}}, {"id": "CLAIM-000197", "patient": {"name": "<PERSON>", "id": "PAT-276838"}, "serviceDate": "04/08/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Primary"}, "amount": "$4847.96", "status": "PENDING", "lastUpdated": {"date": "04/08/2025", "time": "9:26 PM"}, "user": "ML", "dateSent": "03/16/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Cardiology Associates", "id": "PROV-3724"}}, {"id": "CLAIM-000198", "patient": {"name": "<PERSON> DDS", "id": "PAT-930343"}, "serviceDate": "06/16/2025", "insuranceCarrier": {"carrierName": "GEICO", "planCategory": "Secondary"}, "amount": "$2900.55", "status": "DENIED", "lastUpdated": {"date": "06/23/2025", "time": "6:31 AM"}, "user": "JS", "dateSent": "03/22/2025", "dateSentOrig": "07/03/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Oncology Associates", "id": "PROV-3490"}}, {"id": "CLAIM-000199", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-133041"}, "serviceDate": "05/08/2025", "insuranceCarrier": {"carrierName": "MetLife", "planCategory": "Primary"}, "amount": "$13810.03", "status": "PAID", "lastUpdated": {"date": "07/18/2025", "time": "6:05 PM"}, "user": "JF", "dateSent": "05/23/2025", "dateSentOrig": "03/25/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Pediatrics Associates", "id": "PROV-3158"}}, {"id": "CLAIM-000200", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-147469"}, "serviceDate": "06/05/2025", "insuranceCarrier": {"carrierName": "Liberty Mutual", "planCategory": "Primary"}, "amount": "$701.14", "status": "PENDING", "lastUpdated": {"date": "06/05/2025", "time": "4:25 AM"}, "user": "IM", "dateSent": "05/02/2025", "dateSentOrig": "05/11/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Cardiology Associates", "id": "PROV-7899"}}, {"id": "CLAIM-000201", "patient": {"name": "<PERSON>", "id": "PAT-510084"}, "serviceDate": "08/28/2025", "insuranceCarrier": {"carrierName": "MetLife", "planCategory": "Secondary"}, "amount": "$4246.73", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "08/28/2025", "time": "1:29 PM"}, "user": "LF", "dateSent": "08/07/2025", "dateSentOrig": "06/07/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Cardiology Associates", "id": "PROV-1144"}}, {"id": "CLAIM-000202", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-213503"}, "serviceDate": "05/13/2025", "insuranceCarrier": {"carrierName": "Allstate", "planCategory": "Secondary"}, "amount": "$6111.09", "status": "PROCESSING", "lastUpdated": {"date": "08/15/2025", "time": "8:07 AM"}, "user": "CR", "dateSent": "04/30/2025", "dateSentOrig": "07/21/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Oncology Associates", "id": "PROV-3032"}}, {"id": "CLAIM-000203", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-217317"}, "serviceDate": "03/14/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Secondary"}, "amount": "$6396.95", "status": "PENDING", "lastUpdated": {"date": "03/13/2025", "time": "6:48 PM"}, "user": "AV", "dateSent": "08/03/2025", "dateSentOrig": "08/22/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Surgery Associates", "id": "PROV-9472"}}, {"id": "CLAIM-000204", "patient": {"name": "<PERSON>", "id": "PAT-936463"}, "serviceDate": "05/11/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Secondary"}, "amount": "$11084.73", "status": "PAID", "lastUpdated": {"date": "04/26/2025", "time": "4:58 PM"}, "user": "MH", "dateSent": "08/10/2025", "dateSentOrig": "08/24/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Dermatology Associates", "id": "PROV-3935"}}, {"id": "CLAIM-000205", "patient": {"name": "<PERSON>", "id": "PAT-369936"}, "serviceDate": "05/16/2025", "insuranceCarrier": {"carrierName": "MetLife", "planCategory": "Primary"}, "amount": "$5338.09", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "06/19/2025", "time": "11:09 PM"}, "user": "MP", "dateSent": "06/05/2025", "dateSentOrig": "07/09/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Radiology Associates", "id": "PROV-1345"}}, {"id": "CLAIM-000206", "patient": {"name": "<PERSON>", "id": "PAT-818024"}, "serviceDate": "08/30/2025", "insuranceCarrier": {"carrierName": "Liberty Mutual", "planCategory": "Primary"}, "amount": "$7139.75", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "08/30/2025", "time": "9:04 PM"}, "user": "JL", "dateSent": "08/11/2025", "dateSentOrig": "08/02/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Psychiatry Associates", "id": "PROV-1491"}}, {"id": "CLAIM-000207", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-216164"}, "serviceDate": "06/07/2025", "insuranceCarrier": {"carrierName": "UnitedHealth", "planCategory": "Secondary"}, "amount": "$7087.60", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "05/05/2025", "time": "12:20 PM"}, "user": "JT", "dateSent": "07/12/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Primary Care Associates", "id": "PROV-1870"}}, {"id": "CLAIM-000208", "patient": {"name": "<PERSON>", "id": "PAT-520948"}, "serviceDate": "04/26/2025", "insuranceCarrier": {"carrierName": "State Farm", "planCategory": "Primary"}, "amount": "$430.92", "status": "DENIED", "lastUpdated": {"date": "07/22/2025", "time": "11:25 PM"}, "user": "JT", "dateSent": "", "dateSentOrig": "06/25/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "OB/GYN Associates", "id": "PROV-9658"}}, {"id": "CLAIM-000209", "patient": {"name": "<PERSON><PERSON><PERSON>", "id": "PAT-351107"}, "serviceDate": "05/27/2025", "insuranceCarrier": {"carrierName": "Kaiser Permanente", "planCategory": "Secondary"}, "amount": "$848.12", "status": "PENDING", "lastUpdated": {"date": "04/03/2025", "time": "6:30 AM"}, "user": "EG", "dateSent": "", "dateSentOrig": "", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Neurology Associates", "id": "PROV-6137"}}, {"id": "CLAIM-000210", "patient": {"name": "<PERSON>", "id": "PAT-112776"}, "serviceDate": "03/07/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Primary"}, "amount": "$10401.31", "status": "DENIED", "lastUpdated": {"date": "03/07/2025", "time": "4:32 PM"}, "user": "GW", "dateSent": "", "dateSentOrig": "06/23/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Dermatology Associates", "id": "PROV-5894"}}, {"id": "CLAIM-000211", "patient": {"name": "<PERSON>", "id": "PAT-446064"}, "serviceDate": "06/07/2025", "insuranceCarrier": {"carrierName": "Humana", "planCategory": "Primary"}, "amount": "$11732.16", "status": "PENDING", "lastUpdated": {"date": "06/18/2025", "time": "5:48 AM"}, "user": "DG", "dateSent": "07/01/2025", "dateSentOrig": "05/26/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "OB/GYN Associates", "id": "PROV-6140"}}, {"id": "CLAIM-000212", "patient": {"name": "<PERSON>", "id": "PAT-183204"}, "serviceDate": "03/31/2025", "insuranceCarrier": {"carrierName": "Kaiser Permanente", "planCategory": "Primary"}, "amount": "$13009.23", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "03/31/2025", "time": "12:14 AM"}, "user": "SK", "dateSent": "07/08/2025", "dateSentOrig": "07/17/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Primary Care Associates", "id": "PROV-1443"}}, {"id": "CLAIM-000213", "patient": {"name": "<PERSON>", "id": "PAT-977413"}, "serviceDate": "05/09/2025", "insuranceCarrier": {"carrierName": "Kaiser Permanente", "planCategory": "Primary"}, "amount": "$8658.76", "status": "PROCESSING", "lastUpdated": {"date": "07/07/2025", "time": "9:36 AM"}, "user": "MM", "dateSent": "", "dateSentOrig": "08/05/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Primary Care Associates", "id": "PROV-8594"}}, {"id": "CLAIM-000214", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-658211"}, "serviceDate": "04/15/2025", "insuranceCarrier": {"carrierName": "Allstate", "planCategory": "Primary"}, "amount": "$4363.67", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "05/19/2025", "time": "9:13 AM"}, "user": "GW", "dateSent": "03/19/2025", "dateSentOrig": "04/23/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Psychiatry Associates", "id": "PROV-7999"}}, {"id": "CLAIM-000215", "patient": {"name": "<PERSON>", "id": "PAT-108844"}, "serviceDate": "08/05/2025", "insuranceCarrier": {"carrierName": "Kaiser Permanente", "planCategory": "Primary"}, "amount": "$10408.33", "status": "DENIED", "lastUpdated": {"date": "07/30/2025", "time": "4:26 PM"}, "user": "MF", "dateSent": "08/18/2025", "dateSentOrig": "07/20/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Psychiatry Associates", "id": "PROV-3471"}}, {"id": "CLAIM-000216", "patient": {"name": "<PERSON>", "id": "PAT-146048"}, "serviceDate": "05/05/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Primary"}, "amount": "$4670.77", "status": "PAID", "lastUpdated": {"date": "05/03/2025", "time": "4:27 AM"}, "user": "TQ", "dateSent": "07/24/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Surgery Associates", "id": "PROV-2025"}}, {"id": "CLAIM-000217", "patient": {"name": "<PERSON>", "id": "PAT-675066"}, "serviceDate": "08/04/2025", "insuranceCarrier": {"carrierName": "Anthem", "planCategory": "Primary"}, "amount": "$4407.51", "status": "PENDING", "lastUpdated": {"date": "07/09/2025", "time": "10:09 AM"}, "user": "BL", "dateSent": "03/26/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Pediatrics Associates", "id": "PROV-8453"}}, {"id": "CLAIM-000218", "patient": {"name": "<PERSON>", "id": "PAT-446552"}, "serviceDate": "03/20/2025", "insuranceCarrier": {"carrierName": "Farmers", "planCategory": "Secondary"}, "amount": "$12528.74", "status": "DENIED", "lastUpdated": {"date": "07/17/2025", "time": "4:38 AM"}, "user": "ZG", "dateSent": "", "dateSentOrig": "07/10/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Emergency Medicine Associates", "id": "PROV-8527"}}, {"id": "CLAIM-000219", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-685168"}, "serviceDate": "08/29/2025", "insuranceCarrier": {"carrierName": "Farmers", "planCategory": "Secondary"}, "amount": "$6600.98", "status": "PAID", "lastUpdated": {"date": "08/02/2025", "time": "9:27 AM"}, "user": "HW", "dateSent": "05/01/2025", "dateSentOrig": "03/18/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "OB/GYN Associates", "id": "PROV-4240"}}, {"id": "CLAIM-000220", "patient": {"name": "<PERSON>", "id": "PAT-212726"}, "serviceDate": "05/09/2025", "insuranceCarrier": {"carrierName": "State Farm", "planCategory": "Primary"}, "amount": "$8975.29", "status": "PROCESSING", "lastUpdated": {"date": "05/09/2025", "time": "6:27 AM"}, "user": "MF", "dateSent": "08/04/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Dermatology Associates", "id": "PROV-5955"}}, {"id": "CLAIM-000221", "patient": {"name": "Dr. <PERSON>", "id": "PAT-408155"}, "serviceDate": "03/25/2025", "insuranceCarrier": {"carrierName": "Allstate", "planCategory": "Primary"}, "amount": "$11355.78", "status": "PENDING", "lastUpdated": {"date": "04/09/2025", "time": "3:03 PM"}, "user": "RB", "dateSent": "08/01/2025", "dateSentOrig": "05/08/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Radiology Associates", "id": "PROV-3467"}}, {"id": "CLAIM-000222", "patient": {"name": "<PERSON>", "id": "PAT-417232"}, "serviceDate": "07/17/2025", "insuranceCarrier": {"carrierName": "Anthem", "planCategory": "Primary"}, "amount": "$10151.03", "status": "PROCESSING", "lastUpdated": {"date": "07/17/2025", "time": "8:11 AM"}, "user": "RM", "dateSent": "", "dateSentOrig": "", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Pediatrics Associates", "id": "PROV-1505"}}, {"id": "CLAIM-000223", "patient": {"name": "<PERSON>", "id": "PAT-621813"}, "serviceDate": "08/07/2025", "insuranceCarrier": {"carrierName": "Blue Cross Blue Shield", "planCategory": "Secondary"}, "amount": "$2658.95", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "08/18/2025", "time": "3:08 PM"}, "user": "JC", "dateSent": "04/17/2025", "dateSentOrig": "07/07/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Radiology Associates", "id": "PROV-7793"}}, {"id": "CLAIM-000224", "patient": {"name": "<PERSON>", "id": "PAT-608134"}, "serviceDate": "05/26/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Secondary"}, "amount": "$4003.64", "status": "PROCESSING", "lastUpdated": {"date": "07/03/2025", "time": "11:10 PM"}, "user": "JP", "dateSent": "04/09/2025", "dateSentOrig": "03/23/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Cardiology Associates", "id": "PROV-1941"}}, {"id": "CLAIM-000225", "patient": {"name": "<PERSON>", "id": "PAT-250163"}, "serviceDate": "07/28/2025", "insuranceCarrier": {"carrierName": "Nationwide", "planCategory": "Secondary"}, "amount": "$5327.12", "status": "DENIED", "lastUpdated": {"date": "07/06/2025", "time": "9:37 AM"}, "user": "AT", "dateSent": "08/03/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Cardiology Associates", "id": "PROV-7225"}}, {"id": "CLAIM-000226", "patient": {"name": "<PERSON>", "id": "PAT-802629"}, "serviceDate": "08/30/2025", "insuranceCarrier": {"carrierName": "Nationwide", "planCategory": "Primary"}, "amount": "$9468.76", "status": "PENDING", "lastUpdated": {"date": "05/01/2025", "time": "10:56 PM"}, "user": "CR", "dateSent": "03/18/2025", "dateSentOrig": "08/18/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Radiology Associates", "id": "PROV-7489"}}, {"id": "CLAIM-000227", "patient": {"name": "Dr. <PERSON>", "id": "PAT-687053"}, "serviceDate": "07/14/2025", "insuranceCarrier": {"carrierName": "Kaiser Permanente", "planCategory": "Secondary"}, "amount": "$1634.07", "status": "PAID", "lastUpdated": {"date": "07/14/2025", "time": "4:49 PM"}, "user": "CO", "dateSent": "", "dateSentOrig": "", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Psychiatry Associates", "id": "PROV-8155"}}, {"id": "CLAIM-000228", "patient": {"name": "<PERSON>", "id": "PAT-834360"}, "serviceDate": "08/29/2025", "insuranceCarrier": {"carrierName": "Humana", "planCategory": "Primary"}, "amount": "$5149.83", "status": "PROCESSING", "lastUpdated": {"date": "05/20/2025", "time": "2:59 PM"}, "user": "EB", "dateSent": "06/19/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Oncology Associates", "id": "PROV-8586"}}, {"id": "CLAIM-000229", "patient": {"name": "<PERSON>", "id": "PAT-425386"}, "serviceDate": "07/12/2025", "insuranceCarrier": {"carrierName": "Allstate", "planCategory": "Secondary"}, "amount": "$7131.14", "status": "PROCESSING", "lastUpdated": {"date": "07/28/2025", "time": "8:53 AM"}, "user": "LJ", "dateSent": "03/23/2025", "dateSentOrig": "08/11/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Dermatology Associates", "id": "PROV-2903"}}, {"id": "CLAIM-000230", "patient": {"name": "<PERSON>-<PERSON><PERSON><PERSON>", "id": "PAT-786125"}, "serviceDate": "05/03/2025", "insuranceCarrier": {"carrierName": "Blue Cross Blue Shield", "planCategory": "Primary"}, "amount": "$324.09", "status": "PENDING", "lastUpdated": {"date": "06/08/2025", "time": "1:25 PM"}, "user": "IF", "dateSent": "", "dateSentOrig": "04/13/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Dermatology Associates", "id": "PROV-6652"}}, {"id": "CLAIM-000231", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-283830"}, "serviceDate": "05/20/2025", "insuranceCarrier": {"carrierName": "State Farm", "planCategory": "Primary"}, "amount": "$14934.79", "status": "PENDING", "lastUpdated": {"date": "06/16/2025", "time": "12:48 AM"}, "user": "MK", "dateSent": "05/29/2025", "dateSentOrig": "08/18/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Neurology Associates", "id": "PROV-9570"}}, {"id": "CLAIM-000232", "patient": {"name": "<PERSON>", "id": "PAT-502186"}, "serviceDate": "06/28/2025", "insuranceCarrier": {"carrierName": "GEICO", "planCategory": "Primary"}, "amount": "$2489.37", "status": "PENDING", "lastUpdated": {"date": "06/28/2025", "time": "9:25 PM"}, "user": "AO", "dateSent": "03/26/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Dermatology Associates", "id": "PROV-6832"}}, {"id": "CLAIM-000233", "patient": {"name": "<PERSON>", "id": "PAT-397823"}, "serviceDate": "07/14/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Secondary"}, "amount": "$5856.11", "status": "DENIED", "lastUpdated": {"date": "04/14/2025", "time": "3:16 AM"}, "user": "DT", "dateSent": "", "dateSentOrig": "05/06/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Primary Care Associates", "id": "PROV-7122"}}, {"id": "CLAIM-000234", "patient": {"name": "<PERSON>", "id": "PAT-378025"}, "serviceDate": "04/02/2025", "insuranceCarrier": {"carrierName": "Humana", "planCategory": "Secondary"}, "amount": "$12168.76", "status": "PENDING", "lastUpdated": {"date": "04/02/2025", "time": "5:46 AM"}, "user": "SB", "dateSent": "", "dateSentOrig": "", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Surgery Associates", "id": "PROV-2808"}}, {"id": "CLAIM-000235", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-618213"}, "serviceDate": "08/06/2025", "insuranceCarrier": {"carrierName": "State Farm", "planCategory": "Primary"}, "amount": "$14406.50", "status": "PAID", "lastUpdated": {"date": "03/09/2025", "time": "7:55 PM"}, "user": "RL", "dateSent": "06/04/2025", "dateSentOrig": "07/08/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Psychiatry Associates", "id": "PROV-7886"}}, {"id": "CLAIM-000236", "patient": {"name": "<PERSON>", "id": "PAT-966104"}, "serviceDate": "04/02/2025", "insuranceCarrier": {"carrierName": "MetLife", "planCategory": "Primary"}, "amount": "$9411.82", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "04/02/2025", "time": "10:58 PM"}, "user": "JC", "dateSent": "06/13/2025", "dateSentOrig": "05/21/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Pediatrics Associates", "id": "PROV-8705"}}, {"id": "CLAIM-000237", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-660824"}, "serviceDate": "03/23/2025", "insuranceCarrier": {"carrierName": "Humana", "planCategory": "Secondary"}, "amount": "$3524.51", "status": "DENIED", "lastUpdated": {"date": "07/13/2025", "time": "11:07 PM"}, "user": "LK", "dateSent": "07/10/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Cardiology Associates", "id": "PROV-5555"}}, {"id": "CLAIM-000238", "patient": {"name": "Dr. <PERSON>", "id": "PAT-524712"}, "serviceDate": "08/22/2025", "insuranceCarrier": {"carrierName": "MetLife", "planCategory": "Secondary"}, "amount": "$12483.33", "status": "PENDING", "lastUpdated": {"date": "06/03/2025", "time": "7:14 PM"}, "user": "JB", "dateSent": "08/24/2025", "dateSentOrig": "04/06/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Oncology Associates", "id": "PROV-3763"}}, {"id": "CLAIM-000239", "patient": {"name": "<PERSON> III", "id": "PAT-332404"}, "serviceDate": "05/04/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Primary"}, "amount": "$11398.82", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "03/10/2025", "time": "6:46 AM"}, "user": "KR", "dateSent": "08/07/2025", "dateSentOrig": "08/11/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Neurology Associates", "id": "PROV-7504"}}, {"id": "CLAIM-000240", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-949389"}, "serviceDate": "08/16/2025", "insuranceCarrier": {"carrierName": "Farmers", "planCategory": "Primary"}, "amount": "$13851.21", "status": "PENDING", "lastUpdated": {"date": "07/11/2025", "time": "8:59 AM"}, "user": "MB", "dateSent": "09/02/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Surgery Associates", "id": "PROV-5587"}}, {"id": "CLAIM-000241", "patient": {"name": "<PERSON>", "id": "PAT-116226"}, "serviceDate": "04/03/2025", "insuranceCarrier": {"carrierName": "Anthem", "planCategory": "Primary"}, "amount": "$13999.57", "status": "DENIED", "lastUpdated": {"date": "04/03/2025", "time": "6:21 AM"}, "user": "SC", "dateSent": "06/02/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "OB/GYN Associates", "id": "PROV-6217"}}, {"id": "CLAIM-000242", "patient": {"name": "<PERSON>", "id": "PAT-222450"}, "serviceDate": "03/15/2025", "insuranceCarrier": {"carrierName": "Liberty Mutual", "planCategory": "Primary"}, "amount": "$4535.10", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "04/21/2025", "time": "6:24 PM"}, "user": "AB", "dateSent": "03/16/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Psychiatry Associates", "id": "PROV-5743"}}, {"id": "CLAIM-000243", "patient": {"name": "<PERSON>", "id": "PAT-973105"}, "serviceDate": "04/06/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Secondary"}, "amount": "$11825.83", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "04/02/2025", "time": "9:38 PM"}, "user": "TC", "dateSent": "08/05/2025", "dateSentOrig": "03/19/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Emergency Medicine Associates", "id": "PROV-4622"}}, {"id": "CLAIM-000244", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-146101"}, "serviceDate": "07/01/2025", "insuranceCarrier": {"carrierName": "MetLife", "planCategory": "Primary"}, "amount": "$6029.53", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "07/29/2025", "time": "2:21 AM"}, "user": "CQ", "dateSent": "05/11/2025", "dateSentOrig": "06/27/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Cardiology Associates", "id": "PROV-9873"}}, {"id": "CLAIM-000245", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-858830"}, "serviceDate": "05/16/2025", "insuranceCarrier": {"carrierName": "State Farm", "planCategory": "Secondary"}, "amount": "$13005.66", "status": "PROCESSING", "lastUpdated": {"date": "05/16/2025", "time": "2:32 PM"}, "user": "NC", "dateSent": "", "dateSentOrig": "", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Cardiology Associates", "id": "PROV-5174"}}, {"id": "CLAIM-000246", "patient": {"name": "<PERSON>", "id": "PAT-852662"}, "serviceDate": "05/06/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Primary"}, "amount": "$12776.77", "status": "PAID", "lastUpdated": {"date": "05/06/2025", "time": "11:30 PM"}, "user": "AN", "dateSent": "08/20/2025", "dateSentOrig": "06/13/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Radiology Associates", "id": "PROV-6874"}}, {"id": "CLAIM-000247", "patient": {"name": "<PERSON>", "id": "PAT-742246"}, "serviceDate": "05/11/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Secondary"}, "amount": "$13019.16", "status": "PENDING", "lastUpdated": {"date": "05/11/2025", "time": "6:08 AM"}, "user": "VS", "dateSent": "", "dateSentOrig": "03/21/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Emergency Medicine Associates", "id": "PROV-3803"}}, {"id": "CLAIM-000248", "patient": {"name": "<PERSON>", "id": "PAT-430606"}, "serviceDate": "05/25/2025", "insuranceCarrier": {"carrierName": "MetLife", "planCategory": "Secondary"}, "amount": "$5442.48", "status": "PENDING", "lastUpdated": {"date": "04/24/2025", "time": "4:56 PM"}, "user": "KB", "dateSent": "06/06/2025", "dateSentOrig": "05/10/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Primary Care Associates", "id": "PROV-2740"}}, {"id": "CLAIM-000249", "patient": {"name": "<PERSON>", "id": "PAT-557835"}, "serviceDate": "05/19/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Secondary"}, "amount": "$2544.34", "status": "PAID", "lastUpdated": {"date": "04/27/2025", "time": "3:38 PM"}, "user": "RB", "dateSent": "08/30/2025", "dateSentOrig": "07/12/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Primary Care Associates", "id": "PROV-5663"}}, {"id": "CLAIM-000250", "patient": {"name": "<PERSON>", "id": "PAT-158409"}, "serviceDate": "06/17/2025", "insuranceCarrier": {"carrierName": "Kaiser Permanente", "planCategory": "Primary"}, "amount": "$7060.32", "status": "PENDING", "lastUpdated": {"date": "05/10/2025", "time": "2:30 PM"}, "user": "MW", "dateSent": "", "dateSentOrig": "03/13/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Emergency Medicine Associates", "id": "PROV-8448"}}, {"id": "CLAIM-000251", "patient": {"name": "<PERSON>", "id": "PAT-665653"}, "serviceDate": "08/14/2025", "insuranceCarrier": {"carrierName": "Allstate", "planCategory": "Secondary"}, "amount": "$4214.51", "status": "PENDING", "lastUpdated": {"date": "05/20/2025", "time": "10:41 PM"}, "user": "DH", "dateSent": "08/08/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Pediatrics Associates", "id": "PROV-2831"}}, {"id": "CLAIM-000252", "patient": {"name": "<PERSON>.", "id": "PAT-417007"}, "serviceDate": "03/17/2025", "insuranceCarrier": {"carrierName": "Liberty Mutual", "planCategory": "Primary"}, "amount": "$3007.37", "status": "PENDING", "lastUpdated": {"date": "08/18/2025", "time": "10:30 PM"}, "user": "JG", "dateSent": "04/04/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Surgery Associates", "id": "PROV-3414"}}, {"id": "CLAIM-000253", "patient": {"name": "<PERSON>", "id": "PAT-597433"}, "serviceDate": "07/14/2025", "insuranceCarrier": {"carrierName": "Anthem", "planCategory": "Primary"}, "amount": "$7954.77", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "07/14/2025", "time": "1:35 PM"}, "user": "VB", "dateSent": "08/05/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Oncology Associates", "id": "PROV-3882"}}, {"id": "CLAIM-000254", "patient": {"name": "<PERSON>", "id": "PAT-717482"}, "serviceDate": "04/10/2025", "insuranceCarrier": {"carrierName": "Farmers", "planCategory": "Primary"}, "amount": "$12176.71", "status": "PAID", "lastUpdated": {"date": "03/19/2025", "time": "12:45 PM"}, "user": "RW", "dateSent": "", "dateSentOrig": "", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Psychiatry Associates", "id": "PROV-2053"}}, {"id": "CLAIM-000255", "patient": {"name": "<PERSON>", "id": "PAT-542197"}, "serviceDate": "03/29/2025", "insuranceCarrier": {"carrierName": "Farmers", "planCategory": "Primary"}, "amount": "$6379.97", "status": "DENIED", "lastUpdated": {"date": "04/29/2025", "time": "5:13 PM"}, "user": "BB", "dateSent": "07/25/2025", "dateSentOrig": "03/21/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Oncology Associates", "id": "PROV-1089"}}, {"id": "CLAIM-000256", "patient": {"name": "<PERSON>", "id": "PAT-804563"}, "serviceDate": "07/30/2025", "insuranceCarrier": {"carrierName": "Liberty Mutual", "planCategory": "Secondary"}, "amount": "$7639.47", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "07/30/2025", "time": "9:58 AM"}, "user": "DS", "dateSent": "03/18/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Primary Care Associates", "id": "PROV-8517"}}, {"id": "CLAIM-000257", "patient": {"name": "<PERSON>", "id": "PAT-325322"}, "serviceDate": "08/05/2025", "insuranceCarrier": {"carrierName": "Progressive", "planCategory": "Secondary"}, "amount": "$6912.63", "status": "DENIED", "lastUpdated": {"date": "07/29/2025", "time": "12:16 AM"}, "user": "AK", "dateSent": "05/25/2025", "dateSentOrig": "06/08/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Radiology Associates", "id": "PROV-1076"}}, {"id": "CLAIM-000258", "patient": {"name": "<PERSON>", "id": "PAT-391172"}, "serviceDate": "07/13/2025", "insuranceCarrier": {"carrierName": "Blue Cross Blue Shield", "planCategory": "Secondary"}, "amount": "$2343.75", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "06/15/2025", "time": "5:25 PM"}, "user": "AT", "dateSent": "07/12/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Orthopedics Associates", "id": "PROV-4334"}}, {"id": "CLAIM-000259", "patient": {"name": "<PERSON>", "id": "PAT-523630"}, "serviceDate": "08/12/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Primary"}, "amount": "$11862.94", "status": "PENDING", "lastUpdated": {"date": "05/14/2025", "time": "11:44 AM"}, "user": "BW", "dateSent": "06/10/2025", "dateSentOrig": "05/11/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Surgery Associates", "id": "PROV-5709"}}, {"id": "CLAIM-000260", "patient": {"name": "<PERSON>", "id": "PAT-323031"}, "serviceDate": "03/30/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Primary"}, "amount": "$10934.20", "status": "PAID", "lastUpdated": {"date": "06/05/2025", "time": "9:06 AM"}, "user": "JS", "dateSent": "07/30/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Psychiatry Associates", "id": "PROV-7295"}}, {"id": "CLAIM-000261", "patient": {"name": "<PERSON>", "id": "PAT-176862"}, "serviceDate": "03/31/2025", "insuranceCarrier": {"carrierName": "Kaiser Permanente", "planCategory": "Secondary"}, "amount": "$11917.17", "status": "PENDING", "lastUpdated": {"date": "03/31/2025", "time": "4:24 PM"}, "user": "SS", "dateSent": "08/09/2025", "dateSentOrig": "04/07/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Dermatology Associates", "id": "PROV-4761"}}, {"id": "CLAIM-000262", "patient": {"name": "<PERSON>", "id": "PAT-771806"}, "serviceDate": "05/03/2025", "insuranceCarrier": {"carrierName": "Nationwide", "planCategory": "Secondary"}, "amount": "$4996.71", "status": "DENIED", "lastUpdated": {"date": "03/30/2025", "time": "5:48 AM"}, "user": "AS", "dateSent": "04/21/2025", "dateSentOrig": "06/09/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Radiology Associates", "id": "PROV-3098"}}, {"id": "CLAIM-000263", "patient": {"name": "<PERSON>", "id": "PAT-165462"}, "serviceDate": "05/01/2025", "insuranceCarrier": {"carrierName": "GEICO", "planCategory": "Primary"}, "amount": "$10163.64", "status": "PROCESSING", "lastUpdated": {"date": "07/18/2025", "time": "8:21 PM"}, "user": "ER", "dateSent": "04/30/2025", "dateSentOrig": "08/01/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "OB/GYN Associates", "id": "PROV-5905"}}, {"id": "CLAIM-000264", "patient": {"name": "Miss <PERSON>", "id": "PAT-180405"}, "serviceDate": "08/16/2025", "insuranceCarrier": {"carrierName": "Kaiser Permanente", "planCategory": "Primary"}, "amount": "$4385.66", "status": "DENIED", "lastUpdated": {"date": "08/02/2025", "time": "6:35 AM"}, "user": "EW", "dateSent": "", "dateSentOrig": "04/16/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "OB/GYN Associates", "id": "PROV-4101"}}, {"id": "CLAIM-000265", "patient": {"name": "<PERSON>", "id": "PAT-201919"}, "serviceDate": "06/16/2025", "insuranceCarrier": {"carrierName": "Allstate", "planCategory": "Secondary"}, "amount": "$7169.72", "status": "PROCESSING", "lastUpdated": {"date": "03/20/2025", "time": "12:24 AM"}, "user": "KB", "dateSent": "05/26/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Psychiatry Associates", "id": "PROV-9992"}}, {"id": "CLAIM-000266", "patient": {"name": "<PERSON> DVM", "id": "PAT-309060"}, "serviceDate": "04/26/2025", "insuranceCarrier": {"carrierName": "Nationwide", "planCategory": "Primary"}, "amount": "$1714.78", "status": "PROCESSING", "lastUpdated": {"date": "04/05/2025", "time": "6:17 PM"}, "user": "JB", "dateSent": "06/05/2025", "dateSentOrig": "07/16/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Pediatrics Associates", "id": "PROV-9448"}}, {"id": "CLAIM-000267", "patient": {"name": "<PERSON>", "id": "PAT-520648"}, "serviceDate": "08/06/2025", "insuranceCarrier": {"carrierName": "UnitedHealth", "planCategory": "Secondary"}, "amount": "$4709.85", "status": "PAID", "lastUpdated": {"date": "03/17/2025", "time": "2:13 AM"}, "user": "HR", "dateSent": "05/05/2025", "dateSentOrig": "07/24/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Primary Care Associates", "id": "PROV-1115"}}, {"id": "CLAIM-000268", "patient": {"name": "<PERSON>", "id": "PAT-534002"}, "serviceDate": "05/31/2025", "insuranceCarrier": {"carrierName": "MetLife", "planCategory": "Secondary"}, "amount": "$14487.42", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "05/31/2025", "time": "3:12 PM"}, "user": "GB", "dateSent": "08/19/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Cardiology Associates", "id": "PROV-4299"}}, {"id": "CLAIM-000269", "patient": {"name": "<PERSON>", "id": "PAT-918499"}, "serviceDate": "04/04/2025", "insuranceCarrier": {"carrierName": "Blue Cross Blue Shield", "planCategory": "Primary"}, "amount": "$6746.55", "status": "PENDING", "lastUpdated": {"date": "04/16/2025", "time": "12:54 PM"}, "user": "AM", "dateSent": "08/17/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Primary Care Associates", "id": "PROV-9181"}}, {"id": "CLAIM-000270", "patient": {"name": "<PERSON>", "id": "PAT-962181"}, "serviceDate": "03/20/2025", "insuranceCarrier": {"carrierName": "MetLife", "planCategory": "Primary"}, "amount": "$9309.52", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "03/20/2025", "time": "6:11 AM"}, "user": "OR", "dateSent": "05/03/2025", "dateSentOrig": "08/23/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "OB/GYN Associates", "id": "PROV-2861"}}, {"id": "CLAIM-000271", "patient": {"name": "<PERSON>", "id": "PAT-542564"}, "serviceDate": "04/02/2025", "insuranceCarrier": {"carrierName": "Nationwide", "planCategory": "Secondary"}, "amount": "$79.75", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "04/17/2025", "time": "3:02 PM"}, "user": "DF", "dateSent": "", "dateSentOrig": "", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Oncology Associates", "id": "PROV-4744"}}, {"id": "CLAIM-000272", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-470751"}, "serviceDate": "04/11/2025", "insuranceCarrier": {"carrierName": "Farmers", "planCategory": "Primary"}, "amount": "$3173.88", "status": "PENDING", "lastUpdated": {"date": "03/13/2025", "time": "5:38 PM"}, "user": "LB", "dateSent": "03/30/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Oncology Associates", "id": "PROV-3855"}}, {"id": "CLAIM-000273", "patient": {"name": "<PERSON><PERSON><PERSON>", "id": "PAT-609690"}, "serviceDate": "06/04/2025", "insuranceCarrier": {"carrierName": "GEICO", "planCategory": "Primary"}, "amount": "$3449.34", "status": "PROCESSING", "lastUpdated": {"date": "07/20/2025", "time": "6:26 AM"}, "user": "RL", "dateSent": "07/11/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Psychiatry Associates", "id": "PROV-4597"}}, {"id": "CLAIM-000274", "patient": {"name": "<PERSON>", "id": "PAT-523734"}, "serviceDate": "06/30/2025", "insuranceCarrier": {"carrierName": "Blue Cross Blue Shield", "planCategory": "Secondary"}, "amount": "$933.72", "status": "PENDING", "lastUpdated": {"date": "05/24/2025", "time": "9:49 PM"}, "user": "AL", "dateSent": "", "dateSentOrig": "", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Pediatrics Associates", "id": "PROV-3728"}}, {"id": "CLAIM-000275", "patient": {"name": "<PERSON>", "id": "PAT-175041"}, "serviceDate": "05/30/2025", "insuranceCarrier": {"carrierName": "Nationwide", "planCategory": "Secondary"}, "amount": "$10013.88", "status": "PROCESSING", "lastUpdated": {"date": "06/26/2025", "time": "1:11 PM"}, "user": "OH", "dateSent": "03/06/2025", "dateSentOrig": "04/13/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Orthopedics Associates", "id": "PROV-8253"}}, {"id": "CLAIM-000276", "patient": {"name": "<PERSON>", "id": "PAT-772886"}, "serviceDate": "04/10/2025", "insuranceCarrier": {"carrierName": "Allstate", "planCategory": "Secondary"}, "amount": "$14394.90", "status": "DENIED", "lastUpdated": {"date": "06/03/2025", "time": "1:59 AM"}, "user": "CG", "dateSent": "06/20/2025", "dateSentOrig": "08/04/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Orthopedics Associates", "id": "PROV-5037"}}, {"id": "CLAIM-000277", "patient": {"name": "<PERSON>", "id": "PAT-756569"}, "serviceDate": "07/04/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Primary"}, "amount": "$7319.06", "status": "PROCESSING", "lastUpdated": {"date": "08/21/2025", "time": "6:29 AM"}, "user": "PT", "dateSent": "", "dateSentOrig": "", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Psychiatry Associates", "id": "PROV-7093"}}, {"id": "CLAIM-000278", "patient": {"name": "<PERSON><PERSON><PERSON>", "id": "PAT-428542"}, "serviceDate": "03/30/2025", "insuranceCarrier": {"carrierName": "Kaiser Permanente", "planCategory": "Secondary"}, "amount": "$1139.56", "status": "PENDING", "lastUpdated": {"date": "06/18/2025", "time": "8:33 PM"}, "user": "EK", "dateSent": "07/01/2025", "dateSentOrig": "07/02/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Radiology Associates", "id": "PROV-3788"}}, {"id": "CLAIM-000279", "patient": {"name": "<PERSON>", "id": "PAT-852014"}, "serviceDate": "03/23/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Primary"}, "amount": "$13349.00", "status": "PAID", "lastUpdated": {"date": "08/26/2025", "time": "8:15 AM"}, "user": "AT", "dateSent": "07/14/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Neurology Associates", "id": "PROV-2721"}}, {"id": "CLAIM-000280", "patient": {"name": "<PERSON>.", "id": "PAT-580632"}, "serviceDate": "08/17/2025", "insuranceCarrier": {"carrierName": "Nationwide", "planCategory": "Primary"}, "amount": "$7242.87", "status": "PAID", "lastUpdated": {"date": "05/04/2025", "time": "12:16 AM"}, "user": "MK", "dateSent": "", "dateSentOrig": "", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Radiology Associates", "id": "PROV-7021"}}, {"id": "CLAIM-000281", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-778432"}, "serviceDate": "08/24/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Primary"}, "amount": "$9276.75", "status": "DENIED", "lastUpdated": {"date": "08/27/2025", "time": "11:16 PM"}, "user": "JH", "dateSent": "07/16/2025", "dateSentOrig": "06/28/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Emergency Medicine Associates", "id": "PROV-7400"}}, {"id": "CLAIM-000282", "patient": {"name": "<PERSON>", "id": "PAT-774753"}, "serviceDate": "08/08/2025", "insuranceCarrier": {"carrierName": "Anthem", "planCategory": "Primary"}, "amount": "$14875.50", "status": "DENIED", "lastUpdated": {"date": "06/05/2025", "time": "3:24 PM"}, "user": "OW", "dateSent": "05/14/2025", "dateSentOrig": "06/23/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Psychiatry Associates", "id": "PROV-5377"}}, {"id": "CLAIM-000283", "patient": {"name": "<PERSON> DVM", "id": "PAT-432115"}, "serviceDate": "04/07/2025", "insuranceCarrier": {"carrierName": "Blue Cross Blue Shield", "planCategory": "Secondary"}, "amount": "$1711.32", "status": "PROCESSING", "lastUpdated": {"date": "04/21/2025", "time": "5:22 PM"}, "user": "JG", "dateSent": "04/02/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Psychiatry Associates", "id": "PROV-7432"}}, {"id": "CLAIM-000284", "patient": {"name": "<PERSON> DV<PERSON>", "id": "PAT-873392"}, "serviceDate": "08/18/2025", "insuranceCarrier": {"carrierName": "Anthem", "planCategory": "Secondary"}, "amount": "$6364.79", "status": "PAID", "lastUpdated": {"date": "03/15/2025", "time": "7:20 AM"}, "user": "LO", "dateSent": "08/01/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Cardiology Associates", "id": "PROV-5787"}}, {"id": "CLAIM-000285", "patient": {"name": "Mr. <PERSON>", "id": "PAT-338756"}, "serviceDate": "03/15/2025", "insuranceCarrier": {"carrierName": "Nationwide", "planCategory": "Primary"}, "amount": "$8296.26", "status": "PAID", "lastUpdated": {"date": "07/14/2025", "time": "6:15 AM"}, "user": "MS", "dateSent": "04/10/2025", "dateSentOrig": "06/02/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Neurology Associates", "id": "PROV-3886"}}, {"id": "CLAIM-000286", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-782546"}, "serviceDate": "06/30/2025", "insuranceCarrier": {"carrierName": "State Farm", "planCategory": "Secondary"}, "amount": "$827.30", "status": "PAID", "lastUpdated": {"date": "04/13/2025", "time": "7:32 AM"}, "user": "UR", "dateSent": "03/25/2025", "dateSentOrig": "04/04/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "OB/GYN Associates", "id": "PROV-2426"}}, {"id": "CLAIM-000287", "patient": {"name": "<PERSON>.", "id": "PAT-922354"}, "serviceDate": "05/11/2025", "insuranceCarrier": {"carrierName": "Liberty Mutual", "planCategory": "Primary"}, "amount": "$11844.63", "status": "PAID", "lastUpdated": {"date": "05/11/2025", "time": "7:18 AM"}, "user": "AB", "dateSent": "08/24/2025", "dateSentOrig": "06/10/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Oncology Associates", "id": "PROV-7325"}}, {"id": "CLAIM-000288", "patient": {"name": "<PERSON>", "id": "PAT-875462"}, "serviceDate": "04/10/2025", "insuranceCarrier": {"carrierName": "Farmers", "planCategory": "Secondary"}, "amount": "$5300.82", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "04/24/2025", "time": "8:32 PM"}, "user": "GK", "dateSent": "04/24/2025", "dateSentOrig": "05/30/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Emergency Medicine Associates", "id": "PROV-9465"}}, {"id": "CLAIM-000289", "patient": {"name": "Ms. <PERSON><PERSON><PERSON>", "id": "PAT-887826"}, "serviceDate": "03/14/2025", "insuranceCarrier": {"carrierName": "Progressive", "planCategory": "Primary"}, "amount": "$8120.65", "status": "PROCESSING", "lastUpdated": {"date": "05/13/2025", "time": "6:34 AM"}, "user": "SN", "dateSent": "06/23/2025", "dateSentOrig": "03/21/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Psychiatry Associates", "id": "PROV-4625"}}, {"id": "CLAIM-000290", "patient": {"name": "<PERSON>-<PERSON><PERSON><PERSON>", "id": "PAT-185265"}, "serviceDate": "03/22/2025", "insuranceCarrier": {"carrierName": "Farmers", "planCategory": "Primary"}, "amount": "$6371.53", "status": "DENIED", "lastUpdated": {"date": "07/30/2025", "time": "7:16 AM"}, "user": "CP", "dateSent": "03/18/2025", "dateSentOrig": "", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Psychiatry Associates", "id": "PROV-4640"}}, {"id": "CLAIM-000291", "patient": {"name": "<PERSON>", "id": "PAT-502019"}, "serviceDate": "04/14/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Secondary"}, "amount": "$7291.70", "status": "PENDING", "lastUpdated": {"date": "04/08/2025", "time": "2:42 AM"}, "user": "MJ", "dateSent": "05/12/2025", "dateSentOrig": "05/01/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Radiology Associates", "id": "PROV-6992"}}, {"id": "CLAIM-000292", "patient": {"name": "<PERSON>", "id": "PAT-304780"}, "serviceDate": "05/28/2025", "insuranceCarrier": {"carrierName": "UnitedHealth", "planCategory": "Primary"}, "amount": "$9597.96", "status": "PAID", "lastUpdated": {"date": "06/30/2025", "time": "5:42 PM"}, "user": "DB", "dateSent": "05/24/2025", "dateSentOrig": "06/23/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Pediatrics Associates", "id": "PROV-9990"}}, {"id": "CLAIM-000293", "patient": {"name": "<PERSON>", "id": "PAT-436100"}, "serviceDate": "05/18/2025", "insuranceCarrier": {"carrierName": "Humana", "planCategory": "Secondary"}, "amount": "$9770.56", "status": "DENIED", "lastUpdated": {"date": "08/01/2025", "time": "1:52 PM"}, "user": "GO", "dateSent": "08/10/2025", "dateSentOrig": "05/07/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Cardiology Associates", "id": "PROV-4955"}}, {"id": "CLAIM-000294", "patient": {"name": "<PERSON>-<PERSON><PERSON>", "id": "PAT-457562"}, "serviceDate": "05/18/2025", "insuranceCarrier": {"carrierName": "Humana", "planCategory": "Secondary"}, "amount": "$12920.18", "status": "PAID", "lastUpdated": {"date": "08/06/2025", "time": "3:40 PM"}, "user": "RC", "dateSent": "08/24/2025", "dateSentOrig": "05/04/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Radiology Associates", "id": "PROV-9374"}}, {"id": "CLAIM-000295", "patient": {"name": "<PERSON>", "id": "PAT-315289"}, "serviceDate": "09/02/2025", "insuranceCarrier": {"carrierName": "GEICO", "planCategory": "Primary"}, "amount": "$5556.50", "status": "PENDING", "lastUpdated": {"date": "07/16/2025", "time": "5:36 PM"}, "user": "KG", "dateSent": "03/28/2025", "dateSentOrig": "06/04/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Pediatrics Associates", "id": "PROV-7311"}}, {"id": "CLAIM-000296", "patient": {"name": "<PERSON>", "id": "PAT-990295"}, "serviceDate": "05/09/2025", "insuranceCarrier": {"carrierName": "Blue Cross Blue Shield", "planCategory": "Primary"}, "amount": "$11698.49", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "05/09/2025", "time": "9:56 AM"}, "user": "MM", "dateSent": "04/09/2025", "dateSentOrig": "06/10/2025", "pmsSyncStatus": {"status": "Error", "description": "Failed to synchronize - manual review required", "isSynced": false}, "provider": {"name": "Surgery Associates", "id": "PROV-7619"}}, {"id": "CLAIM-000297", "patient": {"name": "<PERSON>", "id": "PAT-838577"}, "serviceDate": "03/27/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Primary"}, "amount": "$8145.94", "status": "PAID", "lastUpdated": {"date": "03/08/2025", "time": "3:34 PM"}, "user": "TO", "dateSent": "03/07/2025", "dateSentOrig": "06/11/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Emergency Medicine Associates", "id": "PROV-4757"}}, {"id": "CLAIM-000298", "patient": {"name": "<PERSON>.", "id": "PAT-991200"}, "serviceDate": "06/08/2025", "insuranceCarrier": {"carrierName": "<PERSON><PERSON><PERSON>", "planCategory": "Primary"}, "amount": "$14961.41", "status": "NCOF - RESUBMITTED", "lastUpdated": {"date": "04/07/2025", "time": "7:54 AM"}, "user": "MM", "dateSent": "04/13/2025", "dateSentOrig": "04/08/2025", "pmsSyncStatus": {"status": "Syncing", "description": "Currently synchronizing with PMS", "isSynced": false}, "provider": {"name": "Pediatrics Associates", "id": "PROV-4574"}}, {"id": "CLAIM-000299", "patient": {"name": "<PERSON><PERSON><PERSON>-Stoltenberg", "id": "PAT-241512"}, "serviceDate": "05/26/2025", "insuranceCarrier": {"carrierName": "Progressive", "planCategory": "Secondary"}, "amount": "$9767.83", "status": "PROCESSING", "lastUpdated": {"date": "08/16/2025", "time": "10:29 PM"}, "user": "EO", "dateSent": "06/27/2025", "dateSentOrig": "03/11/2025", "pmsSyncStatus": {"status": "Synced", "description": "Successfully synchronized with PMS", "isSynced": true}, "provider": {"name": "Oncology Associates", "id": "PROV-3994"}}, {"id": "CLAIM-000300", "patient": {"name": "<PERSON><PERSON>", "id": "PAT-632627"}, "serviceDate": "04/23/2025", "insuranceCarrier": {"carrierName": "Anthem", "planCategory": "Primary"}, "amount": "$1121.85", "status": "PROCESSING", "lastUpdated": {"date": "07/20/2025", "time": "9:40 AM"}, "user": "LM", "dateSent": "", "dateSentOrig": "05/10/2025", "pmsSyncStatus": {"status": "Not synced", "description": "Pending synchronization with PMS", "isSynced": false}, "provider": {"name": "Orthopedics Associates", "id": "PROV-6785"}}]}