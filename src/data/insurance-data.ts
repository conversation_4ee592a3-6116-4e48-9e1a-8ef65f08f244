// Auto-generated insurance claims data
// Generated on: 2025-09-02T15:57:33.757Z
// Total records: 300

import { ClaimRowData } from '../components/ClaimRow';

export const insuranceClaimsData: ClaimRowData[] = [
  {
    "patient": {
      "name": "<PERSON><PERSON><PERSON>",
      "id": "PAT-63215"
    },
    "serviceDate": "07/13/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$10135.06",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "5:01 PM"
    },
    "user": "ERF",
    "dateSent": "07/20/2025",
    "dateSentOrig": "07/18/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PM<PERSON>",
      "isSynced": true
    },
    "provider": {
      "name": "Jerde Medical Group",
      "id": "PROV-7971"
    }
  },
  {
    "patient": {
      "name": "<PERSON>",
      "id": "PAT-47808"
    },
    "serviceDate": "04/04/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$11866.96",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "1:46 AM"
    },
    "user": "CBR",
    "dateSent": "08/12/2025",
    "dateSentOrig": "06/22/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Koch Medical Group",
      "id": "PROV-9139"
    }
  },
  {
    "patient": {
      "name": "Nancy Herzog",
      "id": "PAT-44775"
    },
    "serviceDate": "08/10/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$3348.78",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "4:37 PM"
    },
    "user": "TW",
    "dateSent": "08/22/2025",
    "dateSentOrig": "08/22/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Schneider Associates",
      "id": "PROV-6273"
    }
  },
  {
    "patient": {
      "name": "Patricia Champlin",
      "id": "PAT-97680"
    },
    "serviceDate": "06/14/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$12699.70",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "3:06 PM"
    },
    "user": "ZS",
    "dateSent": "07/01/2025",
    "dateSentOrig": "07/01/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Rippin Clinic",
      "id": "PROV-1067"
    }
  },
  {
    "patient": {
      "name": "Rachel Runte",
      "id": "PAT-96826"
    },
    "serviceDate": "06/21/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$14988.38",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "12:12 AM"
    },
    "user": "JLH",
    "dateSent": "08/21/2025",
    "dateSentOrig": "08/21/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Wilderman Clinic",
      "id": "PROV-4697"
    }
  },
  {
    "patient": {
      "name": "Kathryn Keebler",
      "id": "PAT-56976"
    },
    "serviceDate": "05/07/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$10740.03",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "9:47 AM"
    },
    "user": "AQB",
    "dateSent": "07/11/2025",
    "dateSentOrig": "07/11/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Emard Associates",
      "id": "PROV-4845"
    }
  },
  {
    "patient": {
      "name": "Elias Douglas",
      "id": "PAT-30659"
    },
    "serviceDate": "05/11/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$11045.68",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "8:37 AM"
    },
    "user": "EW",
    "dateSent": "06/04/2025",
    "dateSentOrig": "05/23/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Goldner Medical Group",
      "id": "PROV-3415"
    }
  },
  {
    "patient": {
      "name": "Linda Stracke III",
      "id": "PAT-89060"
    },
    "serviceDate": "06/16/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$6246.97",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "2:30 PM"
    },
    "user": "CSR",
    "dateSent": "07/14/2025",
    "dateSentOrig": "06/17/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Barton Associates",
      "id": "PROV-9479"
    }
  },
  {
    "patient": {
      "name": "Mitchell Kessler",
      "id": "PAT-89625"
    },
    "serviceDate": "04/11/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$3113.40",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "1:27 PM"
    },
    "user": "ERS",
    "dateSent": "05/10/2025",
    "dateSentOrig": "04/29/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Bednar Medical Group",
      "id": "PROV-4208"
    }
  },
  {
    "patient": {
      "name": "Maggie Labadie",
      "id": "PAT-77491"
    },
    "serviceDate": "06/28/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$14039.86",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "11:17 AM"
    },
    "user": "EBR",
    "dateSent": "08/21/2025",
    "dateSentOrig": "08/21/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Hayes Clinic",
      "id": "PROV-4286"
    }
  },
  {
    "patient": {
      "name": "Tommy Williamson",
      "id": "PAT-79489"
    },
    "serviceDate": "07/22/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$2021.20",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "4:07 AM"
    },
    "user": "SO",
    "dateSent": "08/26/2025",
    "dateSentOrig": "08/01/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Schimmel Healthcare",
      "id": "PROV-3378"
    }
  },
  {
    "patient": {
      "name": "Dr. Johnathan Paucek",
      "id": "PAT-24752"
    },
    "serviceDate": "08/21/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$6077.48",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "1:00 PM"
    },
    "user": "MNR",
    "dateSent": "08/30/2025",
    "dateSentOrig": "08/30/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Predovic Associates",
      "id": "PROV-4975"
    }
  },
  {
    "patient": {
      "name": "Jeannie Armstrong",
      "id": "PAT-92760"
    },
    "serviceDate": "08/22/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$2814.73",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "6:26 PM"
    },
    "user": "SEL",
    "dateSent": "08/25/2025",
    "dateSentOrig": "08/25/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Smitham Healthcare",
      "id": "PROV-9096"
    }
  },
  {
    "patient": {
      "name": "Jeanette Pfannerstill IV",
      "id": "PAT-10587"
    },
    "serviceDate": "08/22/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$9782.02",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "9:23 PM"
    },
    "user": "CH",
    "dateSent": "08/30/2025",
    "dateSentOrig": "08/22/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Spencer-Mills Medical Group",
      "id": "PROV-3339"
    }
  },
  {
    "patient": {
      "name": "Pedro Pfeffer",
      "id": "PAT-67979"
    },
    "serviceDate": "07/13/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$12684.34",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "2:57 AM"
    },
    "user": "ALL",
    "dateSent": "08/08/2025",
    "dateSentOrig": "08/08/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Gorczany Associates",
      "id": "PROV-8149"
    }
  },
  {
    "patient": {
      "name": "Everett Keeling",
      "id": "PAT-52903"
    },
    "serviceDate": "04/17/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$3005.75",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "8:12 PM"
    },
    "user": "LLR",
    "dateSent": "04/18/2025",
    "dateSentOrig": "04/18/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Hansen Medical Group",
      "id": "PROV-6078"
    }
  },
  {
    "patient": {
      "name": "Aubrey Dickens",
      "id": "PAT-53564"
    },
    "serviceDate": "03/18/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$2324.56",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "6:16 PM"
    },
    "user": "JJF",
    "dateSent": "08/06/2025",
    "dateSentOrig": "05/12/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Marquardt Clinic",
      "id": "PROV-4490"
    }
  },
  {
    "patient": {
      "name": "Wilbert Reinger",
      "id": "PAT-95568"
    },
    "serviceDate": "08/16/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$13077.61",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "1:20 PM"
    },
    "user": "RA",
    "dateSent": "08/27/2025",
    "dateSentOrig": "08/24/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Keebler Clinic",
      "id": "PROV-4044"
    }
  },
  {
    "patient": {
      "name": "Mark Carroll-Robel",
      "id": "PAT-91405"
    },
    "serviceDate": "07/04/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$13920.98",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "11:32 AM"
    },
    "user": "NLY",
    "dateSent": "08/18/2025",
    "dateSentOrig": "08/18/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Huels Associates",
      "id": "PROV-5668"
    }
  },
  {
    "patient": {
      "name": "Rogelio Deckow",
      "id": "PAT-58983"
    },
    "serviceDate": "06/10/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$6211.06",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "1:41 AM"
    },
    "user": "MPC",
    "dateSent": "08/27/2025",
    "dateSentOrig": "08/27/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Auer Clinic",
      "id": "PROV-4127"
    }
  },
  {
    "patient": {
      "name": "Rufus Rosenbaum",
      "id": "PAT-19749"
    },
    "serviceDate": "08/26/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$12120.43",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "6:25 AM"
    },
    "user": "AKK",
    "dateSent": "08/27/2025",
    "dateSentOrig": "08/27/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Becker Clinic",
      "id": "PROV-9614"
    }
  },
  {
    "patient": {
      "name": "Glen Hudson",
      "id": "PAT-23489"
    },
    "serviceDate": "08/17/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$5302.69",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "7:20 PM"
    },
    "user": "BK",
    "dateSent": "08/24/2025",
    "dateSentOrig": "08/24/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Jakubowski Family Practice",
      "id": "PROV-8967"
    }
  },
  {
    "patient": {
      "name": "Gail Mann",
      "id": "PAT-23397"
    },
    "serviceDate": "04/26/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$2594.85",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "4:49 PM"
    },
    "user": "YTO",
    "dateSent": "05/02/2025",
    "dateSentOrig": "05/02/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Stiedemann Medical Group",
      "id": "PROV-3030"
    }
  },
  {
    "patient": {
      "name": "Sandra Lueilwitz",
      "id": "PAT-79134"
    },
    "serviceDate": "04/04/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$7960.55",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "9:06 PM"
    },
    "user": "MBO",
    "dateSent": "05/08/2025",
    "dateSentOrig": "04/09/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Baumbach Healthcare",
      "id": "PROV-9339"
    }
  },
  {
    "patient": {
      "name": "Ella Cremin",
      "id": "PAT-10963"
    },
    "serviceDate": "03/20/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$11486.23",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "1:19 PM"
    },
    "user": "LW",
    "dateSent": "08/18/2025",
    "dateSentOrig": "03/22/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Gutkowski Clinic",
      "id": "PROV-7875"
    }
  },
  {
    "patient": {
      "name": "Tricia Mueller",
      "id": "PAT-11481"
    },
    "serviceDate": "06/06/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$5353.22",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "11:26 PM"
    },
    "user": "DR",
    "dateSent": "08/28/2025",
    "dateSentOrig": "08/28/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Corwin Family Practice",
      "id": "PROV-1032"
    }
  },
  {
    "patient": {
      "name": "Dr. Carlos Corwin",
      "id": "PAT-55887"
    },
    "serviceDate": "05/11/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$8628.39",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "3:22 PM"
    },
    "user": "SEE",
    "dateSent": "08/30/2025",
    "dateSentOrig": "07/26/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Blick Medical Group",
      "id": "PROV-1970"
    }
  },
  {
    "patient": {
      "name": "Dr. Julia Powlowski",
      "id": "PAT-29667"
    },
    "serviceDate": "03/20/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$11751.80",
    "status": "PENDING",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "10:10 PM"
    },
    "user": "MPP",
    "dateSent": "05/22/2025",
    "dateSentOrig": "05/22/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Schuster Family Practice",
      "id": "PROV-3975"
    }
  },
  {
    "patient": {
      "name": "Derrick Jacobi",
      "id": "PAT-40461"
    },
    "serviceDate": "07/22/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$3448.79",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "1:20 PM"
    },
    "user": "LPP",
    "dateSent": "08/31/2025",
    "dateSentOrig": "08/31/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Trantow Medical Group",
      "id": "PROV-2464"
    }
  },
  {
    "patient": {
      "name": "Geraldine Gleason",
      "id": "PAT-89347"
    },
    "serviceDate": "07/31/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$13215.78",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/13/2025",
      "time": "2:20 PM"
    },
    "user": "DJC",
    "dateSent": "08/07/2025",
    "dateSentOrig": "08/07/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Murphy Clinic",
      "id": "PROV-1495"
    }
  },
  {
    "patient": {
      "name": "Mrs. Louise Blick",
      "id": "PAT-73199"
    },
    "serviceDate": "07/25/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$4472.13",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "11:43 AM"
    },
    "user": "FB",
    "dateSent": "08/11/2025",
    "dateSentOrig": "07/25/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Padberg Medical Group",
      "id": "PROV-5704"
    }
  },
  {
    "patient": {
      "name": "Frederick Kuhlman",
      "id": "PAT-57314"
    },
    "serviceDate": "07/13/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$9865.39",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "10:15 AM"
    },
    "user": "JBT",
    "dateSent": "08/31/2025",
    "dateSentOrig": "08/31/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Kirlin Associates",
      "id": "PROV-8499"
    }
  },
  {
    "patient": {
      "name": "Mitchell Schultz",
      "id": "PAT-15038"
    },
    "serviceDate": "07/29/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$11065.67",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "5:27 AM"
    },
    "user": "LO",
    "dateSent": "08/29/2025",
    "dateSentOrig": "08/09/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Denesik Family Practice",
      "id": "PROV-8127"
    }
  },
  {
    "patient": {
      "name": "Terrance Ullrich",
      "id": "PAT-68251"
    },
    "serviceDate": "07/15/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$8948.96",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "9:48 PM"
    },
    "user": "DJ",
    "dateSent": "08/08/2025",
    "dateSentOrig": "08/08/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Wisoky Healthcare",
      "id": "PROV-1611"
    }
  },
  {
    "patient": {
      "name": "Dr. Jonathon Marks-Schultz",
      "id": "PAT-88475"
    },
    "serviceDate": "07/07/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$12009.28",
    "status": "DENIED",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "2:52 AM"
    },
    "user": "SAR",
    "dateSent": "07/14/2025",
    "dateSentOrig": "07/14/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Cole Clinic",
      "id": "PROV-9448"
    }
  },
  {
    "patient": {
      "name": "Richard Reynolds",
      "id": "PAT-89734"
    },
    "serviceDate": "06/15/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$9631.16",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "4:33 PM"
    },
    "user": "LM",
    "dateSent": "08/15/2025",
    "dateSentOrig": "08/15/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Hegmann Clinic",
      "id": "PROV-7152"
    }
  },
  {
    "patient": {
      "name": "Clifton Ullrich",
      "id": "PAT-18358"
    },
    "serviceDate": "05/18/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$8821.61",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "5:25 AM"
    },
    "user": "CS",
    "dateSent": "09/01/2025",
    "dateSentOrig": "05/31/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Borer Clinic",
      "id": "PROV-3011"
    }
  },
  {
    "patient": {
      "name": "Bridget Klocko",
      "id": "PAT-35980"
    },
    "serviceDate": "03/26/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$8547.67",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "7:52 AM"
    },
    "user": "ESW",
    "dateSent": "05/30/2025",
    "dateSentOrig": "05/30/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Kris Associates",
      "id": "PROV-3046"
    }
  },
  {
    "patient": {
      "name": "Allison Schiller",
      "id": "PAT-74082"
    },
    "serviceDate": "07/05/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$13970.98",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "3:09 PM"
    },
    "user": "DF",
    "dateSent": "07/19/2025",
    "dateSentOrig": "07/07/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Kirlin Family Practice",
      "id": "PROV-7679"
    }
  },
  {
    "patient": {
      "name": "Leona Kris V",
      "id": "PAT-76294"
    },
    "serviceDate": "08/22/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$8745.68",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/04/2025",
      "time": "8:03 PM"
    },
    "user": "FAM",
    "dateSent": "09/01/2025",
    "dateSentOrig": "08/27/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Fisher Associates",
      "id": "PROV-9085"
    }
  },
  {
    "patient": {
      "name": "Audrey Kihn",
      "id": "PAT-60377"
    },
    "serviceDate": "05/02/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$12893.41",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "5:23 PM"
    },
    "user": "VO",
    "dateSent": "05/29/2025",
    "dateSentOrig": "05/09/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Ondricka Healthcare",
      "id": "PROV-1149"
    }
  },
  {
    "patient": {
      "name": "Aaron Baumbach",
      "id": "PAT-15131"
    },
    "serviceDate": "05/11/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$535.60",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "7:26 PM"
    },
    "user": "MGM",
    "dateSent": "07/30/2025",
    "dateSentOrig": "06/26/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Bergnaum Medical Group",
      "id": "PROV-3201"
    }
  },
  {
    "patient": {
      "name": "Teri Kilback",
      "id": "PAT-34488"
    },
    "serviceDate": "07/23/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$3983.84",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "7:23 PM"
    },
    "user": "ESM",
    "dateSent": "08/20/2025",
    "dateSentOrig": "08/20/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "O'Hara Healthcare",
      "id": "PROV-6475"
    }
  },
  {
    "patient": {
      "name": "Alberta Hills",
      "id": "PAT-86201"
    },
    "serviceDate": "07/01/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$8100.03",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "1:50 AM"
    },
    "user": "PR",
    "dateSent": "07/23/2025",
    "dateSentOrig": "07/23/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Skiles Family Practice",
      "id": "PROV-7303"
    }
  },
  {
    "patient": {
      "name": "Gabriel Cremin-Bogisich DDS",
      "id": "PAT-63359"
    },
    "serviceDate": "07/15/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$2900.82",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "2:05 AM"
    },
    "user": "NM",
    "dateSent": "08/31/2025",
    "dateSentOrig": "08/31/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Homenick Clinic",
      "id": "PROV-5422"
    }
  },
  {
    "patient": {
      "name": "Eunice Harvey IV",
      "id": "PAT-94309"
    },
    "serviceDate": "08/14/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$12856.95",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "7:10 PM"
    },
    "user": "SKW",
    "dateSent": "08/17/2025",
    "dateSentOrig": "08/16/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Bartoletti Medical Group",
      "id": "PROV-1059"
    }
  },
  {
    "patient": {
      "name": "Leon Krajcik-Bernier Jr.",
      "id": "PAT-92132"
    },
    "serviceDate": "05/17/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$9373.87",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "6:00 PM"
    },
    "user": "DPB",
    "dateSent": "08/14/2025",
    "dateSentOrig": "05/25/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Schiller Associates",
      "id": "PROV-5051"
    }
  },
  {
    "patient": {
      "name": "Mr. Brad Pagac",
      "id": "PAT-36776"
    },
    "serviceDate": "05/07/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$12918.63",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "2:53 AM"
    },
    "user": "DO",
    "dateSent": "07/17/2025",
    "dateSentOrig": "05/21/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Harber Family Practice",
      "id": "PROV-8480"
    }
  },
  {
    "patient": {
      "name": "Grace Kunde I",
      "id": "PAT-74509"
    },
    "serviceDate": "04/17/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$886.80",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "9:00 AM"
    },
    "user": "OBC",
    "dateSent": "04/26/2025",
    "dateSentOrig": "04/22/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Sauer Clinic",
      "id": "PROV-9998"
    }
  },
  {
    "patient": {
      "name": "Francis Wyman",
      "id": "PAT-34603"
    },
    "serviceDate": "04/02/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$6933.10",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "7:52 PM"
    },
    "user": "EJ",
    "dateSent": "07/14/2025",
    "dateSentOrig": "05/02/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Waters Medical Group",
      "id": "PROV-8103"
    }
  },
  {
    "patient": {
      "name": "Joanne Funk-Kohler",
      "id": "PAT-62392"
    },
    "serviceDate": "04/30/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$11458.20",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "6:24 AM"
    },
    "user": "MG",
    "dateSent": "06/02/2025",
    "dateSentOrig": "05/20/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Thompson Associates",
      "id": "PROV-6536"
    }
  },
  {
    "patient": {
      "name": "Elbert Rogahn",
      "id": "PAT-50851"
    },
    "serviceDate": "07/31/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$9987.47",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "4:08 PM"
    },
    "user": "FK",
    "dateSent": "08/15/2025",
    "dateSentOrig": "08/15/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Ledner Clinic",
      "id": "PROV-3850"
    }
  },
  {
    "patient": {
      "name": "Sarah Sanford",
      "id": "PAT-10043"
    },
    "serviceDate": "04/16/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$741.37",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "2:20 PM"
    },
    "user": "RMD",
    "dateSent": "05/09/2025",
    "dateSentOrig": "05/07/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Berge Associates",
      "id": "PROV-5877"
    }
  },
  {
    "patient": {
      "name": "Tony Wiza",
      "id": "PAT-15121"
    },
    "serviceDate": "07/11/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$9517.97",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "11:42 PM"
    },
    "user": "EL",
    "dateSent": "08/18/2025",
    "dateSentOrig": "07/31/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Kulas Associates",
      "id": "PROV-4750"
    }
  },
  {
    "patient": {
      "name": "Albert Kunde",
      "id": "PAT-37647"
    },
    "serviceDate": "07/26/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$150.51",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "10:26 AM"
    },
    "user": "DRB",
    "dateSent": "09/01/2025",
    "dateSentOrig": "08/11/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Schuster Healthcare",
      "id": "PROV-3934"
    }
  },
  {
    "patient": {
      "name": "Samuel Shields",
      "id": "PAT-77649"
    },
    "serviceDate": "07/08/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$13977.91",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "7:47 PM"
    },
    "user": "LL",
    "dateSent": "07/21/2025",
    "dateSentOrig": "07/21/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Grimes Healthcare",
      "id": "PROV-1120"
    }
  },
  {
    "patient": {
      "name": "Cristina Haley",
      "id": "PAT-73059"
    },
    "serviceDate": "04/16/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$940.47",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "12:42 AM"
    },
    "user": "MNR",
    "dateSent": "08/27/2025",
    "dateSentOrig": "06/15/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Bins Family Practice",
      "id": "PROV-6190"
    }
  },
  {
    "patient": {
      "name": "Jon Price",
      "id": "PAT-29873"
    },
    "serviceDate": "08/04/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$6396.40",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "5:09 AM"
    },
    "user": "HRO",
    "dateSent": "08/26/2025",
    "dateSentOrig": "08/14/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Kautzer-Watsica Associates",
      "id": "PROV-7235"
    }
  },
  {
    "patient": {
      "name": "Irene Torp",
      "id": "PAT-21949"
    },
    "serviceDate": "08/29/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$1171.89",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "1:29 PM"
    },
    "user": "MSK",
    "dateSent": "09/02/2025",
    "dateSentOrig": "09/02/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Volkman Clinic",
      "id": "PROV-4687"
    }
  },
  {
    "patient": {
      "name": "Emanuel Fahey Sr.",
      "id": "PAT-87732"
    },
    "serviceDate": "06/19/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$14572.00",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "2:25 PM"
    },
    "user": "CAH",
    "dateSent": "08/12/2025",
    "dateSentOrig": "08/12/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Willms Associates",
      "id": "PROV-1697"
    }
  },
  {
    "patient": {
      "name": "Miss Lindsay Rath PhD",
      "id": "PAT-55231"
    },
    "serviceDate": "07/07/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$10824.07",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "5:30 AM"
    },
    "user": "TDL",
    "dateSent": "08/10/2025",
    "dateSentOrig": "08/10/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Koss Medical Group",
      "id": "PROV-5796"
    }
  },
  {
    "patient": {
      "name": "Danny Dare",
      "id": "PAT-68597"
    },
    "serviceDate": "04/08/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$4353.62",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "3:11 PM"
    },
    "user": "BS",
    "dateSent": "04/27/2025",
    "dateSentOrig": "04/27/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Carter Healthcare",
      "id": "PROV-7053"
    }
  },
  {
    "patient": {
      "name": "Cora Douglas",
      "id": "PAT-32085"
    },
    "serviceDate": "07/31/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$14673.13",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "4:25 PM"
    },
    "user": "QBM",
    "dateSent": "08/14/2025",
    "dateSentOrig": "08/08/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Schuppe Clinic",
      "id": "PROV-8662"
    }
  },
  {
    "patient": {
      "name": "Herman Farrell",
      "id": "PAT-94944"
    },
    "serviceDate": "04/16/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$7758.15",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "10:42 PM"
    },
    "user": "JGW",
    "dateSent": "05/21/2025",
    "dateSentOrig": "05/21/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Dicki Medical Group",
      "id": "PROV-7011"
    }
  },
  {
    "patient": {
      "name": "Ed Breitenberg",
      "id": "PAT-58738"
    },
    "serviceDate": "03/10/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$6681.00",
    "status": "PENDING",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "11:41 AM"
    },
    "user": "PJS",
    "dateSent": "03/14/2025",
    "dateSentOrig": "03/11/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Wisoky Associates",
      "id": "PROV-7505"
    }
  },
  {
    "patient": {
      "name": "Eva Corkery",
      "id": "PAT-83488"
    },
    "serviceDate": "04/16/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$4978.94",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "8:00 AM"
    },
    "user": "AP",
    "dateSent": "05/04/2025",
    "dateSentOrig": "04/24/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Dickens Associates",
      "id": "PROV-1179"
    }
  },
  {
    "patient": {
      "name": "Clifton Wilderman",
      "id": "PAT-40404"
    },
    "serviceDate": "06/13/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$10497.53",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "6:39 PM"
    },
    "user": "EL",
    "dateSent": "07/23/2025",
    "dateSentOrig": "07/23/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Wilderman Family Practice",
      "id": "PROV-9703"
    }
  },
  {
    "patient": {
      "name": "Sara Abshire-Gulgowski",
      "id": "PAT-89995"
    },
    "serviceDate": "07/22/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$3340.67",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/13/2025",
      "time": "3:14 PM"
    },
    "user": "BJJ",
    "dateSent": "07/30/2025",
    "dateSentOrig": "07/27/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Lind Medical Group",
      "id": "PROV-6824"
    }
  },
  {
    "patient": {
      "name": "Frank Reilly",
      "id": "PAT-97672"
    },
    "serviceDate": "06/21/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$1163.07",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "8:44 PM"
    },
    "user": "GRW",
    "dateSent": "07/07/2025",
    "dateSentOrig": "06/27/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Larson Associates",
      "id": "PROV-3799"
    }
  },
  {
    "patient": {
      "name": "Hilda Sanford",
      "id": "PAT-86346"
    },
    "serviceDate": "04/29/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$12332.52",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "1:06 AM"
    },
    "user": "KM",
    "dateSent": "08/13/2025",
    "dateSentOrig": "08/13/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Kuphal Family Practice",
      "id": "PROV-5743"
    }
  },
  {
    "patient": {
      "name": "Dwayne Huels",
      "id": "PAT-80907"
    },
    "serviceDate": "05/16/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$5347.03",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "12:26 AM"
    },
    "user": "JS",
    "dateSent": "09/02/2025",
    "dateSentOrig": "09/02/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Brown Medical Group",
      "id": "PROV-4286"
    }
  },
  {
    "patient": {
      "name": "Mr. Jerry Wisozk",
      "id": "PAT-74609"
    },
    "serviceDate": "06/13/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$6900.59",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "10:18 AM"
    },
    "user": "CF",
    "dateSent": "07/24/2025",
    "dateSentOrig": "07/11/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Stracke Medical Group",
      "id": "PROV-1678"
    }
  },
  {
    "patient": {
      "name": "Mr. Kurt Graham",
      "id": "PAT-58311"
    },
    "serviceDate": "04/10/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$1514.77",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "10:25 AM"
    },
    "user": "AKC",
    "dateSent": "07/08/2025",
    "dateSentOrig": "07/08/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Kessler-Johns Healthcare",
      "id": "PROV-2155"
    }
  },
  {
    "patient": {
      "name": "Constance Crist",
      "id": "PAT-46118"
    },
    "serviceDate": "04/04/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$1593.53",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "10:37 PM"
    },
    "user": "SQW",
    "dateSent": "05/02/2025",
    "dateSentOrig": "04/17/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Miller Associates",
      "id": "PROV-2548"
    }
  },
  {
    "patient": {
      "name": "Jana Wiegand",
      "id": "PAT-38950"
    },
    "serviceDate": "04/17/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$1257.58",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "12:59 PM"
    },
    "user": "RRD",
    "dateSent": "04/28/2025",
    "dateSentOrig": "04/26/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Oberbrunner Family Practice",
      "id": "PROV-2235"
    }
  },
  {
    "patient": {
      "name": "Lance Schamberger",
      "id": "PAT-23133"
    },
    "serviceDate": "06/24/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$14173.17",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "1:38 AM"
    },
    "user": "LNP",
    "dateSent": "07/06/2025",
    "dateSentOrig": "06/27/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Raynor Medical Group",
      "id": "PROV-4440"
    }
  },
  {
    "patient": {
      "name": "Frederick Rath",
      "id": "PAT-94719"
    },
    "serviceDate": "05/27/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$6518.16",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "9:50 AM"
    },
    "user": "ELL",
    "dateSent": "07/25/2025",
    "dateSentOrig": "07/25/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Rohan Medical Group",
      "id": "PROV-7777"
    }
  },
  {
    "patient": {
      "name": "Molly Mosciski",
      "id": "PAT-87547"
    },
    "serviceDate": "05/19/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$3456.91",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "1:00 PM"
    },
    "user": "SQT",
    "dateSent": "07/17/2025",
    "dateSentOrig": "07/17/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Glover Clinic",
      "id": "PROV-6231"
    }
  },
  {
    "patient": {
      "name": "Reginald Koelpin",
      "id": "PAT-65478"
    },
    "serviceDate": "05/11/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$7205.82",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "1:41 PM"
    },
    "user": "MCM",
    "dateSent": "08/19/2025",
    "dateSentOrig": "08/19/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Koepp Family Practice",
      "id": "PROV-6124"
    }
  },
  {
    "patient": {
      "name": "Julian Maggio",
      "id": "PAT-97365"
    },
    "serviceDate": "05/13/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$13975.75",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "11:37 PM"
    },
    "user": "RJ",
    "dateSent": "08/30/2025",
    "dateSentOrig": "08/30/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Ziemann Associates",
      "id": "PROV-4639"
    }
  },
  {
    "patient": {
      "name": "Mark Schroeder",
      "id": "PAT-47762"
    },
    "serviceDate": "08/28/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$10237.07",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "4:17 AM"
    },
    "user": "AJ",
    "dateSent": "08/30/2025",
    "dateSentOrig": "08/30/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Mertz Family Practice",
      "id": "PROV-7966"
    }
  },
  {
    "patient": {
      "name": "Nellie Schneider",
      "id": "PAT-87483"
    },
    "serviceDate": "04/22/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$12345.36",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/03/2025",
      "time": "5:52 AM"
    },
    "user": "LH",
    "dateSent": "05/02/2025",
    "dateSentOrig": "05/02/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Parker Clinic",
      "id": "PROV-7073"
    }
  },
  {
    "patient": {
      "name": "Eula Gusikowski",
      "id": "PAT-88080"
    },
    "serviceDate": "08/24/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$330.80",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "6:45 PM"
    },
    "user": "CNR",
    "dateSent": "08/28/2025",
    "dateSentOrig": "08/25/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Pouros Medical Group",
      "id": "PROV-6194"
    }
  },
  {
    "patient": {
      "name": "Kelly Streich MD",
      "id": "PAT-82374"
    },
    "serviceDate": "06/22/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$13460.90",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "7:45 PM"
    },
    "user": "GJ",
    "dateSent": "09/02/2025",
    "dateSentOrig": "07/01/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Hudson Medical Group",
      "id": "PROV-7606"
    }
  },
  {
    "patient": {
      "name": "Lynette Sporer",
      "id": "PAT-24165"
    },
    "serviceDate": "06/05/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$11053.97",
    "status": "PENDING",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "2:53 AM"
    },
    "user": "LK",
    "dateSent": "08/09/2025",
    "dateSentOrig": "06/08/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Mante Medical Group",
      "id": "PROV-8396"
    }
  },
  {
    "patient": {
      "name": "Dr. Guadalupe Schmidt III",
      "id": "PAT-51760"
    },
    "serviceDate": "04/21/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$10236.85",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "9:07 PM"
    },
    "user": "ZBT",
    "dateSent": "08/18/2025",
    "dateSentOrig": "06/28/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Heidenreich Associates",
      "id": "PROV-6525"
    }
  },
  {
    "patient": {
      "name": "Erma Shields",
      "id": "PAT-69212"
    },
    "serviceDate": "08/01/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$5809.85",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "5:49 AM"
    },
    "user": "DK",
    "dateSent": "08/13/2025",
    "dateSentOrig": "08/13/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Schaefer Family Practice",
      "id": "PROV-9432"
    }
  },
  {
    "patient": {
      "name": "Yolanda Prosacco",
      "id": "PAT-26542"
    },
    "serviceDate": "05/28/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$6839.59",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "4:02 PM"
    },
    "user": "EO",
    "dateSent": "06/22/2025",
    "dateSentOrig": "06/22/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Ankunding Healthcare",
      "id": "PROV-8471"
    }
  },
  {
    "patient": {
      "name": "Danielle Larkin-Stracke",
      "id": "PAT-29441"
    },
    "serviceDate": "08/10/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$76.49",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "3:44 PM"
    },
    "user": "RRB",
    "dateSent": "08/20/2025",
    "dateSentOrig": "08/20/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Schamberger Healthcare",
      "id": "PROV-7465"
    }
  },
  {
    "patient": {
      "name": "Curtis Larson",
      "id": "PAT-14494"
    },
    "serviceDate": "09/01/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$3237.52",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "12:26 AM"
    },
    "user": "EP",
    "dateSent": "09/01/2025",
    "dateSentOrig": "09/01/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Davis Associates",
      "id": "PROV-3022"
    }
  },
  {
    "patient": {
      "name": "Ruby Nitzsche-Ryan",
      "id": "PAT-47420"
    },
    "serviceDate": "08/12/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$9342.55",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "5:18 PM"
    },
    "user": "IS",
    "dateSent": "08/17/2025",
    "dateSentOrig": "08/14/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Ledner Associates",
      "id": "PROV-1223"
    }
  },
  {
    "patient": {
      "name": "Miss Marilyn Homenick DVM",
      "id": "PAT-41374"
    },
    "serviceDate": "04/18/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$4141.46",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "8:26 AM"
    },
    "user": "TM",
    "dateSent": "04/25/2025",
    "dateSentOrig": "04/25/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Heaney Clinic",
      "id": "PROV-4214"
    }
  },
  {
    "patient": {
      "name": "Ms. Sue Mayer",
      "id": "PAT-44899"
    },
    "serviceDate": "03/29/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$3964.76",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "3:46 PM"
    },
    "user": "FNM",
    "dateSent": "07/21/2025",
    "dateSentOrig": "07/05/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Doyle Family Practice",
      "id": "PROV-9502"
    }
  },
  {
    "patient": {
      "name": "Agnes Schuppe",
      "id": "PAT-61324"
    },
    "serviceDate": "05/26/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$4858.60",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "5:30 AM"
    },
    "user": "CM",
    "dateSent": "06/29/2025",
    "dateSentOrig": "06/20/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Stokes Medical Group",
      "id": "PROV-7919"
    }
  },
  {
    "patient": {
      "name": "Kristina Volkman",
      "id": "PAT-61342"
    },
    "serviceDate": "03/27/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$9270.51",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "3:59 PM"
    },
    "user": "AC",
    "dateSent": "07/22/2025",
    "dateSentOrig": "07/05/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Ziemann Healthcare",
      "id": "PROV-9334"
    }
  },
  {
    "patient": {
      "name": "Luis Wuckert IV",
      "id": "PAT-17611"
    },
    "serviceDate": "07/24/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$6986.20",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "1:55 PM"
    },
    "user": "LBM",
    "dateSent": "08/26/2025",
    "dateSentOrig": "08/12/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Kutch Healthcare",
      "id": "PROV-5086"
    }
  },
  {
    "patient": {
      "name": "Wanda Wintheiser",
      "id": "PAT-96862"
    },
    "serviceDate": "08/03/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$714.29",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "6:48 AM"
    },
    "user": "SO",
    "dateSent": "08/22/2025",
    "dateSentOrig": "08/04/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Lueilwitz Clinic",
      "id": "PROV-3862"
    }
  },
  {
    "patient": {
      "name": "Troy Hayes",
      "id": "PAT-32147"
    },
    "serviceDate": "08/04/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$1715.71",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "8:55 PM"
    },
    "user": "VRR",
    "dateSent": "08/26/2025",
    "dateSentOrig": "08/11/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Flatley Family Practice",
      "id": "PROV-8801"
    }
  },
  {
    "patient": {
      "name": "Theodore Jacobs",
      "id": "PAT-13107"
    },
    "serviceDate": "05/14/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$3726.88",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "7:05 PM"
    },
    "user": "KR",
    "dateSent": "07/03/2025",
    "dateSentOrig": "07/03/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Hand Healthcare",
      "id": "PROV-8276"
    }
  },
  {
    "patient": {
      "name": "Kelly Welch",
      "id": "PAT-88163"
    },
    "serviceDate": "07/31/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$11261.82",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "8:49 AM"
    },
    "user": "JW",
    "dateSent": "08/02/2025",
    "dateSentOrig": "08/01/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Schimmel-Bins Healthcare",
      "id": "PROV-6443"
    }
  },
  {
    "patient": {
      "name": "Sophie Casper",
      "id": "PAT-37992"
    },
    "serviceDate": "07/09/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$9076.36",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "4:39 AM"
    },
    "user": "RFM",
    "dateSent": "08/07/2025",
    "dateSentOrig": "08/07/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Orn Medical Group",
      "id": "PROV-1323"
    }
  },
  {
    "patient": {
      "name": "May Hodkiewicz",
      "id": "PAT-87463"
    },
    "serviceDate": "03/25/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$9627.32",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "12:17 AM"
    },
    "user": "LBM",
    "dateSent": "06/15/2025",
    "dateSentOrig": "06/15/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Gottlieb Family Practice",
      "id": "PROV-6279"
    }
  },
  {
    "patient": {
      "name": "Rebecca Ernser",
      "id": "PAT-19736"
    },
    "serviceDate": "06/25/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$7006.45",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "4:20 AM"
    },
    "user": "KAC",
    "dateSent": "07/23/2025",
    "dateSentOrig": "07/15/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Corkery Associates",
      "id": "PROV-3999"
    }
  },
  {
    "patient": {
      "name": "Suzanne Flatley",
      "id": "PAT-22643"
    },
    "serviceDate": "03/22/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$13297.60",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/13/2025",
      "time": "10:13 AM"
    },
    "user": "BSP",
    "dateSent": "05/27/2025",
    "dateSentOrig": "04/13/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "McGlynn Healthcare",
      "id": "PROV-5794"
    }
  },
  {
    "patient": {
      "name": "Luke Torp I",
      "id": "PAT-35012"
    },
    "serviceDate": "05/29/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$2551.77",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "10:30 PM"
    },
    "user": "CR",
    "dateSent": "07/20/2025",
    "dateSentOrig": "07/02/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Mosciski Healthcare",
      "id": "PROV-4491"
    }
  },
  {
    "patient": {
      "name": "Ismael Wolff DDS",
      "id": "PAT-79698"
    },
    "serviceDate": "05/06/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$139.61",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "3:13 AM"
    },
    "user": "MB",
    "dateSent": "08/26/2025",
    "dateSentOrig": "06/19/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Green-McClure Medical Group",
      "id": "PROV-2440"
    }
  },
  {
    "patient": {
      "name": "Mr. Elijah Hartmann",
      "id": "PAT-47121"
    },
    "serviceDate": "05/15/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$581.69",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "9:49 AM"
    },
    "user": "CH",
    "dateSent": "05/23/2025",
    "dateSentOrig": "05/23/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Toy-Sanford Clinic",
      "id": "PROV-2947"
    }
  },
  {
    "patient": {
      "name": "Silvia Rolfson",
      "id": "PAT-29381"
    },
    "serviceDate": "07/23/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$10065.31",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "6:12 AM"
    },
    "user": "EH",
    "dateSent": "08/24/2025",
    "dateSentOrig": "08/22/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Goyette Clinic",
      "id": "PROV-2442"
    }
  },
  {
    "patient": {
      "name": "Devin Abshire",
      "id": "PAT-15693"
    },
    "serviceDate": "05/14/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$11406.48",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "10:44 AM"
    },
    "user": "PAH",
    "dateSent": "08/14/2025",
    "dateSentOrig": "08/14/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Bauch Medical Group",
      "id": "PROV-5140"
    }
  },
  {
    "patient": {
      "name": "Lance Goldner-Green II",
      "id": "PAT-54943"
    },
    "serviceDate": "08/08/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$5171.78",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "7:07 PM"
    },
    "user": "CG",
    "dateSent": "08/10/2025",
    "dateSentOrig": "08/08/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Green Healthcare",
      "id": "PROV-1983"
    }
  },
  {
    "patient": {
      "name": "Jacqueline Carter",
      "id": "PAT-32996"
    },
    "serviceDate": "08/06/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$4830.91",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "11:39 PM"
    },
    "user": "SK",
    "dateSent": "08/29/2025",
    "dateSentOrig": "08/11/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Pacocha Healthcare",
      "id": "PROV-8690"
    }
  },
  {
    "patient": {
      "name": "Roberta Bins MD",
      "id": "PAT-93618"
    },
    "serviceDate": "03/17/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$5758.91",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "7:28 PM"
    },
    "user": "RLL",
    "dateSent": "07/11/2025",
    "dateSentOrig": "07/11/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "VonRueden Family Practice",
      "id": "PROV-6654"
    }
  },
  {
    "patient": {
      "name": "Jackie Littel",
      "id": "PAT-18246"
    },
    "serviceDate": "04/12/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$2353.17",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "1:22 PM"
    },
    "user": "OSB",
    "dateSent": "04/23/2025",
    "dateSentOrig": "04/19/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Hettinger Family Practice",
      "id": "PROV-1347"
    }
  },
  {
    "patient": {
      "name": "Leslie Sipes",
      "id": "PAT-33316"
    },
    "serviceDate": "06/07/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$10250.56",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "12:06 PM"
    },
    "user": "MO",
    "dateSent": "08/21/2025",
    "dateSentOrig": "08/18/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Hessel Clinic",
      "id": "PROV-1373"
    }
  },
  {
    "patient": {
      "name": "Robert Roob-Rolfson",
      "id": "PAT-23748"
    },
    "serviceDate": "06/12/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$4926.70",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "11:56 AM"
    },
    "user": "LC",
    "dateSent": "06/14/2025",
    "dateSentOrig": "06/13/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Rohan Clinic",
      "id": "PROV-9990"
    }
  },
  {
    "patient": {
      "name": "Noah Hansen",
      "id": "PAT-46261"
    },
    "serviceDate": "04/08/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$5871.91",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "7:05 AM"
    },
    "user": "LJL",
    "dateSent": "09/02/2025",
    "dateSentOrig": "08/21/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Predovic Healthcare",
      "id": "PROV-9747"
    }
  },
  {
    "patient": {
      "name": "Jackie Herman",
      "id": "PAT-30060"
    },
    "serviceDate": "06/04/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$13234.03",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "6:05 PM"
    },
    "user": "ER",
    "dateSent": "07/13/2025",
    "dateSentOrig": "07/13/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Kreiger Medical Group",
      "id": "PROV-1779"
    }
  },
  {
    "patient": {
      "name": "Forrest Metz",
      "id": "PAT-36164"
    },
    "serviceDate": "08/02/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$8442.49",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "9:43 PM"
    },
    "user": "RQH",
    "dateSent": "08/04/2025",
    "dateSentOrig": "08/04/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Ritchie Family Practice",
      "id": "PROV-5150"
    }
  },
  {
    "patient": {
      "name": "Yvette Durgan II",
      "id": "PAT-42757"
    },
    "serviceDate": "06/17/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$12985.24",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "8:45 PM"
    },
    "user": "EB",
    "dateSent": "06/24/2025",
    "dateSentOrig": "06/24/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Thompson Medical Group",
      "id": "PROV-3039"
    }
  },
  {
    "patient": {
      "name": "Samuel Stanton",
      "id": "PAT-41307"
    },
    "serviceDate": "07/17/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$4967.31",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "12:56 PM"
    },
    "user": "CBR",
    "dateSent": "07/21/2025",
    "dateSentOrig": "07/18/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Weissnat Family Practice",
      "id": "PROV-4894"
    }
  },
  {
    "patient": {
      "name": "Ken Murazik",
      "id": "PAT-68306"
    },
    "serviceDate": "05/08/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$4706.46",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "6:03 PM"
    },
    "user": "HM",
    "dateSent": "06/17/2025",
    "dateSentOrig": "05/26/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Murray Medical Group",
      "id": "PROV-9567"
    }
  },
  {
    "patient": {
      "name": "Mrs. Diane MacGyver",
      "id": "PAT-28864"
    },
    "serviceDate": "03/15/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$1341.26",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "5:47 AM"
    },
    "user": "CRL",
    "dateSent": "04/05/2025",
    "dateSentOrig": "03/20/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Fay Clinic",
      "id": "PROV-2085"
    }
  },
  {
    "patient": {
      "name": "Harry Berge",
      "id": "PAT-43401"
    },
    "serviceDate": "06/16/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$14886.72",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "3:42 PM"
    },
    "user": "IPB",
    "dateSent": "07/05/2025",
    "dateSentOrig": "06/23/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Kiehn Family Practice",
      "id": "PROV-1796"
    }
  },
  {
    "patient": {
      "name": "Roman Weimann",
      "id": "PAT-84743"
    },
    "serviceDate": "07/04/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$4553.32",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "5:26 PM"
    },
    "user": "AW",
    "dateSent": "08/08/2025",
    "dateSentOrig": "08/08/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Watsica Healthcare",
      "id": "PROV-1426"
    }
  },
  {
    "patient": {
      "name": "Shane Rohan-Pfeffer",
      "id": "PAT-68092"
    },
    "serviceDate": "08/03/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$2413.00",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "12:06 AM"
    },
    "user": "JH",
    "dateSent": "08/06/2025",
    "dateSentOrig": "08/06/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Nader Healthcare",
      "id": "PROV-1096"
    }
  },
  {
    "patient": {
      "name": "Christie Heaney",
      "id": "PAT-14962"
    },
    "serviceDate": "03/07/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$14485.30",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "1:46 AM"
    },
    "user": "AMM",
    "dateSent": "08/17/2025",
    "dateSentOrig": "06/30/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Kutch Medical Group",
      "id": "PROV-6659"
    }
  },
  {
    "patient": {
      "name": "Rick Goldner",
      "id": "PAT-14390"
    },
    "serviceDate": "07/10/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$13871.47",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "7:31 AM"
    },
    "user": "DH",
    "dateSent": "08/03/2025",
    "dateSentOrig": "08/03/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Volkman Healthcare",
      "id": "PROV-3744"
    }
  },
  {
    "patient": {
      "name": "Nellie Sporer",
      "id": "PAT-98496"
    },
    "serviceDate": "05/18/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$7992.45",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "4:33 AM"
    },
    "user": "LBB",
    "dateSent": "08/10/2025",
    "dateSentOrig": "06/16/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "McCullough Healthcare",
      "id": "PROV-3272"
    }
  },
  {
    "patient": {
      "name": "Wendell Bins",
      "id": "PAT-85026"
    },
    "serviceDate": "07/28/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$3596.69",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "11:15 PM"
    },
    "user": "NNC",
    "dateSent": "08/20/2025",
    "dateSentOrig": "08/20/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Langworth Medical Group",
      "id": "PROV-8013"
    }
  },
  {
    "patient": {
      "name": "Kim O'Keefe PhD",
      "id": "PAT-60731"
    },
    "serviceDate": "04/05/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$14919.77",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/13/2025",
      "time": "12:23 PM"
    },
    "user": "ABL",
    "dateSent": "05/09/2025",
    "dateSentOrig": "04/26/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Stroman Healthcare",
      "id": "PROV-8004"
    }
  },
  {
    "patient": {
      "name": "Joanna Cremin-Block",
      "id": "PAT-75873"
    },
    "serviceDate": "08/30/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$9692.01",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "12:33 PM"
    },
    "user": "AP",
    "dateSent": "09/02/2025",
    "dateSentOrig": "09/02/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Gulgowski Healthcare",
      "id": "PROV-5581"
    }
  },
  {
    "patient": {
      "name": "Darnell O'Conner",
      "id": "PAT-93377"
    },
    "serviceDate": "04/24/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$7459.29",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "3:44 AM"
    },
    "user": "JR",
    "dateSent": "05/16/2025",
    "dateSentOrig": "05/16/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Schneider Clinic",
      "id": "PROV-3211"
    }
  },
  {
    "patient": {
      "name": "Kenny Schowalter",
      "id": "PAT-13562"
    },
    "serviceDate": "04/05/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$2549.73",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "6:00 PM"
    },
    "user": "MAS",
    "dateSent": "07/14/2025",
    "dateSentOrig": "06/29/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Nicolas Medical Group",
      "id": "PROV-3599"
    }
  },
  {
    "patient": {
      "name": "Sheri Witting-Bode",
      "id": "PAT-32226"
    },
    "serviceDate": "06/17/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$1486.49",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "7:55 AM"
    },
    "user": "EO",
    "dateSent": "06/23/2025",
    "dateSentOrig": "06/18/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Cole Family Practice",
      "id": "PROV-5084"
    }
  },
  {
    "patient": {
      "name": "Carroll O'Keefe",
      "id": "PAT-88792"
    },
    "serviceDate": "06/16/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$11544.43",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "9:29 PM"
    },
    "user": "HCB",
    "dateSent": "08/17/2025",
    "dateSentOrig": "08/16/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Ernser Healthcare",
      "id": "PROV-4220"
    }
  },
  {
    "patient": {
      "name": "Regina Nitzsche",
      "id": "PAT-78274"
    },
    "serviceDate": "04/28/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$4512.69",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "2:31 AM"
    },
    "user": "CSD",
    "dateSent": "07/04/2025",
    "dateSentOrig": "07/04/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Ullrich Healthcare",
      "id": "PROV-7016"
    }
  },
  {
    "patient": {
      "name": "Wilfred Heathcote",
      "id": "PAT-89296"
    },
    "serviceDate": "04/18/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$7626.40",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "1:38 AM"
    },
    "user": "AHL",
    "dateSent": "08/21/2025",
    "dateSentOrig": "06/13/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "VonRueden Associates",
      "id": "PROV-9393"
    }
  },
  {
    "patient": {
      "name": "Aubrey Mueller",
      "id": "PAT-94409"
    },
    "serviceDate": "07/01/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$13556.56",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "6:40 PM"
    },
    "user": "DRG",
    "dateSent": "07/26/2025",
    "dateSentOrig": "07/26/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Hoeger Clinic",
      "id": "PROV-8747"
    }
  },
  {
    "patient": {
      "name": "Mr. Douglas Walker",
      "id": "PAT-70012"
    },
    "serviceDate": "03/09/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$2712.80",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "6:15 AM"
    },
    "user": "WP",
    "dateSent": "05/11/2025",
    "dateSentOrig": "03/31/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Deckow Healthcare",
      "id": "PROV-1795"
    }
  },
  {
    "patient": {
      "name": "Lorene Langosh",
      "id": "PAT-23696"
    },
    "serviceDate": "06/16/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$11386.00",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "9:54 PM"
    },
    "user": "MP",
    "dateSent": "06/18/2025",
    "dateSentOrig": "06/16/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Gulgowski-Labadie Healthcare",
      "id": "PROV-5373"
    }
  },
  {
    "patient": {
      "name": "Lynette VonRueden",
      "id": "PAT-38566"
    },
    "serviceDate": "05/10/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$12478.12",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "9:14 AM"
    },
    "user": "TW",
    "dateSent": "05/16/2025",
    "dateSentOrig": "05/15/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Crooks Clinic",
      "id": "PROV-2764"
    }
  },
  {
    "patient": {
      "name": "Homer Toy-Schiller",
      "id": "PAT-92590"
    },
    "serviceDate": "06/19/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$12833.19",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "2:50 PM"
    },
    "user": "DSO",
    "dateSent": "07/24/2025",
    "dateSentOrig": "06/20/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Rempel Family Practice",
      "id": "PROV-7699"
    }
  },
  {
    "patient": {
      "name": "Ed Yundt",
      "id": "PAT-26693"
    },
    "serviceDate": "03/23/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$11722.08",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "4:09 AM"
    },
    "user": "DRD",
    "dateSent": "08/24/2025",
    "dateSentOrig": "07/02/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Lockman Healthcare",
      "id": "PROV-3320"
    }
  },
  {
    "patient": {
      "name": "Mr. Edwin Tromp",
      "id": "PAT-74690"
    },
    "serviceDate": "08/20/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$14878.65",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "8:20 PM"
    },
    "user": "VKK",
    "dateSent": "08/28/2025",
    "dateSentOrig": "08/21/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Nicolas Associates",
      "id": "PROV-5760"
    }
  },
  {
    "patient": {
      "name": "Mrs. Constance Jenkins",
      "id": "PAT-98835"
    },
    "serviceDate": "05/10/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$7137.08",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "9:16 PM"
    },
    "user": "MRK",
    "dateSent": "05/29/2025",
    "dateSentOrig": "05/25/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Hirthe Healthcare",
      "id": "PROV-5008"
    }
  },
  {
    "patient": {
      "name": "Stuart Zulauf",
      "id": "PAT-97406"
    },
    "serviceDate": "03/23/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$6542.34",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "11:54 AM"
    },
    "user": "CH",
    "dateSent": "06/02/2025",
    "dateSentOrig": "06/02/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Hills Healthcare",
      "id": "PROV-9336"
    }
  },
  {
    "patient": {
      "name": "Carlton Leannon",
      "id": "PAT-42909"
    },
    "serviceDate": "08/19/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$1006.50",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "2:38 PM"
    },
    "user": "MHR",
    "dateSent": "08/21/2025",
    "dateSentOrig": "08/21/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Batz Healthcare",
      "id": "PROV-1538"
    }
  },
  {
    "patient": {
      "name": "Deanna Jacobs",
      "id": "PAT-35052"
    },
    "serviceDate": "05/30/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$2173.51",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "11:55 PM"
    },
    "user": "HS",
    "dateSent": "07/01/2025",
    "dateSentOrig": "07/01/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Cronin Clinic",
      "id": "PROV-3761"
    }
  },
  {
    "patient": {
      "name": "Jan Leannon",
      "id": "PAT-98916"
    },
    "serviceDate": "07/12/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$12178.23",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "9:59 AM"
    },
    "user": "AC",
    "dateSent": "07/31/2025",
    "dateSentOrig": "07/31/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Kunze Medical Group",
      "id": "PROV-2491"
    }
  },
  {
    "patient": {
      "name": "Mrs. Harriet Morar",
      "id": "PAT-37109"
    },
    "serviceDate": "07/16/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$3315.91",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "4:46 PM"
    },
    "user": "ABK",
    "dateSent": "07/21/2025",
    "dateSentOrig": "07/21/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Rohan Associates",
      "id": "PROV-3684"
    }
  },
  {
    "patient": {
      "name": "Alonzo Wolf",
      "id": "PAT-53090"
    },
    "serviceDate": "07/03/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$9355.78",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "4:32 AM"
    },
    "user": "GS",
    "dateSent": "08/29/2025",
    "dateSentOrig": "08/29/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Keeling-Kessler Healthcare",
      "id": "PROV-5430"
    }
  },
  {
    "patient": {
      "name": "Evelyn Murray",
      "id": "PAT-58521"
    },
    "serviceDate": "05/02/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$9130.90",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "2:03 AM"
    },
    "user": "ESS",
    "dateSent": "05/04/2025",
    "dateSentOrig": "05/04/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Johnston-Roob Healthcare",
      "id": "PROV-8143"
    }
  },
  {
    "patient": {
      "name": "Lila Rath",
      "id": "PAT-71253"
    },
    "serviceDate": "05/05/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$598.71",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "8:10 PM"
    },
    "user": "OLS",
    "dateSent": "07/10/2025",
    "dateSentOrig": "06/21/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Kautzer Associates",
      "id": "PROV-7922"
    }
  },
  {
    "patient": {
      "name": "Shaun Lindgren",
      "id": "PAT-48464"
    },
    "serviceDate": "04/17/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$1931.54",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "12:21 PM"
    },
    "user": "AH",
    "dateSent": "05/06/2025",
    "dateSentOrig": "04/27/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Cormier Family Practice",
      "id": "PROV-4225"
    }
  },
  {
    "patient": {
      "name": "Terrence Prohaska",
      "id": "PAT-20513"
    },
    "serviceDate": "07/07/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$9421.22",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "2:59 PM"
    },
    "user": "YH",
    "dateSent": "08/04/2025",
    "dateSentOrig": "08/04/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Pollich Clinic",
      "id": "PROV-4455"
    }
  },
  {
    "patient": {
      "name": "Jacqueline Rau PhD",
      "id": "PAT-22335"
    },
    "serviceDate": "05/31/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$14829.60",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "6:20 AM"
    },
    "user": "MC",
    "dateSent": "08/01/2025",
    "dateSentOrig": "08/01/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Stracke Medical Group",
      "id": "PROV-2276"
    }
  },
  {
    "patient": {
      "name": "Mr. Clark Pfannerstill",
      "id": "PAT-63746"
    },
    "serviceDate": "07/18/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$12556.18",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "12:45 PM"
    },
    "user": "YKJ",
    "dateSent": "08/18/2025",
    "dateSentOrig": "08/18/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Heaney Associates",
      "id": "PROV-5695"
    }
  },
  {
    "patient": {
      "name": "Ralph Larkin",
      "id": "PAT-57456"
    },
    "serviceDate": "06/11/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$2812.20",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "12:17 AM"
    },
    "user": "ADA",
    "dateSent": "08/04/2025",
    "dateSentOrig": "08/04/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Shields Medical Group",
      "id": "PROV-9715"
    }
  },
  {
    "patient": {
      "name": "Joey Grady",
      "id": "PAT-22658"
    },
    "serviceDate": "04/06/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$489.19",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "10:55 PM"
    },
    "user": "GBM",
    "dateSent": "07/06/2025",
    "dateSentOrig": "07/06/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Fadel Medical Group",
      "id": "PROV-6613"
    }
  },
  {
    "patient": {
      "name": "Vernon Haag",
      "id": "PAT-83325"
    },
    "serviceDate": "05/16/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$12942.82",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "12:46 PM"
    },
    "user": "TKS",
    "dateSent": "06/10/2025",
    "dateSentOrig": "05/22/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Cummings Clinic",
      "id": "PROV-1630"
    }
  },
  {
    "patient": {
      "name": "Dr. Shawna MacGyver",
      "id": "PAT-25651"
    },
    "serviceDate": "04/18/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$6180.51",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "10:39 AM"
    },
    "user": "IL",
    "dateSent": "08/12/2025",
    "dateSentOrig": "08/12/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Schuppe Clinic",
      "id": "PROV-6870"
    }
  },
  {
    "patient": {
      "name": "Jonathan King",
      "id": "PAT-70054"
    },
    "serviceDate": "08/10/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$2942.62",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "9:43 PM"
    },
    "user": "VL",
    "dateSent": "08/28/2025",
    "dateSentOrig": "08/16/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Hayes Medical Group",
      "id": "PROV-9099"
    }
  },
  {
    "patient": {
      "name": "Fred Kihn",
      "id": "PAT-21244"
    },
    "serviceDate": "06/01/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$13413.29",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "1:36 PM"
    },
    "user": "AH",
    "dateSent": "08/12/2025",
    "dateSentOrig": "07/14/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Bernier Medical Group",
      "id": "PROV-7403"
    }
  },
  {
    "patient": {
      "name": "Lonnie Franecki",
      "id": "PAT-95083"
    },
    "serviceDate": "06/10/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$124.74",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "6:36 AM"
    },
    "user": "TPH",
    "dateSent": "08/14/2025",
    "dateSentOrig": "08/14/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Kuhlman Family Practice",
      "id": "PROV-1100"
    }
  },
  {
    "patient": {
      "name": "Ida Schuppe",
      "id": "PAT-50845"
    },
    "serviceDate": "05/10/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$3641.41",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "7:48 PM"
    },
    "user": "MRM",
    "dateSent": "06/14/2025",
    "dateSentOrig": "06/14/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Howe Family Practice",
      "id": "PROV-2066"
    }
  },
  {
    "patient": {
      "name": "Mr. Evan Windler",
      "id": "PAT-18347"
    },
    "serviceDate": "07/03/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$9535.40",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "8:51 PM"
    },
    "user": "RH",
    "dateSent": "07/13/2025",
    "dateSentOrig": "07/13/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Torphy Associates",
      "id": "PROV-1514"
    }
  },
  {
    "patient": {
      "name": "Dr. Eugene Tremblay",
      "id": "PAT-78504"
    },
    "serviceDate": "04/14/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$12411.22",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "3:28 AM"
    },
    "user": "TW",
    "dateSent": "08/21/2025",
    "dateSentOrig": "08/21/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Reichel Medical Group",
      "id": "PROV-2957"
    }
  },
  {
    "patient": {
      "name": "Josefina Jaskolski",
      "id": "PAT-57463"
    },
    "serviceDate": "07/01/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$9918.70",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "8:52 AM"
    },
    "user": "RCS",
    "dateSent": "07/19/2025",
    "dateSentOrig": "07/01/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Leuschke Associates",
      "id": "PROV-1990"
    }
  },
  {
    "patient": {
      "name": "Della Considine",
      "id": "PAT-46854"
    },
    "serviceDate": "08/06/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$3537.88",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "6:46 AM"
    },
    "user": "EO",
    "dateSent": "08/23/2025",
    "dateSentOrig": "08/12/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Price Clinic",
      "id": "PROV-6467"
    }
  },
  {
    "patient": {
      "name": "Julia Wuckert",
      "id": "PAT-43151"
    },
    "serviceDate": "06/26/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$9007.74",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "7:00 AM"
    },
    "user": "JB",
    "dateSent": "08/28/2025",
    "dateSentOrig": "08/23/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Watsica Medical Group",
      "id": "PROV-8447"
    }
  },
  {
    "patient": {
      "name": "Blanca Baumbach",
      "id": "PAT-97885"
    },
    "serviceDate": "05/16/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$10315.21",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "2:44 AM"
    },
    "user": "RS",
    "dateSent": "07/25/2025",
    "dateSentOrig": "06/28/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Kassulke Healthcare",
      "id": "PROV-9978"
    }
  },
  {
    "patient": {
      "name": "Beulah Mertz",
      "id": "PAT-20491"
    },
    "serviceDate": "05/31/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$2534.24",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "10:21 PM"
    },
    "user": "DS",
    "dateSent": "07/12/2025",
    "dateSentOrig": "06/28/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Pollich Healthcare",
      "id": "PROV-4584"
    }
  },
  {
    "patient": {
      "name": "Gloria Botsford",
      "id": "PAT-11492"
    },
    "serviceDate": "05/08/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$13885.52",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "3:25 AM"
    },
    "user": "RNW",
    "dateSent": "08/04/2025",
    "dateSentOrig": "08/04/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Ritchie Family Practice",
      "id": "PROV-1606"
    }
  },
  {
    "patient": {
      "name": "Ray Adams",
      "id": "PAT-60373"
    },
    "serviceDate": "05/06/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$7338.85",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "4:23 AM"
    },
    "user": "GRS",
    "dateSent": "06/02/2025",
    "dateSentOrig": "05/08/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Nicolas Healthcare",
      "id": "PROV-3434"
    }
  },
  {
    "patient": {
      "name": "Tabitha D'Amore",
      "id": "PAT-89226"
    },
    "serviceDate": "05/06/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$9841.43",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "9:50 PM"
    },
    "user": "SG",
    "dateSent": "06/16/2025",
    "dateSentOrig": "06/16/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Boehm Associates",
      "id": "PROV-8309"
    }
  },
  {
    "patient": {
      "name": "Erin Okuneva",
      "id": "PAT-25948"
    },
    "serviceDate": "07/12/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$14360.48",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "3:09 PM"
    },
    "user": "EPL",
    "dateSent": "08/17/2025",
    "dateSentOrig": "08/17/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Halvorson Medical Group",
      "id": "PROV-5737"
    }
  },
  {
    "patient": {
      "name": "Latoya Jaskolski",
      "id": "PAT-88854"
    },
    "serviceDate": "06/06/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$3002.23",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "12:42 AM"
    },
    "user": "HO",
    "dateSent": "08/30/2025",
    "dateSentOrig": "08/30/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Funk Medical Group",
      "id": "PROV-4944"
    }
  },
  {
    "patient": {
      "name": "Miriam Hartmann",
      "id": "PAT-62412"
    },
    "serviceDate": "08/13/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$12856.38",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "4:10 AM"
    },
    "user": "JA",
    "dateSent": "08/22/2025",
    "dateSentOrig": "08/22/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Marvin Clinic",
      "id": "PROV-4172"
    }
  },
  {
    "patient": {
      "name": "Dawn Bahringer",
      "id": "PAT-77997"
    },
    "serviceDate": "05/06/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$14818.31",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "5:00 AM"
    },
    "user": "MJ",
    "dateSent": "07/01/2025",
    "dateSentOrig": "06/16/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Kohler Associates",
      "id": "PROV-4316"
    }
  },
  {
    "patient": {
      "name": "Jacqueline Rohan I",
      "id": "PAT-80550"
    },
    "serviceDate": "03/07/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$9807.43",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "4:35 PM"
    },
    "user": "LR",
    "dateSent": "04/17/2025",
    "dateSentOrig": "04/17/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Gutmann Associates",
      "id": "PROV-5056"
    }
  },
  {
    "patient": {
      "name": "Lonnie Sporer",
      "id": "PAT-39840"
    },
    "serviceDate": "08/06/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$3525.83",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "11:23 PM"
    },
    "user": "SSH",
    "dateSent": "09/02/2025",
    "dateSentOrig": "09/02/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Gusikowski Medical Group",
      "id": "PROV-8160"
    }
  },
  {
    "patient": {
      "name": "Carmen Klein",
      "id": "PAT-23172"
    },
    "serviceDate": "06/24/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$3702.82",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "8:21 AM"
    },
    "user": "DJE",
    "dateSent": "08/27/2025",
    "dateSentOrig": "08/27/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Zulauf Family Practice",
      "id": "PROV-1096"
    }
  },
  {
    "patient": {
      "name": "Mrs. Gretchen Mraz-Hoeger IV",
      "id": "PAT-99733"
    },
    "serviceDate": "07/20/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$11561.17",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "5:09 AM"
    },
    "user": "NPW",
    "dateSent": "08/17/2025",
    "dateSentOrig": "08/17/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Rogahn Family Practice",
      "id": "PROV-2104"
    }
  },
  {
    "patient": {
      "name": "Willie Simonis",
      "id": "PAT-47411"
    },
    "serviceDate": "07/05/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$5411.08",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "3:39 PM"
    },
    "user": "AS",
    "dateSent": "08/09/2025",
    "dateSentOrig": "07/23/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Murphy Family Practice",
      "id": "PROV-9862"
    }
  },
  {
    "patient": {
      "name": "Kimberly Goldner",
      "id": "PAT-50889"
    },
    "serviceDate": "05/16/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$10396.74",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "9:01 PM"
    },
    "user": "JK",
    "dateSent": "07/13/2025",
    "dateSentOrig": "07/13/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Ondricka Medical Group",
      "id": "PROV-7457"
    }
  },
  {
    "patient": {
      "name": "Russell Kozey",
      "id": "PAT-38586"
    },
    "serviceDate": "06/07/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$10993.78",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "8:42 AM"
    },
    "user": "RRK",
    "dateSent": "08/14/2025",
    "dateSentOrig": "08/14/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Barton Family Practice",
      "id": "PROV-5368"
    }
  },
  {
    "patient": {
      "name": "Andrea Kuphal DVM",
      "id": "PAT-69561"
    },
    "serviceDate": "05/30/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$10427.07",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "7:49 PM"
    },
    "user": "ESA",
    "dateSent": "07/06/2025",
    "dateSentOrig": "07/05/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Pacocha Associates",
      "id": "PROV-7434"
    }
  },
  {
    "patient": {
      "name": "Rebecca Herzog",
      "id": "PAT-62555"
    },
    "serviceDate": "03/20/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$1501.99",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "7:47 AM"
    },
    "user": "OSS",
    "dateSent": "08/30/2025",
    "dateSentOrig": "08/30/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Gutkowski Medical Group",
      "id": "PROV-6493"
    }
  },
  {
    "patient": {
      "name": "Ebony Murphy",
      "id": "PAT-15696"
    },
    "serviceDate": "04/19/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$12552.36",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "10:51 PM"
    },
    "user": "FLJ",
    "dateSent": "07/03/2025",
    "dateSentOrig": "07/03/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Kuhlman Associates",
      "id": "PROV-5037"
    }
  },
  {
    "patient": {
      "name": "Debbie Vandervort",
      "id": "PAT-12994"
    },
    "serviceDate": "07/14/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$7147.01",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "12:57 AM"
    },
    "user": "MJH",
    "dateSent": "07/22/2025",
    "dateSentOrig": "07/22/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Crona Medical Group",
      "id": "PROV-6747"
    }
  },
  {
    "patient": {
      "name": "Brian Lesch",
      "id": "PAT-58916"
    },
    "serviceDate": "06/29/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$7430.65",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "4:28 PM"
    },
    "user": "GB",
    "dateSent": "09/02/2025",
    "dateSentOrig": "09/02/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Flatley Medical Group",
      "id": "PROV-9291"
    }
  },
  {
    "patient": {
      "name": "Charlene Price",
      "id": "PAT-43517"
    },
    "serviceDate": "08/07/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$4429.34",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/04/2025",
      "time": "1:33 PM"
    },
    "user": "LK",
    "dateSent": "08/15/2025",
    "dateSentOrig": "08/15/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Ziemann Healthcare",
      "id": "PROV-7111"
    }
  },
  {
    "patient": {
      "name": "Earl Steuber",
      "id": "PAT-30813"
    },
    "serviceDate": "05/13/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$4284.68",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "6:30 AM"
    },
    "user": "AR",
    "dateSent": "05/23/2025",
    "dateSentOrig": "05/23/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Lynch Family Practice",
      "id": "PROV-7965"
    }
  },
  {
    "patient": {
      "name": "Tracy Pollich Jr.",
      "id": "PAT-53445"
    },
    "serviceDate": "07/02/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$586.75",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "8:16 AM"
    },
    "user": "MAB",
    "dateSent": "07/15/2025",
    "dateSentOrig": "07/15/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "West Medical Group",
      "id": "PROV-2613"
    }
  },
  {
    "patient": {
      "name": "Lynette Jakubowski",
      "id": "PAT-57115"
    },
    "serviceDate": "05/07/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$71.90",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "4:19 PM"
    },
    "user": "AAA",
    "dateSent": "05/23/2025",
    "dateSentOrig": "05/10/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Koss Associates",
      "id": "PROV-8362"
    }
  },
  {
    "patient": {
      "name": "Tiffany Dickens",
      "id": "PAT-34171"
    },
    "serviceDate": "03/27/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$8265.44",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "5:27 AM"
    },
    "user": "AP",
    "dateSent": "04/21/2025",
    "dateSentOrig": "04/02/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Kuvalis Medical Group",
      "id": "PROV-6307"
    }
  },
  {
    "patient": {
      "name": "Natasha Balistreri Jr.",
      "id": "PAT-99246"
    },
    "serviceDate": "08/25/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$7195.38",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "8:26 PM"
    },
    "user": "SC",
    "dateSent": "08/27/2025",
    "dateSentOrig": "08/25/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Beer Healthcare",
      "id": "PROV-2345"
    }
  },
  {
    "patient": {
      "name": "Tricia Metz",
      "id": "PAT-62892"
    },
    "serviceDate": "07/08/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$10264.16",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "5:20 AM"
    },
    "user": "JR",
    "dateSent": "07/20/2025",
    "dateSentOrig": "07/15/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Berge Family Practice",
      "id": "PROV-5628"
    }
  },
  {
    "patient": {
      "name": "Jacob Willms",
      "id": "PAT-36953"
    },
    "serviceDate": "08/25/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$1079.46",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "11:12 AM"
    },
    "user": "LR",
    "dateSent": "09/02/2025",
    "dateSentOrig": "09/02/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Anderson Family Practice",
      "id": "PROV-9857"
    }
  },
  {
    "patient": {
      "name": "Amanda Bayer",
      "id": "PAT-81169"
    },
    "serviceDate": "05/22/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$6514.43",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "2:28 AM"
    },
    "user": "QPV",
    "dateSent": "08/23/2025",
    "dateSentOrig": "06/12/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Brakus Family Practice",
      "id": "PROV-4761"
    }
  },
  {
    "patient": {
      "name": "Sheila Leannon",
      "id": "PAT-83555"
    },
    "serviceDate": "05/10/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$4953.51",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "12:14 AM"
    },
    "user": "RJB",
    "dateSent": "06/22/2025",
    "dateSentOrig": "05/12/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Sawayn Medical Group",
      "id": "PROV-3874"
    }
  },
  {
    "patient": {
      "name": "Kurt McKenzie",
      "id": "PAT-73222"
    },
    "serviceDate": "07/12/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$9784.03",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "9:26 AM"
    },
    "user": "ES",
    "dateSent": "08/05/2025",
    "dateSentOrig": "08/05/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Thiel Medical Group",
      "id": "PROV-7366"
    }
  },
  {
    "patient": {
      "name": "Gayle Nader DVM",
      "id": "PAT-77819"
    },
    "serviceDate": "04/28/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$14512.62",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "5:33 AM"
    },
    "user": "ORW",
    "dateSent": "05/27/2025",
    "dateSentOrig": "05/27/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Roob Clinic",
      "id": "PROV-4376"
    }
  },
  {
    "patient": {
      "name": "Kendra Mueller-Dickens",
      "id": "PAT-82581"
    },
    "serviceDate": "08/16/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$8597.06",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "12:38 AM"
    },
    "user": "AL",
    "dateSent": "08/19/2025",
    "dateSentOrig": "08/19/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Schuppe Healthcare",
      "id": "PROV-1741"
    }
  },
  {
    "patient": {
      "name": "Roberta Botsford",
      "id": "PAT-20955"
    },
    "serviceDate": "08/25/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$7793.28",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "1:42 PM"
    },
    "user": "LL",
    "dateSent": "08/30/2025",
    "dateSentOrig": "08/30/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Bahringer Medical Group",
      "id": "PROV-3314"
    }
  },
  {
    "patient": {
      "name": "Ms. Hattie Turner",
      "id": "PAT-38957"
    },
    "serviceDate": "06/02/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$5036.39",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "1:09 PM"
    },
    "user": "RP",
    "dateSent": "06/06/2025",
    "dateSentOrig": "06/06/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Sauer Healthcare",
      "id": "PROV-6141"
    }
  },
  {
    "patient": {
      "name": "Louis Towne",
      "id": "PAT-61380"
    },
    "serviceDate": "05/26/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$13841.49",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "8:17 PM"
    },
    "user": "HSL",
    "dateSent": "08/17/2025",
    "dateSentOrig": "08/17/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Rippin Healthcare",
      "id": "PROV-6919"
    }
  },
  {
    "patient": {
      "name": "Desiree Rowe",
      "id": "PAT-63169"
    },
    "serviceDate": "04/30/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$14248.95",
    "status": "DENIED",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "12:57 AM"
    },
    "user": "AS",
    "dateSent": "08/10/2025",
    "dateSentOrig": "08/10/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Botsford Healthcare",
      "id": "PROV-8365"
    }
  },
  {
    "patient": {
      "name": "Olga Bosco",
      "id": "PAT-69234"
    },
    "serviceDate": "07/23/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$9745.05",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "5:04 PM"
    },
    "user": "SJH",
    "dateSent": "08/27/2025",
    "dateSentOrig": "08/27/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Bergnaum Family Practice",
      "id": "PROV-3072"
    }
  },
  {
    "patient": {
      "name": "Christina Hoppe",
      "id": "PAT-75300"
    },
    "serviceDate": "03/27/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$14163.64",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "8:02 PM"
    },
    "user": "DRB",
    "dateSent": "07/02/2025",
    "dateSentOrig": "05/22/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Schmidt Family Practice",
      "id": "PROV-6499"
    }
  },
  {
    "patient": {
      "name": "Bernice Dooley",
      "id": "PAT-77677"
    },
    "serviceDate": "04/21/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$1701.82",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "1:08 AM"
    },
    "user": "LT",
    "dateSent": "06/15/2025",
    "dateSentOrig": "05/12/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Oberbrunner Medical Group",
      "id": "PROV-1196"
    }
  },
  {
    "patient": {
      "name": "Mitchell Hamill Jr.",
      "id": "PAT-53418"
    },
    "serviceDate": "06/29/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$11348.11",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "9:10 PM"
    },
    "user": "IB",
    "dateSent": "07/28/2025",
    "dateSentOrig": "07/16/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "O'Conner Associates",
      "id": "PROV-9776"
    }
  },
  {
    "patient": {
      "name": "Jim Ward",
      "id": "PAT-75356"
    },
    "serviceDate": "06/10/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$13626.74",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "1:57 PM"
    },
    "user": "KSG",
    "dateSent": "07/15/2025",
    "dateSentOrig": "06/25/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Kuhn Family Practice",
      "id": "PROV-5669"
    }
  },
  {
    "patient": {
      "name": "Barry Fay",
      "id": "PAT-87035"
    },
    "serviceDate": "05/04/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$2700.16",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "5:12 AM"
    },
    "user": "AGP",
    "dateSent": "08/20/2025",
    "dateSentOrig": "08/20/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Breitenberg Medical Group",
      "id": "PROV-9771"
    }
  },
  {
    "patient": {
      "name": "Sean Harris",
      "id": "PAT-45673"
    },
    "serviceDate": "05/25/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$9721.15",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "12:31 PM"
    },
    "user": "KB",
    "dateSent": "07/19/2025",
    "dateSentOrig": "07/19/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Hamill Medical Group",
      "id": "PROV-1542"
    }
  },
  {
    "patient": {
      "name": "Julie Russel",
      "id": "PAT-14536"
    },
    "serviceDate": "06/07/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$3745.36",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "8:51 PM"
    },
    "user": "MRR",
    "dateSent": "08/29/2025",
    "dateSentOrig": "08/29/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Kuphal Associates",
      "id": "PROV-4403"
    }
  },
  {
    "patient": {
      "name": "Jenny Harber",
      "id": "PAT-24534"
    },
    "serviceDate": "03/10/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$4461.28",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "1:10 AM"
    },
    "user": "KK",
    "dateSent": "03/23/2025",
    "dateSentOrig": "03/22/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Maggio Healthcare",
      "id": "PROV-4454"
    }
  },
  {
    "patient": {
      "name": "Norman Bruen Sr.",
      "id": "PAT-85031"
    },
    "serviceDate": "03/27/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$7189.73",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "6:49 AM"
    },
    "user": "HD",
    "dateSent": "05/05/2025",
    "dateSentOrig": "05/04/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Nicolas Clinic",
      "id": "PROV-2292"
    }
  },
  {
    "patient": {
      "name": "Jake Lockman",
      "id": "PAT-92718"
    },
    "serviceDate": "07/29/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$370.08",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "1:05 AM"
    },
    "user": "BM",
    "dateSent": "08/06/2025",
    "dateSentOrig": "08/01/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Dickens Healthcare",
      "id": "PROV-5244"
    }
  },
  {
    "patient": {
      "name": "Tommie Luettgen",
      "id": "PAT-27455"
    },
    "serviceDate": "07/26/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$2855.06",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "12:35 PM"
    },
    "user": "LO",
    "dateSent": "08/29/2025",
    "dateSentOrig": "08/13/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Conn Associates",
      "id": "PROV-8299"
    }
  },
  {
    "patient": {
      "name": "Traci McKenzie Jr.",
      "id": "PAT-70157"
    },
    "serviceDate": "03/16/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$3057.33",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "4:40 PM"
    },
    "user": "BCD",
    "dateSent": "08/06/2025",
    "dateSentOrig": "08/06/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Moen-McDermott Family Practice",
      "id": "PROV-2287"
    }
  },
  {
    "patient": {
      "name": "Jessie Bruen I",
      "id": "PAT-40100"
    },
    "serviceDate": "04/09/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$1655.93",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "7:18 AM"
    },
    "user": "RDB",
    "dateSent": "07/21/2025",
    "dateSentOrig": "06/30/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Hoeger Medical Group",
      "id": "PROV-9614"
    }
  },
  {
    "patient": {
      "name": "Ellis Langosh",
      "id": "PAT-68067"
    },
    "serviceDate": "03/27/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$5188.93",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "10:06 PM"
    },
    "user": "PH",
    "dateSent": "04/26/2025",
    "dateSentOrig": "04/23/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Satterfield Medical Group",
      "id": "PROV-1010"
    }
  },
  {
    "patient": {
      "name": "Dr. Sylvester Romaguera",
      "id": "PAT-85834"
    },
    "serviceDate": "08/02/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$10718.57",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "12:46 AM"
    },
    "user": "TP",
    "dateSent": "08/04/2025",
    "dateSentOrig": "08/04/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Smith Medical Group",
      "id": "PROV-6574"
    }
  },
  {
    "patient": {
      "name": "Mark Ebert",
      "id": "PAT-98815"
    },
    "serviceDate": "07/28/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$4524.18",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "6:36 AM"
    },
    "user": "MH",
    "dateSent": "08/16/2025",
    "dateSentOrig": "07/30/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Lebsack Family Practice",
      "id": "PROV-7469"
    }
  },
  {
    "patient": {
      "name": "Rick Bode",
      "id": "PAT-10547"
    },
    "serviceDate": "03/07/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$10008.69",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "1:18 PM"
    },
    "user": "BD",
    "dateSent": "07/11/2025",
    "dateSentOrig": "07/11/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Schamberger Healthcare",
      "id": "PROV-3636"
    }
  },
  {
    "patient": {
      "name": "Velma Keebler",
      "id": "PAT-51355"
    },
    "serviceDate": "03/27/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$4054.76",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "12:25 PM"
    },
    "user": "GEC",
    "dateSent": "04/20/2025",
    "dateSentOrig": "04/20/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Quitzon Medical Group",
      "id": "PROV-7047"
    }
  },
  {
    "patient": {
      "name": "Kim Sporer",
      "id": "PAT-68133"
    },
    "serviceDate": "08/11/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$6368.57",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/04/2025",
      "time": "2:51 PM"
    },
    "user": "JHK",
    "dateSent": "08/19/2025",
    "dateSentOrig": "08/19/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Fritsch Healthcare",
      "id": "PROV-1267"
    }
  },
  {
    "patient": {
      "name": "Sandy Spinka",
      "id": "PAT-53055"
    },
    "serviceDate": "08/08/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$6209.99",
    "status": "PENDING",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "4:42 PM"
    },
    "user": "NEC",
    "dateSent": "08/31/2025",
    "dateSentOrig": "08/31/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Marks Clinic",
      "id": "PROV-8334"
    }
  },
  {
    "patient": {
      "name": "Douglas Stracke",
      "id": "PAT-91481"
    },
    "serviceDate": "07/22/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$8630.05",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "11:38 PM"
    },
    "user": "DS",
    "dateSent": "08/23/2025",
    "dateSentOrig": "08/09/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Metz Family Practice",
      "id": "PROV-1066"
    }
  },
  {
    "patient": {
      "name": "Alfred Klein",
      "id": "PAT-42702"
    },
    "serviceDate": "07/26/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$7103.43",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "5:03 AM"
    },
    "user": "OZ",
    "dateSent": "08/31/2025",
    "dateSentOrig": "08/09/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Harris-Stehr Associates",
      "id": "PROV-7624"
    }
  },
  {
    "patient": {
      "name": "Rodney Russel",
      "id": "PAT-17059"
    },
    "serviceDate": "03/23/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$7487.13",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "9:02 PM"
    },
    "user": "GAF",
    "dateSent": "07/14/2025",
    "dateSentOrig": "07/14/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Mertz Healthcare",
      "id": "PROV-7930"
    }
  },
  {
    "patient": {
      "name": "Ernesto Zemlak",
      "id": "PAT-68958"
    },
    "serviceDate": "04/30/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$7249.79",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "6:39 PM"
    },
    "user": "VK",
    "dateSent": "06/20/2025",
    "dateSentOrig": "05/01/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Wilderman Clinic",
      "id": "PROV-2364"
    }
  },
  {
    "patient": {
      "name": "Mindy Sporer",
      "id": "PAT-59178"
    },
    "serviceDate": "06/16/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$10516.85",
    "status": "PAID",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "8:05 PM"
    },
    "user": "CH",
    "dateSent": "07/29/2025",
    "dateSentOrig": "07/29/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Hammes Associates",
      "id": "PROV-8165"
    }
  },
  {
    "patient": {
      "name": "Dan Powlowski",
      "id": "PAT-28339"
    },
    "serviceDate": "06/07/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$1235.66",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "4:05 AM"
    },
    "user": "NB",
    "dateSent": "07/13/2025",
    "dateSentOrig": "06/21/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Erdman Associates",
      "id": "PROV-4116"
    }
  },
  {
    "patient": {
      "name": "Dr. Harry Predovic",
      "id": "PAT-28520"
    },
    "serviceDate": "03/22/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$815.88",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "2:16 AM"
    },
    "user": "LW",
    "dateSent": "04/29/2025",
    "dateSentOrig": "03/28/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Conn Associates",
      "id": "PROV-3992"
    }
  },
  {
    "patient": {
      "name": "Arnold Oberbrunner",
      "id": "PAT-34180"
    },
    "serviceDate": "04/06/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$2207.66",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/13/2025",
      "time": "6:43 PM"
    },
    "user": "MB",
    "dateSent": "06/30/2025",
    "dateSentOrig": "05/25/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Raynor Healthcare",
      "id": "PROV-8140"
    }
  },
  {
    "patient": {
      "name": "Rochelle Lynch",
      "id": "PAT-64329"
    },
    "serviceDate": "06/11/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$6321.88",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "1:58 PM"
    },
    "user": "AD",
    "dateSent": "08/16/2025",
    "dateSentOrig": "08/16/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Hermann Family Practice",
      "id": "PROV-2602"
    }
  },
  {
    "patient": {
      "name": "Eloise Haley IV",
      "id": "PAT-71161"
    },
    "serviceDate": "03/18/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$8729.80",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "4:23 AM"
    },
    "user": "TG",
    "dateSent": "07/11/2025",
    "dateSentOrig": "07/07/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Hegmann Medical Group",
      "id": "PROV-1790"
    }
  },
  {
    "patient": {
      "name": "Georgia Hudson",
      "id": "PAT-79770"
    },
    "serviceDate": "04/21/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$14838.88",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "12:32 PM"
    },
    "user": "AR",
    "dateSent": "05/27/2025",
    "dateSentOrig": "05/27/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Okuneva Family Practice",
      "id": "PROV-3577"
    }
  },
  {
    "patient": {
      "name": "Kim Hintz",
      "id": "PAT-26332"
    },
    "serviceDate": "07/22/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$2418.36",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "8:14 AM"
    },
    "user": "TG",
    "dateSent": "08/09/2025",
    "dateSentOrig": "08/09/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Schroeder Medical Group",
      "id": "PROV-3935"
    }
  },
  {
    "patient": {
      "name": "Moses O'Hara Sr.",
      "id": "PAT-26094"
    },
    "serviceDate": "03/15/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$2488.41",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "7:05 AM"
    },
    "user": "BL",
    "dateSent": "07/18/2025",
    "dateSentOrig": "05/02/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Quitzon Healthcare",
      "id": "PROV-2686"
    }
  },
  {
    "patient": {
      "name": "Christy Kulas",
      "id": "PAT-16501"
    },
    "serviceDate": "06/14/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$11364.58",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "11:12 PM"
    },
    "user": "SW",
    "dateSent": "06/17/2025",
    "dateSentOrig": "06/16/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Becker Clinic",
      "id": "PROV-3544"
    }
  },
  {
    "patient": {
      "name": "Shari Ernser",
      "id": "PAT-48744"
    },
    "serviceDate": "04/26/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$5707.92",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "9:33 AM"
    },
    "user": "JB",
    "dateSent": "06/07/2025",
    "dateSentOrig": "04/26/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Hamill Clinic",
      "id": "PROV-6138"
    }
  },
  {
    "patient": {
      "name": "Clifford Beahan",
      "id": "PAT-53958"
    },
    "serviceDate": "04/17/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$2455.60",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "4:41 AM"
    },
    "user": "CCP",
    "dateSent": "08/10/2025",
    "dateSentOrig": "06/18/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Armstrong Associates",
      "id": "PROV-3759"
    }
  },
  {
    "patient": {
      "name": "Edna Daugherty",
      "id": "PAT-27786"
    },
    "serviceDate": "06/22/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$746.92",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "10:10 PM"
    },
    "user": "JO",
    "dateSent": "08/11/2025",
    "dateSentOrig": "08/11/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Batz Medical Group",
      "id": "PROV-2558"
    }
  },
  {
    "patient": {
      "name": "Stacy Satterfield",
      "id": "PAT-68031"
    },
    "serviceDate": "05/03/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$14415.42",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "6:31 AM"
    },
    "user": "LS",
    "dateSent": "07/21/2025",
    "dateSentOrig": "07/21/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Douglas Healthcare",
      "id": "PROV-7411"
    }
  },
  {
    "patient": {
      "name": "Darryl Ward",
      "id": "PAT-55614"
    },
    "serviceDate": "05/03/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$14222.51",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "6:11 PM"
    },
    "user": "VBC",
    "dateSent": "05/29/2025",
    "dateSentOrig": "05/16/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Kemmer Medical Group",
      "id": "PROV-7372"
    }
  },
  {
    "patient": {
      "name": "Dr. Jessie Wiegand",
      "id": "PAT-78914"
    },
    "serviceDate": "06/27/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$12098.33",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "7:02 PM"
    },
    "user": "GAB",
    "dateSent": "07/15/2025",
    "dateSentOrig": "07/15/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Flatley Healthcare",
      "id": "PROV-5592"
    }
  },
  {
    "patient": {
      "name": "Sophie Crooks",
      "id": "PAT-34317"
    },
    "serviceDate": "03/14/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$10997.13",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "3:49 AM"
    },
    "user": "JK",
    "dateSent": "08/04/2025",
    "dateSentOrig": "07/07/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Hilll Associates",
      "id": "PROV-7564"
    }
  },
  {
    "patient": {
      "name": "Edgar Stokes DDS",
      "id": "PAT-37922"
    },
    "serviceDate": "04/09/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$7267.07",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "9:38 AM"
    },
    "user": "SB",
    "dateSent": "08/10/2025",
    "dateSentOrig": "04/26/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Willms-Bartoletti Associates",
      "id": "PROV-8060"
    }
  },
  {
    "patient": {
      "name": "Darlene Connelly",
      "id": "PAT-47544"
    },
    "serviceDate": "08/04/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$7473.28",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "12:42 AM"
    },
    "user": "CA",
    "dateSent": "08/11/2025",
    "dateSentOrig": "08/06/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Ledner Medical Group",
      "id": "PROV-4895"
    }
  },
  {
    "patient": {
      "name": "Alice Wolf",
      "id": "PAT-65090"
    },
    "serviceDate": "06/13/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$8672.27",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "2:52 AM"
    },
    "user": "RCB",
    "dateSent": "07/11/2025",
    "dateSentOrig": "06/24/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Swift Associates",
      "id": "PROV-7025"
    }
  },
  {
    "patient": {
      "name": "Fernando Douglas",
      "id": "PAT-20656"
    },
    "serviceDate": "07/28/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$12305.27",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "5:59 PM"
    },
    "user": "AF",
    "dateSent": "08/29/2025",
    "dateSentOrig": "08/08/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Runolfsdottir Medical Group",
      "id": "PROV-4862"
    }
  },
  {
    "patient": {
      "name": "Nicholas Keeling",
      "id": "PAT-70635"
    },
    "serviceDate": "05/06/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$11095.88",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "9:52 PM"
    },
    "user": "RJ",
    "dateSent": "05/14/2025",
    "dateSentOrig": "05/06/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Pouros Clinic",
      "id": "PROV-5511"
    }
  },
  {
    "patient": {
      "name": "Jackie Keeling",
      "id": "PAT-18719"
    },
    "serviceDate": "04/27/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$10562.58",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "8:44 AM"
    },
    "user": "KC",
    "dateSent": "05/18/2025",
    "dateSentOrig": "05/18/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Wilkinson Associates",
      "id": "PROV-8275"
    }
  },
  {
    "patient": {
      "name": "Craig Wisozk",
      "id": "PAT-64075"
    },
    "serviceDate": "03/08/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$9598.42",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "9:53 PM"
    },
    "user": "PB",
    "dateSent": "05/10/2025",
    "dateSentOrig": "05/10/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Schmidt Associates",
      "id": "PROV-1240"
    }
  },
  {
    "patient": {
      "name": "Rosalie Ankunding Jr.",
      "id": "PAT-83313"
    },
    "serviceDate": "04/08/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$9349.76",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "9:58 AM"
    },
    "user": "AQB",
    "dateSent": "07/06/2025",
    "dateSentOrig": "05/16/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Schmidt Healthcare",
      "id": "PROV-4805"
    }
  },
  {
    "patient": {
      "name": "Mathew Daniel",
      "id": "PAT-22863"
    },
    "serviceDate": "03/27/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$12171.30",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "8:55 PM"
    },
    "user": "OLC",
    "dateSent": "06/04/2025",
    "dateSentOrig": "06/04/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Dietrich Associates",
      "id": "PROV-9695"
    }
  },
  {
    "patient": {
      "name": "Merle Schmeler",
      "id": "PAT-71067"
    },
    "serviceDate": "03/25/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$5486.52",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "8:03 PM"
    },
    "user": "CW",
    "dateSent": "05/26/2025",
    "dateSentOrig": "05/26/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Bins-Bailey Medical Group",
      "id": "PROV-8050"
    }
  },
  {
    "patient": {
      "name": "Jorge Heller",
      "id": "PAT-34223"
    },
    "serviceDate": "07/15/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$8538.29",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "11:33 AM"
    },
    "user": "JMT",
    "dateSent": "08/01/2025",
    "dateSentOrig": "07/20/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Parker Associates",
      "id": "PROV-1769"
    }
  },
  {
    "patient": {
      "name": "Guadalupe Purdy",
      "id": "PAT-24671"
    },
    "serviceDate": "05/26/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$6236.23",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "4:14 PM"
    },
    "user": "SLS",
    "dateSent": "07/04/2025",
    "dateSentOrig": "07/04/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Christiansen Associates",
      "id": "PROV-2277"
    }
  },
  {
    "patient": {
      "name": "Shawna D'Amore",
      "id": "PAT-17065"
    },
    "serviceDate": "06/03/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$13702.42",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "5:41 AM"
    },
    "user": "SS",
    "dateSent": "07/26/2025",
    "dateSentOrig": "07/26/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Jenkins Associates",
      "id": "PROV-8027"
    }
  },
  {
    "patient": {
      "name": "Lorene Dibbert",
      "id": "PAT-57571"
    },
    "serviceDate": "08/12/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$3288.67",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "7:15 PM"
    },
    "user": "DKC",
    "dateSent": "08/24/2025",
    "dateSentOrig": "08/24/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Mertz Clinic",
      "id": "PROV-9186"
    }
  },
  {
    "patient": {
      "name": "Miss Alma Will MD",
      "id": "PAT-76793"
    },
    "serviceDate": "08/21/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$13240.86",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "7:04 AM"
    },
    "user": "HS",
    "dateSent": "08/26/2025",
    "dateSentOrig": "08/26/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Paucek Associates",
      "id": "PROV-8351"
    }
  },
  {
    "patient": {
      "name": "Lila Schmeler",
      "id": "PAT-30298"
    },
    "serviceDate": "08/29/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$8983.50",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "9:17 AM"
    },
    "user": "CJR",
    "dateSent": "09/02/2025",
    "dateSentOrig": "08/30/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Kub Associates",
      "id": "PROV-7649"
    }
  },
  {
    "patient": {
      "name": "Felipe Kertzmann",
      "id": "PAT-95445"
    },
    "serviceDate": "06/07/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$5545.44",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "9:28 AM"
    },
    "user": "CG",
    "dateSent": "06/16/2025",
    "dateSentOrig": "06/16/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Trantow Clinic",
      "id": "PROV-6283"
    }
  },
  {
    "patient": {
      "name": "Ada Wuckert-Dickinson",
      "id": "PAT-30312"
    },
    "serviceDate": "03/09/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$2490.58",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "5:57 PM"
    },
    "user": "FBG",
    "dateSent": "04/18/2025",
    "dateSentOrig": "04/18/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Lueilwitz Healthcare",
      "id": "PROV-1362"
    }
  },
  {
    "patient": {
      "name": "Simon Ullrich",
      "id": "PAT-95194"
    },
    "serviceDate": "07/29/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$10449.94",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/13/2025",
      "time": "3:48 AM"
    },
    "user": "PP",
    "dateSent": "08/10/2025",
    "dateSentOrig": "08/09/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Howell Clinic",
      "id": "PROV-6541"
    }
  },
  {
    "patient": {
      "name": "Rodolfo Fritsch",
      "id": "PAT-53396"
    },
    "serviceDate": "08/15/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$4436.07",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "7:05 AM"
    },
    "user": "MP",
    "dateSent": "08/18/2025",
    "dateSentOrig": "08/18/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Swift Healthcare",
      "id": "PROV-3177"
    }
  },
  {
    "patient": {
      "name": "Jane McClure",
      "id": "PAT-62936"
    },
    "serviceDate": "07/08/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$10122.14",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "3:09 AM"
    },
    "user": "NT",
    "dateSent": "08/27/2025",
    "dateSentOrig": "08/27/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Weissnat Healthcare",
      "id": "PROV-9854"
    }
  },
  {
    "patient": {
      "name": "Dr. Rachel Frami I",
      "id": "PAT-15285"
    },
    "serviceDate": "06/30/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$14344.77",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "11:31 PM"
    },
    "user": "EB",
    "dateSent": "07/28/2025",
    "dateSentOrig": "07/28/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "McCullough Family Practice",
      "id": "PROV-6944"
    }
  },
  {
    "patient": {
      "name": "Nichole Tromp",
      "id": "PAT-79569"
    },
    "serviceDate": "05/07/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$11028.38",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "6:13 PM"
    },
    "user": "DJ",
    "dateSent": "06/30/2025",
    "dateSentOrig": "05/25/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Koch Clinic",
      "id": "PROV-5686"
    }
  },
  {
    "patient": {
      "name": "Jake Mills",
      "id": "PAT-71352"
    },
    "serviceDate": "03/13/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$8513.69",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "7:03 AM"
    },
    "user": "AH",
    "dateSent": "08/24/2025",
    "dateSentOrig": "04/11/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "O'Reilly Associates",
      "id": "PROV-9085"
    }
  },
  {
    "patient": {
      "name": "Scott Wuckert",
      "id": "PAT-36749"
    },
    "serviceDate": "08/09/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$6312.99",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "2:04 AM"
    },
    "user": "HK",
    "dateSent": "09/02/2025",
    "dateSentOrig": "08/29/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Kling Medical Group",
      "id": "PROV-2253"
    }
  },
  {
    "patient": {
      "name": "Brittany Christiansen",
      "id": "PAT-35948"
    },
    "serviceDate": "04/30/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$5249.03",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "5:14 AM"
    },
    "user": "RS",
    "dateSent": "07/23/2025",
    "dateSentOrig": "07/06/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Kuvalis Healthcare",
      "id": "PROV-8280"
    }
  },
  {
    "patient": {
      "name": "Tracy Wiegand",
      "id": "PAT-35114"
    },
    "serviceDate": "06/02/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$14857.65",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "2:22 AM"
    },
    "user": "JNW",
    "dateSent": "06/26/2025",
    "dateSentOrig": "06/26/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Gorczany Medical Group",
      "id": "PROV-8243"
    }
  },
  {
    "patient": {
      "name": "Kara Harris",
      "id": "PAT-20323"
    },
    "serviceDate": "07/07/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$9189.35",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "9:32 PM"
    },
    "user": "DSL",
    "dateSent": "08/30/2025",
    "dateSentOrig": "07/27/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Mosciski Clinic",
      "id": "PROV-3554"
    }
  },
  {
    "patient": {
      "name": "Rhonda Morar",
      "id": "PAT-36032"
    },
    "serviceDate": "04/08/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$9165.37",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "1:53 PM"
    },
    "user": "ZBW",
    "dateSent": "05/16/2025",
    "dateSentOrig": "05/01/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Jones Associates",
      "id": "PROV-4872"
    }
  },
  {
    "patient": {
      "name": "Winifred O'Reilly",
      "id": "PAT-93091"
    },
    "serviceDate": "08/03/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$7601.65",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "1:40 PM"
    },
    "user": "LDS",
    "dateSent": "08/19/2025",
    "dateSentOrig": "08/12/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Farrell Healthcare",
      "id": "PROV-7353"
    }
  },
  {
    "patient": {
      "name": "Mitchell Lueilwitz",
      "id": "PAT-90809"
    },
    "serviceDate": "05/17/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$4533.39",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "12:59 PM"
    },
    "user": "DF",
    "dateSent": "08/06/2025",
    "dateSentOrig": "08/06/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Miller Family Practice",
      "id": "PROV-2346"
    }
  },
  {
    "patient": {
      "name": "Marco Gorczany",
      "id": "PAT-13912"
    },
    "serviceDate": "08/01/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$5607.64",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/04/2025",
      "time": "9:36 PM"
    },
    "user": "AG",
    "dateSent": "08/24/2025",
    "dateSentOrig": "08/07/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Russel Medical Group",
      "id": "PROV-7479"
    }
  },
  {
    "patient": {
      "name": "Tara Halvorson",
      "id": "PAT-66331"
    },
    "serviceDate": "04/30/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$8877.13",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "4:43 AM"
    },
    "user": "RLH",
    "dateSent": "06/18/2025",
    "dateSentOrig": "06/18/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Champlin Medical Group",
      "id": "PROV-6330"
    }
  },
  {
    "patient": {
      "name": "Lionel Lynch",
      "id": "PAT-50493"
    },
    "serviceDate": "06/16/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$14058.92",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "1:45 AM"
    },
    "user": "MDK",
    "dateSent": "07/15/2025",
    "dateSentOrig": "07/15/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Blick Associates",
      "id": "PROV-7206"
    }
  },
  {
    "patient": {
      "name": "Nathan King DVM",
      "id": "PAT-15329"
    },
    "serviceDate": "08/23/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$3065.31",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "10:44 AM"
    },
    "user": "CW",
    "dateSent": "09/02/2025",
    "dateSentOrig": "09/02/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Barrows Medical Group",
      "id": "PROV-5027"
    }
  },
  {
    "patient": {
      "name": "Penny Nader",
      "id": "PAT-82152"
    },
    "serviceDate": "06/17/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$4876.42",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "5:12 PM"
    },
    "user": "GB",
    "dateSent": "08/06/2025",
    "dateSentOrig": "08/06/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Spencer Associates",
      "id": "PROV-3653"
    }
  },
  {
    "patient": {
      "name": "Darryl Larson",
      "id": "PAT-36120"
    },
    "serviceDate": "04/22/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$12974.81",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "8:56 PM"
    },
    "user": "IAL",
    "dateSent": "08/20/2025",
    "dateSentOrig": "07/30/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Heidenreich Family Practice",
      "id": "PROV-1969"
    }
  },
  {
    "patient": {
      "name": "Melba Schamberger",
      "id": "PAT-74433"
    },
    "serviceDate": "05/16/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$8548.27",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "8:11 PM"
    },
    "user": "IJM",
    "dateSent": "08/08/2025",
    "dateSentOrig": "08/08/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Mitchell Healthcare",
      "id": "PROV-1737"
    }
  },
  {
    "patient": {
      "name": "Wilfred Hamill-Crona DVM",
      "id": "PAT-29326"
    },
    "serviceDate": "06/02/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$2031.50",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "10:09 PM"
    },
    "user": "DK",
    "dateSent": "08/03/2025",
    "dateSentOrig": "08/03/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Koelpin Associates",
      "id": "PROV-4203"
    }
  },
  {
    "patient": {
      "name": "Ronnie Klocko",
      "id": "PAT-15492"
    },
    "serviceDate": "03/28/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$13690.37",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "1:46 PM"
    },
    "user": "SKO",
    "dateSent": "06/15/2025",
    "dateSentOrig": "06/15/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Harris-Cassin Healthcare",
      "id": "PROV-5857"
    }
  },
  {
    "patient": {
      "name": "Chester Reinger",
      "id": "PAT-42080"
    },
    "serviceDate": "06/12/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$12175.08",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "11:53 AM"
    },
    "user": "JEO",
    "dateSent": "06/23/2025",
    "dateSentOrig": "06/23/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Murray Associates",
      "id": "PROV-2680"
    }
  },
  {
    "patient": {
      "name": "Ricardo Schuppe",
      "id": "PAT-49933"
    },
    "serviceDate": "07/21/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$13540.39",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "7:06 PM"
    },
    "user": "QK",
    "dateSent": "09/02/2025",
    "dateSentOrig": "09/02/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Roberts Clinic",
      "id": "PROV-8817"
    }
  },
  {
    "patient": {
      "name": "Mrs. Jo Kunze",
      "id": "PAT-17963"
    },
    "serviceDate": "04/06/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$1143.49",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "2:12 PM"
    },
    "user": "DLN",
    "dateSent": "06/14/2025",
    "dateSentOrig": "04/13/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Ryan Associates",
      "id": "PROV-4467"
    }
  },
  {
    "patient": {
      "name": "Marianne Armstrong Sr.",
      "id": "PAT-71089"
    },
    "serviceDate": "06/17/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$6330.80",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "8:47 PM"
    },
    "user": "KNG",
    "dateSent": "07/30/2025",
    "dateSentOrig": "07/30/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Bahringer Healthcare",
      "id": "PROV-3929"
    }
  },
  {
    "patient": {
      "name": "Kent Goldner",
      "id": "PAT-16234"
    },
    "serviceDate": "07/10/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$2096.61",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "11:57 PM"
    },
    "user": "ES",
    "dateSent": "08/28/2025",
    "dateSentOrig": "08/17/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Grimes Associates",
      "id": "PROV-9402"
    }
  },
  {
    "patient": {
      "name": "Duane Zieme",
      "id": "PAT-37455"
    },
    "serviceDate": "06/30/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$7211.48",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "11:01 PM"
    },
    "user": "RB",
    "dateSent": "07/08/2025",
    "dateSentOrig": "07/08/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Rau Healthcare",
      "id": "PROV-9575"
    }
  },
  {
    "patient": {
      "name": "Holly Schmidt",
      "id": "PAT-60041"
    },
    "serviceDate": "07/14/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$5349.60",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "11:35 AM"
    },
    "user": "DO",
    "dateSent": "07/15/2025",
    "dateSentOrig": "07/15/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Rippin Associates",
      "id": "PROV-9527"
    }
  },
  {
    "patient": {
      "name": "Vincent Hickle",
      "id": "PAT-24968"
    },
    "serviceDate": "06/18/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$11464.76",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/13/2025",
      "time": "5:22 AM"
    },
    "user": "DR",
    "dateSent": "06/22/2025",
    "dateSentOrig": "06/21/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Ledner Associates",
      "id": "PROV-6329"
    }
  },
  {
    "patient": {
      "name": "Dr. Rogelio Leannon",
      "id": "PAT-69798"
    },
    "serviceDate": "07/04/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$3523.10",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "2:05 AM"
    },
    "user": "SRK",
    "dateSent": "07/21/2025",
    "dateSentOrig": "07/21/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Buckridge Healthcare",
      "id": "PROV-6299"
    }
  },
  {
    "patient": {
      "name": "Lindsey Beahan",
      "id": "PAT-86314"
    },
    "serviceDate": "03/25/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$4691.52",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "4:35 AM"
    },
    "user": "NDR",
    "dateSent": "07/17/2025",
    "dateSentOrig": "04/23/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Harvey Associates",
      "id": "PROV-9342"
    }
  }
];

export default insuranceClaimsData;
