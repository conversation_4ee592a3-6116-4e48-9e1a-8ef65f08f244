import { ClaimRowData } from '@/components/ClaimRow';

/**
 * Insurance claims data utility for Next.js application
 * Provides access to statically generated insurance data
 */

export interface InsuranceDataMeta {
  generatedAt: string;
  totalRecords: number;
  fakerSeed: number;
  version: string;
}

export interface InsuranceDataSet {
  metadata: InsuranceDataMeta;
  claims: ClaimRowData[];
}

// Import generated data at build time
let cachedData: InsuranceDataSet | null = null;

function loadData(): InsuranceDataSet {
  if (!cachedData) {
    try {
      // Use dynamic import for JSON data
      // eslint-disable-next-line @typescript-eslint/no-require-imports
      cachedData = require('./insurance-claims.json') as InsuranceDataSet;
    } catch {
      console.warn('No generated data found. Run npm run generate:data first.');
      // Return empty dataset as fallback
      cachedData = {
        metadata: {
          generatedAt: new Date().toISOString(),
          totalRecords: 0,
          fakerSeed: 42,
          version: '1.0.0'
        },
        claims: []
      };
    }
  }
  return cachedData;
}

/**
 * Get all insurance claims data
 * This data is generated at build time for optimal performance
 */
export function getAllClaims(): ClaimRowData[] {
  return loadData().claims;
}

/**
 * Get paginated claims data
 */
export function getPaginatedClaims(page: number, itemsPerPage: number): {
  claims: ClaimRowData[];
  totalPages: number;
  currentPage: number;
  totalItems: number;
} {
  const allClaims = getAllClaims();
  const startIndex = (page - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  
  return {
    claims: allClaims.slice(startIndex, endIndex),
    totalPages: Math.ceil(allClaims.length / itemsPerPage),
    currentPage: page,
    totalItems: allClaims.length
  };
}

/**
 * Get metadata about the generated dataset
 */
export function getDatasetMetadata(): InsuranceDataMeta {
  return loadData().metadata;
}

/**
 * Get claims by status
 */
export function getClaimsByStatus(status: string): ClaimRowData[] {
  return getAllClaims().filter(claim => claim.status === status);
}

/**
 * Get claims by insurance carrier
 */
export function getClaimsByCarrier(carrierName: string): ClaimRowData[] {
  return getAllClaims().filter(claim => 
    claim.insuranceCarrier.carrierName === carrierName
  );
}

/**
 * Get claims by sync status
 */
export function getClaimsBySyncStatus(syncStatus: string): ClaimRowData[] {
  return getAllClaims().filter(claim => 
    claim.pmsSyncStatus.status === syncStatus
  );
}

/**
 * Search claims by patient name or ID
 */
export function searchClaims(query: string): ClaimRowData[] {
  const searchTerm = query.toLowerCase();
  return getAllClaims().filter(claim => 
    claim.patient.name.toLowerCase().includes(searchTerm) ||
    claim.patient.id.toLowerCase().includes(searchTerm) ||
    claim.provider.name.toLowerCase().includes(searchTerm)
  );
}

/**
 * Get summary statistics about the claims
 */
export function getClaimsStats() {
  const claims = getAllClaims();
  
  // Calculate status distribution
  const statusCounts = claims.reduce((acc, claim) => {
    acc[claim.status] = (acc[claim.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  // Calculate sync status distribution
  const syncStatusCounts = claims.reduce((acc, claim) => {
    acc[claim.pmsSyncStatus.status] = (acc[claim.pmsSyncStatus.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  // Calculate total amounts
  const totalAmount = claims.reduce((sum, claim) => {
    const amount = parseFloat(claim.amount.replace('$', '').replace(',', ''));
    return sum + amount;
  }, 0);
  
  return {
    totalClaims: claims.length,
    statusDistribution: statusCounts,
    syncStatusDistribution: syncStatusCounts,
    totalAmount: `$${totalAmount.toLocaleString('en-US', { minimumFractionDigits: 2 })}`,
    averageAmount: `$${(totalAmount / claims.length).toLocaleString('en-US', { minimumFractionDigits: 2 })}`
  };
}
