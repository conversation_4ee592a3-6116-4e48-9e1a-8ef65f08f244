export interface InsuranceRecord {
  id: string;
  patient: {
    name: string;
    id: string;
  };
  serviceDate: string;
  insuranceCarrier: {
    name: string;
    type: 'Primary' | 'Secondary' | 'Tertiary';
  };
  amount: string;
  status: string;
  lastUpdated: {
    date: string;
    time: string;
  };
  user: string;
  dateSent: string;
  dateSentOrig: string;
  pmsSync: {
    status: 'Not synced' | 'Synced' | 'Pending' | 'Error';
    statusText: string;
    isModifiedToday: boolean;
  };
  provider: {
    name: string;
    id: string;
  };
}

export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  rowsPerPage: number;
  totalRows: number;
}

export interface TableColumn {
  key: keyof InsuranceRecord | string;
  label: string;
  sortable?: boolean;
  width?: string;
}

export interface SortConfig {
  key: string;
  direction: 'asc' | 'desc';
}

export interface InsuranceClaimsTableProps {
  data?: InsuranceRecord[];
  columns?: TableColumn[];
  className?: string;
  initialRowsPerPage?: number;
  onSort?: (column: string, direction: 'asc' | 'desc') => void;
  onFilter?: (filters: Record<string, string>) => void;
  onPageChange?: (page: number) => void;
  onRowsPerPageChange?: (rowsPerPage: number) => void;
  onRowClick?: (record: InsuranceRecord) => void;
  loading?: boolean;
  error?: string | null;
}
