"use client";

import React from "react";

export interface TableColumn {
  key: string;
  label: string;
  width: string;
}

export interface DataTableProps {
  columns: TableColumn[];
  data: Record<string, string | number>[];
  className?: string;
}

/**
 * Robust DataTable component using CSS Grid
 * - Automatically calculates minimum width
 * - Maintains consistent styling
 * - Provides type safety
 * - Makes columns easily configurable
 */
export default function DataTable({ columns, data, className = "" }: DataTableProps) {
  // Generate grid template from column widths
  const gridTemplate = columns.map(col => col.width).join(' ');
  
  // Calculate total minimum width (column widths + gaps + padding)
  const totalColumnWidth = columns.reduce((sum, col) => {
    const width = parseInt(col.width.replace('px', ''));
    return sum + width;
  }, 0);
  
  // Add space for gaps (20px * (columns - 1)) and some padding
  const gapSpace = (columns.length - 1) * 20; // 20px gap between columns
  const minWidth = totalColumnWidth + gapSpace + 44; // extra padding from left and right 

  return (
    <div className="w-full bg-white rounded-[20px] shadow-lg overflow-hidden" style={{ maxWidth: `${minWidth}px` }}>
      <div className={`overflow-x-auto ${className}`}>
        <div 
          className="grid gap-5 px-[24px]"
          style={{ gridTemplateColumns: gridTemplate }}
        >
          {/* Header Row */}
          {columns.map((column) => (
            <div 
              key={`header-${column.key}`}
              className="text-left py-[29px] font-medium text-[14px] text-[#546661]"
            >
              {column.label}
            </div>
          ))}

          {/* Data Rows */}
          {data.map((row, rowIndex) => 
            columns.map((column) => {
              const cellValue = row[column.key] || '-';
              const isMultiLine = typeof cellValue === 'string' && cellValue.includes('\n');
              
              return (
                <div 
                  key={`${rowIndex}-${column.key}`}
                  className="py-4 text-sm border-b border-gray-100"
                >
                  {isMultiLine ? (
                    <div>
                      {cellValue.split('\n').map((line, lineIndex) => (
                        <div key={lineIndex} className={lineIndex > 0 ? 'text-[#112A24]' : ''}>
                          {line}
                        </div>
                      ))}
                    </div>
                  ) : (
                    cellValue
                  )}
                </div>
              );
            })
          )}
        </div>
      </div>
    </div>
  );
}
