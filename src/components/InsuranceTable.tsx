"use client";

import React, { useState } from 'react';
import { InsuranceRecord, PaginationInfo } from '@/types/insurance';
import { WarningIcon } from '@/components/icons/WarningIcon';
import { Pagination } from '@/components/Pagination';

interface InsuranceTableProps {
  data: InsuranceRecord[];
  className?: string;
}

export const InsuranceTable: React.FC<InsuranceTableProps> = ({ 
  data, 
  className = '' 
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage] = useState(20);

  const totalPages = Math.ceil(data.length / rowsPerPage);
  const startIndex = (currentPage - 1) * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const currentData = data.slice(startIndex, endIndex);

  const paginationInfo: PaginationInfo = {
    currentPage,
    totalPages,
    rowsPerPage,
    totalRows: data.length
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleRowsPerPageChange = (newRowsPerPage: number) => {
    // Implementation for changing rows per page
    console.log('Change rows per page to:', newRowsPerPage);
  };

  return (
    <div className={`insurance-table-container ${className}`}>
      {/* Table Header */}
      <div className="insurance-table-header">
        <div className="header-cell">Patient</div>
        <div className="header-cell">Service Date</div>
        <div className="header-cell">Insurance Carrier</div>
        <div className="header-cell">Amount</div>
        <div className="header-cell">Status</div>
        <div className="header-cell">Last Updated</div>
        <div className="header-cell">User</div>
        <div className="header-cell">Date Sent</div>
        <div className="header-cell">Date Sent Orig</div>
        <div className="header-cell">PMS Sync Status</div>
        <div className="header-cell">Provider</div>
      </div>

      {/* Table Body */}
      <div className="insurance-table-body">
        {currentData.map((record) => (
          <div 
            key={record.id} 
            className="insurance-table-row"
            tabIndex={0}
            role="row"
          >
            {/* Patient Column */}
            <div className="table-cell patient-cell">
              <div className="patient-name">{record.patient.name}</div>
              <div className="patient-id">{record.patient.id}</div>
            </div>

            {/* Service Date Column */}
            <div className="table-cell service-date-cell">
              <div className="service-date">{record.serviceDate}</div>
            </div>

            {/* Insurance Carrier Column */}
            <div className="table-cell insurance-carrier-cell">
              <div className="carrier-name">{record.insuranceCarrier.name}</div>
              <div className="insurance-badge">
                <span className="badge-text">{record.insuranceCarrier.type}</span>
              </div>
            </div>

            {/* Amount Column */}
            <div className="table-cell amount-cell">
              <div className="amount-text">{record.amount}</div>
            </div>

            {/* Status Column */}
            <div className="table-cell status-cell">
              <div className="status-text">{record.status}</div>
            </div>

            {/* Last Updated Column */}
            <div className="table-cell last-updated-cell">
              <div className="updated-date">{record.lastUpdated.date}</div>
              <div className="updated-time">{record.lastUpdated.time}</div>
            </div>

            {/* User Column */}
            <div className="table-cell user-cell">
              <div className="user-avatar">{record.user}</div>
            </div>

            {/* Date Sent Column */}
            <div className="table-cell date-sent-cell">
              <div className="date-sent">{record.dateSent}</div>
            </div>

            {/* Date Sent Orig Column */}
            <div className="table-cell date-sent-orig-cell">
              <div className="date-sent-orig">{record.dateSentOrig}</div>
            </div>

            {/* PMS Sync Status Column */}
            <div className="table-cell pms-sync-cell">
              <div className="pms-sync-badge">
                <WarningIcon className="warning-icon" />
                <span className="sync-status-text">{record.pmsSync.status}</span>
              </div>
              <div className="sync-status-modified">{record.pmsSync.statusText}</div>
            </div>

            {/* Provider Column */}
            <div className="table-cell provider-cell">
              <div className="provider-name">{record.provider.name}</div>
              <div className="provider-id">{record.provider.id}</div>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      <Pagination
        paginationInfo={paginationInfo}
        onPageChangeAction={handlePageChange}
        onRowsPerPageChangeAction={handleRowsPerPageChange}
      />
    </div>
  );
};

export default InsuranceTable;
