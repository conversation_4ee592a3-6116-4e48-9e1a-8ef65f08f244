import React from 'react';

interface InsuranceRecord {
  id: string;
  patient: {
    name: string;
    id: string;
  };
  serviceDate: string;
  insuranceCarrier: {
    name: string;
    type: string;
  };
  amount: string;
  status: string;
  lastUpdated: {
    date: string;
    time: string;
  };
  user: string;
  dateSent: string;
  dateSentOrig: string;
  pmsSync: {
    status: string;
    statusText: string;
  };
  provider: {
    name: string;
    id: string;
  };
}

const mockData: InsuranceRecord[] = [
  {
    id: '1',
    patient: { name: 'First Last', id: 'ID: 11060' },
    serviceDate: 'July 00, 2025',
    insuranceCarrier: { name: 'BCBS OF COLORADO FEP PPO INN', type: 'Primary' },
    amount: '$00,000',
    status: 'NCOF - RESUBMITTED',
    lastUpdated: { date: 'May 28, 2030', time: '11:36 AM' },
    user: 'AA',
    dateSent: 'Aug 28, 2025',
    dateSentOrig: 'Aug 28, 2025',
    pmsSync: { status: 'Not synced', statusText: 'Status modified today' },
    provider: { name: 'Dr. First Last', id: 'ID:56712349911' }
  },
  {
    id: '2',
    patient: { name: 'First Last', id: 'ID: 11060' },
    serviceDate: 'July 00, 2025',
    insuranceCarrier: { name: 'BCBS OF COLORADO FEP PPO INN', type: 'Primary' },
    amount: '$00,000',
    status: 'NCOF - RESUBMITTED',
    lastUpdated: { date: 'May 28, 2030', time: '11:36 AM' },
    user: 'AA',
    dateSent: 'Aug 28, 2025',
    dateSentOrig: 'Aug 28, 2025',
    pmsSync: { status: 'Not synced', statusText: 'Status modified today' },
    provider: { name: 'Dr. First Last', id: 'ID:56712349911' }
  },
  {
    id: '3',
    patient: { name: 'First Last', id: 'ID: 11060' },
    serviceDate: 'July 00, 2025',
    insuranceCarrier: { name: 'BCBS OF COLORADO FEP PPO INN', type: 'Primary' },
    amount: '$00,000',
    status: 'NCOF - RESUBMITTED',
    lastUpdated: { date: 'May 28, 2030', time: '11:36 AM' },
    user: 'AA',
    dateSent: 'Aug 28, 2025',
    dateSentOrig: 'Aug 28, 2025',
    pmsSync: { status: 'Not synced', statusText: 'Status modified today' },
    provider: { name: 'Dr. First Last', id: 'ID:56712349911' }
  },
  {
    id: '4',
    patient: { name: 'First Last', id: 'ID: 11060' },
    serviceDate: 'July 00, 2025',
    insuranceCarrier: { name: 'BCBS OF COLORADO FEP PPO INN', type: 'Primary' },
    amount: '$00,000',
    status: 'NCOF - RESUBMITTED',
    lastUpdated: { date: 'May 28, 2030', time: '11:36 AM' },
    user: 'AA',
    dateSent: 'Aug 28, 2025',
    dateSentOrig: 'Aug 28, 2025',
    pmsSync: { status: 'Not synced', statusText: 'Status modified today' },
    provider: { name: 'Dr. First Last', id: 'ID:56712349911' }
  },
  {
    id: '5',
    patient: { name: 'First Last', id: 'ID: 11060' },
    serviceDate: 'July 00, 2025',
    insuranceCarrier: { name: 'BCBS OF COLORADO FEP PPO INN', type: 'Primary' },
    amount: '$00,000',
    status: 'NCOF - RESUBMITTED',
    lastUpdated: { date: 'May 28, 2030', time: '11:36 AM' },
    user: 'AA',
    dateSent: 'Aug 28, 2025',
    dateSentOrig: 'Aug 28, 2025',
    pmsSync: { status: 'Not synced', statusText: 'Status modified today' },
    provider: { name: 'Dr. First Last', id: 'ID:56712349911' }
  },
  {
    id: '6',
    patient: { name: 'First Last', id: 'ID: 11060' },
    serviceDate: 'July 00, 2025',
    insuranceCarrier: { name: 'BCBS OF COLORADO FEP PPO INN', type: 'Primary' },
    amount: '$00,000',
    status: 'NCOF - RESUBMITTED',
    lastUpdated: { date: 'May 28, 2030', time: '11:36 AM' },
    user: 'AA',
    dateSent: 'Aug 28, 2025',
    dateSentOrig: 'Aug 28, 2025',
    pmsSync: { status: 'Not synced', statusText: 'Status modified today' },
    provider: { name: 'Dr. First Last', id: 'ID:56712349911' }
  },
  {
    id: '7',
    patient: { name: 'First Last', id: 'ID: 11060' },
    serviceDate: 'July 00, 2025',
    insuranceCarrier: { name: 'BCBS OF COLORADO FEP PPO INN', type: 'Primary' },
    amount: '$00,000',
    status: 'NCOF - RESUBMITTED',
    lastUpdated: { date: 'May 28, 2030', time: '11:36 AM' },
    user: 'AA',
    dateSent: 'Aug 28, 2025',
    dateSentOrig: 'Aug 28, 2025',
    pmsSync: { status: 'Not synced', statusText: 'Status modified today' },
    provider: { name: 'Dr. First Last', id: 'ID:56712349911' }
  }
];

const WarningIcon = () => (
  <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M7.5 1.5L1.5 13.5H13.5L7.5 1.5Z" stroke="#838580" strokeWidth="1" fill="none"/>
    <circle cx="7.5" cy="11" r="0.5" fill="#838580"/>
    <line x1="7.5" y1="6" x2="7.5" y2="9" stroke="#838580" strokeWidth="1"/>
  </svg>
);

export default function InsuranceTable() {
  return (
    <div className="insurance-table-container">
      {/* Table Header */}
      <div className="table-header">
        <div className="header-cell">Patient</div>
        <div className="header-cell">Service Date</div>
        <div className="header-cell">Insurance Carrier</div>
        <div className="header-cell">Amount</div>
        <div className="header-cell">Status</div>
        <div className="header-cell">Last Updated</div>
        <div className="header-cell">User</div>
        <div className="header-cell">Date Sent</div>
        <div className="header-cell">Date Sent Orig</div>
        <div className="header-cell">PMS Sync Status</div>
        <div className="header-cell">Provider</div>
      </div>

      {/* Table Rows */}
      {mockData.map((record) => (
        <div key={record.id} className="table-row">
          {/* Patient */}
          <div className="patient">
            <div className="patient-name">{record.patient.name}</div>
            <div className="patient-id">{record.patient.id}</div>
          </div>

          {/* Service Date */}
          <div className="service-date">
            <div className="date-text">{record.serviceDate}</div>
          </div>

          {/* Insurance Carrier */}
          <div className="insurance-carrier">
            <div className="carrier-name">{record.insuranceCarrier.name}</div>
            <div className="insurance-type-badge">
              <span className="badge-text">{record.insuranceCarrier.type}</span>
            </div>
          </div>

          {/* Amount */}
          <div className="amount">
            <div className="amount-text">{record.amount}</div>
          </div>

          {/* Status */}
          <div className="status">
            <div className="status-text">{record.status}</div>
          </div>

          {/* Last Updated */}
          <div className="last-updated">
            <div className="updated-date">{record.lastUpdated.date}</div>
            <div className="updated-time">{record.lastUpdated.time}</div>
          </div>

          {/* User */}
          <div className="user">
            <div className="user-avatar">{record.user}</div>
          </div>

          {/* Date Sent */}
          <div className="date-sent">
            <div className="sent-date">{record.dateSent}</div>
          </div>

          {/* Date Sent Orig */}
          <div className="date-sent-orig">
            <div className="sent-orig-date">{record.dateSentOrig}</div>
          </div>

          {/* PMS Sync Status */}
          <div className="pms-sync-status">
            <div className="pms-sync-badge">
              <WarningIcon />
              <span className="sync-status-text">{record.pmsSync.status}</span>
            </div>
            <div className="sync-status-modified">{record.pmsSync.statusText}</div>
          </div>

          {/* Provider */}
          <div className="provider">
            <div className="provider-name">{record.provider.name}</div>
            <div className="provider-id">{record.provider.id}</div>
          </div>
        </div>
      ))}

      {/* Pagination */}
      <div className="pagination">
        <div className="rows-per-page">
          <span>20</span>
          <span>Rows per page</span>
        </div>
        <div className="page-info">
          <span>Page 1 of 8</span>
        </div>
        <div className="pagination-controls">
          <button disabled title="First page">«</button>
          <button disabled title="Previous page">‹</button>
          <button title="Next page">›</button>
          <button title="Last page">»</button>
        </div>
      </div>
    </div>
  );
}
