"use client";

import React, { useState, useCallback, useMemo } from 'react';
import { InsuranceRecord, TableColumn, PaginationInfo, SortConfig, InsuranceClaimsTableProps } from '@/types/insurance';
import { TableHeader } from './TableHeader';
import { ClaimRow } from './ClaimRow';
import { Pagination } from './Pagination';

const SAMPLE_DATA: InsuranceRecord[] = [
  {
    id: '1',
    patient: { name: '<PERSON>', id: 'P001' },
    serviceDate: '2024-01-15',
    insuranceCarrier: { name: '<PERSON> Cross', type: 'Primary' },
    amount: '$12,000.00',
    status: 'Active',
    lastUpdated: { date: '2024-01-20', time: '10:30 AM' },
    user: 'Nora',
    dateSent: '2024-01-15',
    dateSentOrig: '2024-01-15',
    pmsSync: { status: 'Synced', statusText: 'Synced', isModifiedToday: true },
    provider: { name: '<PERSON><PERSON> <PERSON>', id: 'PR001' }
  },
  {
    id: '2',
    patient: { name: '<PERSON>', id: 'P002' },
    serviceDate: '2024-01-10',
    insuranceCarrier: { name: 'Aetna', type: 'Primary' },
    amount: '$8,500.50',
    status: 'Pending',
    lastUpdated: { date: '2024-01-18', time: '2:15 PM' },
    user: 'Mike',
    dateSent: '2024-01-10',
    dateSentOrig: '2024-01-10',
    pmsSync: { status: 'Not synced', statusText: 'Not synced', isModifiedToday: false },
    provider: { name: 'Dr. Smith', id: 'PR002' }
  },
  {
    id: '3',
    patient: { name: 'Carol Davis', id: 'P003' },
    serviceDate: '2023-12-28',
    insuranceCarrier: { name: 'UnitedHealth', type: 'Primary' },
    amount: '$25,000.00',
    status: 'Closed',
    lastUpdated: { date: '2024-01-12', time: '9:45 AM' },
    user: 'Sarah',
    dateSent: '2023-12-28',
    dateSentOrig: '2023-12-28',
    pmsSync: { status: 'Synced', statusText: 'Synced', isModifiedToday: true },
    provider: { name: 'Dr. Lee', id: 'PR003' }
  },
  {
    id: '4',
    patient: { name: 'David Wilson', id: 'P004' },
    serviceDate: '2024-01-08',
    insuranceCarrier: { name: 'Cigna', type: 'Primary' },
    amount: '$15,750.25',
    status: 'Active',
    lastUpdated: { date: '2024-01-22', time: '11:20 AM' },
    user: 'Nora',
    dateSent: '2024-01-08',
    dateSentOrig: '2024-01-08',
    pmsSync: { status: 'Pending', statusText: 'Pending', isModifiedToday: false },
    provider: { name: 'Dr. Brown', id: 'PR004' }
  },
  {
    id: '5',
    patient: { name: 'Eva Brown', id: 'P005' },
    serviceDate: '2024-01-05',
    insuranceCarrier: { name: 'Humana', type: 'Secondary' },
    amount: '$9,200.75',
    status: 'Under Review',
    lastUpdated: { date: '2024-01-19', time: '3:30 PM' },
    user: 'Tom',
    dateSent: '2024-01-05',
    dateSentOrig: '2024-01-05',
    pmsSync: { status: 'Synced', statusText: 'Synced', isModifiedToday: true },
    provider: { name: 'Dr. Garcia', id: 'PR005' }
  },
  {
    id: '6',
    patient: { name: 'Frank Miller', id: 'P006' },
    serviceDate: '2024-01-12',
    insuranceCarrier: { name: 'Medicare', type: 'Primary' },
    amount: '$18,500.00',
    status: 'Active',
    lastUpdated: { date: '2024-01-21', time: '1:45 PM' },
    user: 'Lisa',
    dateSent: '2024-01-12',
    dateSentOrig: '2024-01-12',
    pmsSync: { status: 'Not synced', statusText: 'Not synced', isModifiedToday: false },
    provider: { name: 'Dr. Davis', id: 'PR006' }
  },
  {
    id: '7',
    patient: { name: 'Grace Lee', id: 'P007' },
    serviceDate: '2024-01-14',
    insuranceCarrier: { name: 'Medicaid', type: 'Primary' },
    amount: '$6,800.25',
    status: 'Pending',
    lastUpdated: { date: '2024-01-17', time: '4:15 PM' },
    user: 'Mike',
    dateSent: '2024-01-14',
    dateSentOrig: '2024-01-14',
    pmsSync: { status: 'Synced', statusText: 'Synced', isModifiedToday: true },
    provider: { name: 'Dr. Martinez', id: 'PR007' }
  },
  {
    id: '8',
    patient: { name: 'Henry Garcia', id: 'P008' },
    serviceDate: '2023-12-30',
    insuranceCarrier: { name: 'BCBS', type: 'Primary' },
    amount: '$22,000.50',
    status: 'Closed',
    lastUpdated: { date: '2024-01-10', time: '8:30 AM' },
    user: 'Sarah',
    dateSent: '2023-12-30',
    dateSentOrig: '2023-12-30',
    pmsSync: { status: 'Synced', statusText: 'Synced', isModifiedToday: false },
    provider: { name: 'Dr. Johnson', id: 'PR008' }
  }
];

const TABLE_COLUMNS: TableColumn[] = [
  { key: 'patient', label: 'Patient', sortable: true },
  { key: 'serviceDate', label: 'Service Date', sortable: true },
  { key: 'insuranceCarrier', label: 'Insurance', sortable: true },
  { key: 'amount', label: 'Amount', sortable: true },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'lastUpdated', label: 'Last Updated', sortable: true },
  { key: 'user', label: 'User', sortable: true },
  { key: 'dateSent', label: 'Date Sent', sortable: true },
  { key: 'pmsSync', label: 'PMS', sortable: false },
  { key: 'provider', label: 'Provider', sortable: true }
];

export const InsuranceClaimsTable: React.FC<InsuranceClaimsTableProps> = React.memo(({
  data = SAMPLE_DATA,
  columns = TABLE_COLUMNS,
  initialRowsPerPage = 10,
  onRowClick,
  onSort,
  onFilter,
  onPageChange,
  onRowsPerPageChange,
  loading = false,
  error = null
}) => {
  const [sortConfig, setSortConfig] = useState<SortConfig | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(initialRowsPerPage);
  const [filterText, setFilterText] = useState('');

  // Sort data
  const sortedData = useMemo(() => {
    if (!sortConfig) return data;

    return [...data].sort((a, b) => {
      const aValue = a[sortConfig.key as keyof InsuranceRecord];
      const bValue = b[sortConfig.key as keyof InsuranceRecord];

      if (aValue === null || aValue === undefined) return 1;
      if (bValue === null || bValue === undefined) return -1;

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        const comparison = aValue.localeCompare(bValue);
        return sortConfig.direction === 'asc' ? comparison : -comparison;
      }

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        const comparison = aValue - bValue;
        return sortConfig.direction === 'asc' ? comparison : -comparison;
      }

      return 0;
    });
  }, [data, sortConfig]);

  // Filter data
  const filteredData = useMemo(() => {
    if (!filterText) return sortedData;

    return sortedData.filter(record =>
      Object.values(record).some(value =>
        value?.toString().toLowerCase().includes(filterText.toLowerCase())
      )
    );
  }, [sortedData, filterText]);

  // Paginate data
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * rowsPerPage;
    const endIndex = startIndex + rowsPerPage;
    return filteredData.slice(startIndex, endIndex);
  }, [filteredData, currentPage, rowsPerPage]);

  // Pagination info
  const paginationInfo: PaginationInfo = useMemo(() => ({
    currentPage,
    totalPages: Math.ceil(filteredData.length / rowsPerPage),
    totalRows: filteredData.length,
    rowsPerPage
  }), [currentPage, filteredData.length, rowsPerPage]);

  // Handle sorting
  const handleSort = useCallback((key: string) => {
    setSortConfig(current => {
      if (!current || current.key !== key) {
        const newConfig = { key, direction: 'asc' as const };
        onSort?.(key, 'asc');
        return newConfig;
      }
      
      if (current.direction === 'asc') {
        const newConfig = { key, direction: 'desc' as const };
        onSort?.(key, 'desc');
        return newConfig;
      }
      
      onSort?.(key, 'asc');
      return null;
    });
  }, [onSort]);

  // Handle page change
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
    onPageChange?.(page);
  }, [onPageChange]);

  // Handle rows per page change
  const handleRowsPerPageChange = useCallback((newRowsPerPage: number) => {
    setRowsPerPage(newRowsPerPage);
    setCurrentPage(1); // Reset to first page
    onRowsPerPageChange?.(newRowsPerPage);
  }, [onRowsPerPageChange]);

  // Handle row click
  const handleRowClick = useCallback((record: InsuranceRecord) => {
    onRowClick?.(record);
  }, [onRowClick]);

  // Handle filter
  const handleFilter = useCallback((text: string) => {
    setFilterText(text);
    setCurrentPage(1); // Reset to first page when filtering
    onFilter?.({ search: text });
  }, [onFilter]);

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
        <div className="text-center">
          <div className="text-red-500 text-lg font-medium mb-2">Error Loading Data</div>
          <div className="text-gray-600">{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {/* Header with search */}
      <div className="px-6 py-4 border-b border-gray-100">
        <div className="flex justify-between items-center">
          <h1 className="text-xl font-semibold text-gray-900">Insurance Claims</h1>
          <div className="relative">
            <input
              type="text"
              placeholder="Search claims..."
              value={filterText}
              onChange={(e) => handleFilter(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all duration-200"
            />
            <svg
              className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <TableHeader
            columns={columns}
            onSort={handleSort}
            sortColumn={sortConfig?.key}
            sortDirection={sortConfig?.direction}
          />
          <tbody className="divide-y divide-gray-100">
            {loading ? (
              <tr>
                <td colSpan={columns.length} className="px-6 py-12 text-center">
                  <div className="flex justify-center items-center space-x-2">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                    <span className="text-gray-600">Loading claims...</span>
                  </div>
                </td>
              </tr>
            ) : paginatedData.length === 0 ? (
              <tr>
                <td colSpan={columns.length} className="px-6 py-12 text-center">
                  <div className="text-gray-500">
                    {filterText ? 'No claims found matching your search.' : 'No claims data available.'}
                  </div>
                </td>
              </tr>
            ) : (
              paginatedData.map((record, index) => (
                <ClaimRow
                  key={record.id}
                  record={record}
                  index={index}
                  onRowClick={handleRowClick}
                />
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {!loading && paginatedData.length > 0 && (
        <Pagination
          paginationInfo={paginationInfo}
          onPageChangeAction={handlePageChange}
          onRowsPerPageChangeAction={handleRowsPerPageChange}
        />
      )}
    </div>
  );
});

InsuranceClaimsTable.displayName = 'InsuranceClaimsTable';
