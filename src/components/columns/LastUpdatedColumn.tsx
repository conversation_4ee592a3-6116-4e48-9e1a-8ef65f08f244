import React from "react";

export interface LastUpdatedColumnProps {
  date: string;
  time: string;
}

/**
 * Component for Last Updated column
 * Displays date on top, time below in gray
 */
export default function LastUpdatedColumn({ date, time }: LastUpdatedColumnProps) {
  return (
    <div className="py-4 text-sm">
      <div className="text-[#112A24] font-medium">{date}</div>
      <div className="text-[#74827F] text-[12px] mt-2 font-semibold">{time}</div>
    </div>
  );
}
