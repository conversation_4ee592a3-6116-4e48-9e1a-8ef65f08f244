import React from "react";

export interface InsuranceCarrierColumnProps {
  carrierName: string;
  planCategory: string;
}

/**
 * Component for Insurance Carrier column
 * Displays carrier name with plan category in a styled box below
 */
export default function InsuranceCarrierColumn({ 
  carrierName, 
  planCategory 
}: InsuranceCarrierColumnProps) {
  return (
    <div className="py-4 text-sm">
      <div className="text-[#112A24] font-medium">{carrierName}</div>
      <div className="mt-2">
        <div 
          className="text-[#4A9EFF] text-[12px] font-semibold flex flex-row justify-center items-center p-1 bg-[#EBF9FE] rounded"
        >
          {planCategory}
        </div>
      </div>
    </div>
  );
}
