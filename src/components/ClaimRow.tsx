import React from "react";
import PatientProviderColumn from "./columns/PatientProviderColumn";
import InsuranceCarrierColumn from "./columns/InsuranceCarrierColumn";
import LastUpdatedColumn from "./columns/LastUpdatedColumn";
import UserColumn from "./columns/UserColumn";
import PmsSyncStatusColumn from "./columns/PmsSyncStatusColumn";

export interface ClaimRowData {
  patient: string;
  serviceDate: string;
  insuranceCarrier: string;
  amount: string;
  status: string;
  lastUpdated: string;
  user: string;
  dateSent: string;
  dateSentOrig: string;
  pmsSyncStatus: string;
  provider: string;
}

export interface ClaimRowProps {
  data: ClaimRowData;
  columns: Array<{ key: string; width: string }>;
}

/**
 * ClaimRow component that renders a single row of claims data
 * Uses dedicated column components for better maintainability
 */
export default function ClaimRow({ data, columns }: ClaimRowProps) {
  const gridTemplate = columns.map(col => col.width).join(' ');

  // Helper function to parse patient/provider data
  const parsePatientProvider = (value: string) => {
    const lines = value.split('\n');
    const name = lines[0];
    const id = lines[1]?.replace('ID: ', '') || '';
    return { name, id };
  };

  // Helper function to parse insurance carrier data
  const parseInsuranceCarrier = (value: string) => {
    const lines = value.split('\n');
    return {
      carrierName: lines[0] || '',
      planCategory: lines[2] || ''
    };
  };

  // Helper function to parse last updated data
  const parseLastUpdated = (value: string) => {
    const lines = value.split('\n');
    return {
      date: lines[0] || '',
      time: lines[1] || ''
    };
  };

  // Helper function to parse PMS sync status
  const parsePmsSyncStatus = (value: string) => {
    const lines = value.split('\n');
    const statusLine = lines[0] || '';
    // Remove emoji/icon from the beginning and get the status text
    const status = statusLine.replace(/^[⭕✅]?\s*/, '').trim();
    // Determine if synced based on the status text or icon
    const isSynced = statusLine.includes('✅') || status.toLowerCase().includes('synced') && !status.toLowerCase().includes('not synced');
    return {
      status,
      description: lines[1] || '',
      isSynced
    };
  };

  const renderCell = (columnKey: string) => {
    switch (columnKey) {
      case 'patient':
        const patientData = parsePatientProvider(data.patient);
        return <PatientProviderColumn name={patientData.name} id={patientData.id} />;
      
      case 'provider':
        const providerData = parsePatientProvider(data.provider);
        return <PatientProviderColumn name={providerData.name} id={providerData.id} />;
      
      case 'insuranceCarrier':
        const insuranceData = parseInsuranceCarrier(data.insuranceCarrier);
        return (
          <InsuranceCarrierColumn 
            carrierName={insuranceData.carrierName}
            planCategory={insuranceData.planCategory}
          />
        );
      
      case 'lastUpdated':
        const lastUpdatedData = parseLastUpdated(data.lastUpdated);
        return <LastUpdatedColumn date={lastUpdatedData.date} time={lastUpdatedData.time} />;
      
      case 'user':
        return <UserColumn initials={data.user} />;
      
      case 'pmsSyncStatus':
        const syncStatusData = parsePmsSyncStatus(data.pmsSyncStatus);
        return (
          <PmsSyncStatusColumn 
            status={syncStatusData.status}
            description={syncStatusData.description}
            isSynced={syncStatusData.isSynced}
          />
        );
      
      default:
        // For simple text columns (serviceDate, amount, status, dateSent, dateSentOrig)
        return (
          <div className="py-4 text-sm">
            <div className="text-[#112A24] font-medium">{data[columnKey as keyof ClaimRowData]}</div>
          </div>
        );
    }
  };

  return (
    <div 
      className="grid gap-5 px-[24px] border-b border-gray-100"
      style={{ gridTemplateColumns: gridTemplate }}
    >
      {columns.map((column) => (
        <div key={column.key}>
          {renderCell(column.key)}
        </div>
      ))}
    </div>
  );
}
