"use client";

import React from 'react';
import { InsuranceRecord } from '@/types/insurance';

interface ClaimRowProps {
  record: InsuranceRecord;
  index: number;
  onRowClick?: (record: InsuranceRecord) => void;
}

export const ClaimRow: React.FC<ClaimRowProps> = React.memo(({
  record,
  index,
  onRowClick
}) => {
  const getPmsIcon = (status: string) => {
    if (status === 'Synced') {
      return (
        <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 24 24">
          <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
        </svg>
      );
    }
    return (
      <svg className="w-4 h-4 text-yellow-500" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/>
      </svg>
    );
  };

  return (
    <div
      className={`
        flex justify-between items-center px-6 py-5 border-b border-gray-50 transition-all duration-200
        hover:bg-gray-25 cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset
        ${index % 2 === 0 ? 'bg-white' : 'bg-gray-25'}
      `}
      onClick={() => onRowClick?.(record)}
      role="row"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onRowClick?.(record);
        }
      }}
    >
      {/* Patient */}
      <div className="flex flex-col items-start w-[72px]">
        <div className="text-sm font-normal text-gray-900 truncate w-full">
          {record.patient.name}
        </div>
        <div className="text-xs font-semibold text-gray-400 truncate w-full">
          {record.patient.id}
        </div>
      </div>

      {/* Service Date */}
      <div className="flex items-center w-[92px]">
        <div className="text-sm font-normal text-gray-900 truncate w-full text-center">
          {record.serviceDate}
        </div>
      </div>

      {/* Insurance Carrier */}
      <div className="flex flex-col items-start w-[121px] space-y-2">
        <div className="text-xs font-normal text-gray-900 leading-tight line-clamp-2 w-full">
          {record.insuranceCarrier.name}
        </div>
        <div className="flex items-center justify-center w-full">
          <span className="inline-flex items-center px-2 py-1 rounded text-xs font-semibold bg-blue-50 text-blue-600">
            {record.insuranceCarrier.type}
          </span>
        </div>
      </div>

      {/* Amount */}
      <div className="flex items-center w-[49px]">
        <div className="text-xs font-normal text-gray-900 truncate w-full text-center">
          {record.amount}
        </div>
      </div>

      {/* Status */}
      <div className="flex items-center w-[126px]">
        <div className="text-xs font-normal text-gray-900 truncate w-full text-center">
          {record.status}
        </div>
      </div>

      {/* Last Updated */}
      <div className="flex flex-col items-start w-[92px] space-y-1">
        <div className="text-sm font-normal text-gray-900 truncate w-full">
          {record.lastUpdated.date}
        </div>
        <div className="text-xs font-semibold text-gray-500 truncate w-full">
          {record.lastUpdated.time}
        </div>
      </div>

      {/* User */}
      <div className="flex items-center justify-center w-[30px]">
        <div className="w-7 h-7 bg-green-50 rounded-full flex items-center justify-center">
          <span className="text-sm font-semibold text-green-700">
            {record.user}
          </span>
        </div>
      </div>

      {/* Date Sent */}
      <div className="flex items-center w-[92px]">
        <div className="text-sm font-normal text-gray-900 truncate w-full text-center">
          {record.dateSent}
        </div>
      </div>

      {/* Date Sent Orig */}
      <div className="flex items-center w-[91px]">
        <div className="text-sm font-normal text-gray-900 truncate w-full text-center">
          {record.dateSentOrig}
        </div>
      </div>

      {/* PMS Sync Status */}
      <div className="flex flex-col items-center w-[111px] space-y-1">
        <div className="flex items-center justify-center space-x-2 px-2 py-1 bg-gray-100 rounded w-full">
          {getPmsIcon(record.pmsSync.status)}
          <span className="text-xs font-semibold text-gray-600 truncate">
            {record.pmsSync.status}
          </span>
        </div>
        <div className="text-xs font-medium text-gray-500 text-center w-full">
          {record.pmsSync.statusText}
        </div>
      </div>

      {/* Provider */}
      <div className="flex flex-col items-start w-[91px]">
        <div className="text-sm font-normal text-gray-900 truncate w-full">
          {record.provider.name}
        </div>
        <div className="text-xs font-semibold text-center text-gray-400 truncate w-full">
          {record.provider.id}
        </div>
      </div>
    </div>
  );
});

ClaimRow.displayName = 'ClaimRow';
