import React from 'react';

interface PMSIconProps {
  className?: string;
  status: 'Synced' | 'Not synced' | 'Pending' | 'Error';
}

export const PMSIcon: React.FC<PMSIconProps> = ({ 
  className,
  status 
}) => {
  const getIconProps = () => {
    switch (status) {
      case 'Synced':
        return {
          className: className || "w-4 h-4 text-green-500",
          path: <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        };
      case 'Pending':
        return {
          className: className || "w-4 h-4 text-yellow-500",
          path: <path d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        };
      case 'Error':
        return {
          className: className || "w-4 h-4 text-red-500",
          path: <path d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
        };
      case 'Not synced':
      default:
        return {
          className: className || "w-4 h-4 text-gray-400",
          path: <path d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364L18.364 5.636" />
        };
    }
  };

  const { className: iconClassName, path } = getIconProps();

  return (
    <svg 
      className={iconClassName} 
      fill="currentColor" 
      viewBox="0 0 24 24"
      aria-hidden="true"
    >
      {path}
    </svg>
  );
};
