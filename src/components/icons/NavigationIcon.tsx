import React from 'react';

interface NavigationIconProps {
  className?: string;
  direction: 'first' | 'prev' | 'next' | 'last';
}

export const NavigationIcon: React.FC<NavigationIconProps> = ({ 
  className = "w-4 h-4", 
  direction 
}) => {
  const getPath = () => {
    switch (direction) {
      case 'first':
        return (
          <>
            <path d="M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6 1.41-1.41z"/>
            <path d="M6 6h2v12H6z"/>
          </>
        );
      case 'prev':
        return <path d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"/>;
      case 'next':
        return <path d="M8.59 16.59L13.17 12L8.59 7.41L10 6l6 6-6 6-1.41-1.41z"/>;
      case 'last':
        return (
          <>
            <path d="M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6-1.41 1.41z"/>
            <path d="M16 6h2v12h-2z"/>
          </>
        );
      default:
        return null;
    }
  };

  return (
    <svg 
      className={className} 
      fill="currentColor" 
      viewBox="0 0 24 24"
      aria-hidden="true"
    >
      {getPath()}
    </svg>
  );
};
