import React from 'react';

interface SortIconProps {
  className?: string;
  direction?: 'asc' | 'desc' | null;
}

export const SortIcon: React.FC<SortIconProps> = ({ 
  className = "w-4 h-4", 
  direction 
}) => {
  return (
    <svg
      className={className}
      fill="none"
      stroke="currentColor"
      viewBox="0 0 20 20"
      strokeWidth={2}
      aria-hidden="true"
    >
      {direction === 'asc' ? (
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M5 15l7-7 7 7"
        />
      ) : direction === 'desc' ? (
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M19 9l-7 7-7-7"
        />
      ) : (
        <>
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M5 15l7-7 7 7"
            opacity="0.5"
          />
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M19 9l-7 7-7-7"
            opacity="0.5"
          />
        </>
      )}
    </svg>
  );
};
