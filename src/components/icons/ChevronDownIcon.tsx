import React from 'react';

interface ChevronDownIconProps {
  className?: string;
  size?: number;
}

export const ChevronDownIcon: React.FC<ChevronDownIconProps> = ({ 
  className = "w-4 h-4", 
  size 
}) => {
  const sizeClass = size ? `w-${size} h-${size}` : className;
  
  return (
    <svg 
      className={sizeClass} 
      fill="currentColor" 
      viewBox="0 0 16 16"
      aria-hidden="true"
    >
      <path d="M8 4L6 6L8 8L10 6L8 4Z"/>
      <path d="M8 8L6 10L8 12L10 10L8 8Z"/>
    </svg>
  );
};
