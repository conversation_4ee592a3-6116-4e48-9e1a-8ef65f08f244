import React from 'react';

interface WarningIconProps {
  className?: string;
  size?: number;
}

export const WarningIcon: React.FC<WarningIconProps> = ({ 
  className = '', 
  size = 15 
}) => {
  return (
    <svg 
      width={size} 
      height={size} 
      viewBox="0 0 15 15" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path 
        d="M7.5 1.5L1.5 13.5H13.5L7.5 1.5Z" 
        stroke="currentColor" 
        strokeWidth="1" 
        fill="none"
      />
      <circle cx="7.5" cy="11" r="0.5" fill="currentColor"/>
      <line x1="7.5" y1="6" x2="7.5" y2="9" stroke="currentColor" strokeWidth="1"/>
    </svg>
  );
};
