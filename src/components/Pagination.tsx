"use client";

import React from 'react';
import { PaginationInfo } from '@/types/insurance';

interface PaginationProps {
  paginationInfo: PaginationInfo;
  onPageChangeAction: (page: number) => void;
  onRowsPerPageChangeAction: (rowsPerPage: number) => void;
}

export const Pagination: React.FC<PaginationProps> = ({
  paginationInfo,
  onPageChangeAction,
  onRowsPerPageChangeAction
}) => {
  const { currentPage, totalPages, rowsPerPage } = paginationInfo;

  const handleFirstPage = () => onPageChangeAction(1);
  const handlePrevPage = () => onPageChangeAction(Math.max(1, currentPage - 1));
  const handleNextPage = () => onPageChangeAction(Math.min(totalPages, currentPage + 1));
  const handleLastPage = () => onPageChangeAction(totalPages);

  return (
    <div className="pagination-container">
      <div className="pagination-info">
        <div className="rows-per-page">
          <span>{rowsPerPage}</span>
          <span>Rows per page</span>
        </div>
      </div>

      <div className="page-info">
        Page {currentPage} of {totalPages}
      </div>

      <div className="pagination-controls">
        <button
          onClick={handleFirstPage}
          disabled={currentPage === 1}
          className="pagination-button"
          title="First page"
        >
          ⟪
        </button>
        <button
          onClick={handlePrevPage}
          disabled={currentPage === 1}
          className="pagination-button"
          title="Previous page"
        >
          ⟨
        </button>
        <button
          onClick={handleNextPage}
          disabled={currentPage === totalPages}
          className="pagination-button"
          title="Next page"
        >
          ⟩
        </button>
        <button
          onClick={handleLastPage}
          disabled={currentPage === totalPages}
          className="pagination-button"
          title="Last page"
        >
          ⟫
        </button>
      </div>
    </div>
  );
};
