"use client";

import React from 'react';
import { PaginationInfo } from '@/types/insurance';

interface PaginationProps {
  paginationInfo: PaginationInfo;
  onPageChangeAction: (page: number) => void;
  onRowsPerPageChangeAction: (rowsPerPage: number) => void;
}

export const Pagination: React.FC<PaginationProps> = React.memo(({
  paginationInfo,
  onPageChangeAction,
  onRowsPerPageChangeAction
}) => {
  const { currentPage, totalPages, rowsPerPage } = paginationInfo;

  const handleFirstPage = () => onPageChangeAction(1);
  const handlePrevPage = () => onPageChangeAction(Math.max(1, currentPage - 1));
  const handleNextPage = () => onPageChangeAction(Math.min(totalPages, currentPage + 1));
  const handleLastPage = () => onPageChangeAction(totalPages);

  const handleRowsPerPageChange = (newRowsPerPage: number) => {
    onRowsPerPageChangeAction(newRowsPerPage);
  };

  return (
    <div className="flex justify-between items-center px-6 py-5 bg-white border-t border-gray-100">
      {/* Left: Rows per page */}
      <div className="flex items-center space-x-2">
        <div className="flex items-end space-x-1">
          <span className="text-sm font-normal text-green-700">{rowsPerPage}</span>
          <svg className="w-4 h-4 text-green-700" fill="currentColor" viewBox="0 0 16 16">
            <path d="M8 4L6 6L8 8L10 6L8 4Z"/>
            <path d="M8 8L6 10L8 12L10 10L8 8Z"/>
          </svg>
        </div>
        <span className="text-sm font-normal text-gray-600">Rows per page</span>
      </div>

      {/* Center: Page info and controls */}
      <div className="flex items-center space-x-3">
        <span className="text-sm font-normal text-gray-600">
          Page {currentPage} of {totalPages}
        </span>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={handleFirstPage}
            disabled={currentPage === 1}
            className="p-2 border border-gray-200 rounded-lg shadow-sm bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="First page"
          >
            <svg className="w-4 h-4 text-green-700" fill="currentColor" viewBox="0 0 24 24">
              <path d="M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6 1.41-1.41z"/>
              <path d="M6 6h2v12H6z"/>
            </svg>
          </button>
          
          <button
            onClick={handlePrevPage}
            disabled={currentPage === 1}
            className="p-2 border border-gray-200 rounded-lg shadow-sm bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="Previous page"
          >
            <svg className="w-4 h-4 text-green-700" fill="currentColor" viewBox="0 0 24 24">
              <path d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"/>
            </svg>
          </button>
          
          <button
            onClick={handleNextPage}
            disabled={currentPage === totalPages}
            className="p-2 border border-gray-200 rounded-lg shadow-sm bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="Next page"
          >
            <svg className="w-4 h-4 text-green-700" fill="currentColor" viewBox="0 0 24 24">
              <path d="M8.59 16.59L13.17 12L8.59 7.41L10 6l6 6-6 6-1.41-1.41z"/>
            </svg>
          </button>
          
          <button
            onClick={handleLastPage}
            disabled={currentPage === totalPages}
            className="p-2 border border-gray-200 rounded-lg shadow-sm bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="Last page"
          >
            <svg className="w-4 h-4 text-green-700" fill="currentColor" viewBox="0 0 24 24">
              <path d="M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6-1.41 1.41z"/>
              <path d="M16 6h2v12h-2z"/>
            </svg>
          </button>
        </div>
      </div>

      {/* Right: Empty space for layout balance */}
      <div className="w-[140px]"></div>
    </div>
  );
});

Pagination.displayName = 'Pagination';
