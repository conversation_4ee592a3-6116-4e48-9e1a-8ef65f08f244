"use client";

import React from 'react';
import { TableColumn } from '@/types/insurance';
import { SortIcon } from '@/components/icons';

interface TableHeaderProps {
  columns: TableColumn[];
  onSort?: (column: string, direction: 'asc' | 'desc') => void;
  sortColumn?: string;
  sortDirection?: 'asc' | 'desc';
}

export const TableHeader: React.FC<TableHeaderProps> = React.memo(({
  columns,
  onSort,
  sortColumn,
  sortDirection
}) => {
  const handleSort = (column: TableColumn) => {
    if (!column.sortable || !onSort) return;

    const currentDirection = sortColumn === column.key ? sortDirection : null;
    const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
    
    onSort(column.key as string, newDirection);
  };

  const handleKeyDown = (event: React.KeyboardEvent, column: TableColumn) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleSort(column);
    }
  };

  return (
    <thead className="bg-gray-50 border-b border-gray-100">
      <tr>
        {columns.map((column) => (
          <th
            key={column.key}
            className={`
              px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider
              ${column.sortable ? 'cursor-pointer hover:bg-gray-100 select-none group' : ''}
            `}
            style={{ width: column.width }}
            onClick={() => column.sortable && handleSort(column)}
            onKeyDown={(e) => column.sortable && handleKeyDown(e, column)}
            role={column.sortable ? 'button' : 'columnheader'}
            tabIndex={column.sortable ? 0 : undefined}
            aria-label={column.sortable ? `Sort by ${column.label}` : column.label}
            aria-sort={
              sortColumn === column.key 
                ? sortDirection === 'asc' ? 'ascending' : 'descending'
                : 'none'
            }
          >
            <div className="flex items-center justify-between">
              <span>{column.label}</span>
              {column.sortable && (
                <SortIcon 
                  className="ml-2 w-4 h-4 text-gray-400 group-hover:text-gray-600" 
                  direction={
                    sortColumn === column.key 
                      ? sortDirection 
                      : null
                  }
                />
              )}
            </div>
          </th>
        ))}
      </tr>
    </thead>
  );
});

TableHeader.displayName = 'TableHeader';
