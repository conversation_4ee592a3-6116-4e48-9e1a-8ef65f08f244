import { getPaginatedClaims, getDatasetMetadata, getClaimsStats } from '@/data/insurance-data';
import { useState, useEffect } from 'react';

interface ClaimsStats {
  totalClaims: number;
  statusDistribution: Record<string, number>;
  syncStatusDistribution: Record<string, number>;
  totalAmount: string;
  averageAmount: string;
}

/**
 * Demo component to showcase the generated insurance data
 * Demonstrates Next.js build-time data generation with pagination
 */
export default function InsuranceDataDemo() {
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [stats, setStats] = useState<ClaimsStats | null>(null);

  useEffect(() => {
    // Load stats on component mount
    setStats(getClaimsStats());
  }, []);

  const { claims, totalPages, totalItems } = getPaginatedClaims(currentPage, rowsPerPage);
  const metadata = getDatasetMetadata();

  if (!stats) {
    return <div>Loading insurance data...</div>;
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Insurance Claims Dashboard
        </h1>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h2 className="text-lg font-semibold text-blue-900 mb-2">
            📊 Dataset Information
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="font-medium">Total Claims:</span>
              <br />
              <span className="text-blue-700">{stats.totalClaims}</span>
            </div>
            <div>
              <span className="font-medium">Total Amount:</span>
              <br />
              <span className="text-green-700">{stats.totalAmount}</span>
            </div>
            <div>
              <span className="font-medium">Average Amount:</span>
              <br />
              <span className="text-green-700">{stats.averageAmount}</span>
            </div>
            <div>
              <span className="font-medium">Generated:</span>
              <br />
              <span className="text-gray-600">
                {new Date(metadata.generatedAt).toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Status Distribution */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-3">Claim Status Distribution</h3>
          <div className="space-y-2">
            {Object.entries(stats.statusDistribution).map(([status, count]) => (
              <div key={status} className="flex justify-between items-center">
                <span className="text-sm font-medium">{status}</span>
                <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">
                  {count}
                </span>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-3">Sync Status Distribution</h3>
          <div className="space-y-2">
            {Object.entries(stats.syncStatusDistribution).map(([status, count]) => (
              <div key={status} className="flex justify-between items-center">
                <span className="text-sm font-medium">{status}</span>
                <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">
                  {count}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Pagination Controls */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center space-x-2">
          <label htmlFor="rowsPerPage" className="text-sm font-medium">
            Rows per page:
          </label>
          <select
            id="rowsPerPage"
            value={rowsPerPage}
            onChange={(e) => {
              setRowsPerPage(Number(e.target.value));
              setCurrentPage(1);
            }}
            className="border border-gray-300 rounded px-2 py-1 text-sm"
          >
            <option value={5}>5</option>
            <option value={10}>10</option>
            <option value={25}>25</option>
            <option value={50}>50</option>
          </select>
        </div>
        
        <div className="text-sm text-gray-600">
          Showing {((currentPage - 1) * rowsPerPage) + 1} to{' '}
          {Math.min(currentPage * rowsPerPage, totalItems)} of {totalItems} claims
        </div>
      </div>

      {/* Claims Table */}
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Patient
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Provider
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Insurance
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Sync Status
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {claims.map((claim) => (
                <tr key={claim.id} className="hover:bg-gray-50">
                  <td className="px-4 py-3 text-sm">
                    <div className="font-medium text-gray-900">{claim.patient.name}</div>
                    <div className="text-gray-500">{claim.patient.id}</div>
                  </td>
                  <td className="px-4 py-3 text-sm">
                    <div className="font-medium text-gray-900">{claim.provider.name}</div>
                    <div className="text-gray-500">{claim.provider.id}</div>
                  </td>
                  <td className="px-4 py-3 text-sm">
                    <div className="font-medium text-gray-900">
                      {claim.insuranceCarrier.carrierName}
                    </div>
                    <div className="text-gray-500">{claim.insuranceCarrier.planCategory}</div>
                  </td>
                  <td className="px-4 py-3 text-sm font-medium text-gray-900">
                    {claim.amount}
                  </td>
                  <td className="px-4 py-3 text-sm">
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                      claim.status === 'PAID' ? 'bg-green-100 text-green-800' :
                      claim.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' :
                      claim.status === 'DENIED' ? 'bg-red-100 text-red-800' :
                      'bg-blue-100 text-blue-800'
                    }`}>
                      {claim.status}
                    </span>
                  </td>
                  <td className="px-4 py-3 text-sm">
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                      claim.pmsSyncStatus.isSynced ? 'bg-green-100 text-green-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {claim.pmsSyncStatus.status}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex justify-between items-center mt-4">
        <button
          onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
          disabled={currentPage === 1}
          className="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
        >
          Previous
        </button>
        
        <span className="text-sm text-gray-600">
          Page {currentPage} of {totalPages}
        </span>
        
        <button
          onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
          disabled={currentPage === totalPages}
          className="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
        >
          Next
        </button>
      </div>
    </div>
  );
}
