"use client";

import React from 'react';
import { TableColumn } from '@/types/insurance';

interface TableHeaderProps {
  columns: TableColumn[];
  onSort?: (column: string, direction: 'asc' | 'desc') => void;
  sortColumn?: string;
  sortDirection?: 'asc' | 'desc';
}

export const TableHeader: React.FC<TableHeaderProps> = React.memo(({
  columns,
  onSort,
  sortColumn,
  sortDirection
}) => {
  const handleSort = (column: TableColumn) => {
    if (!column.sortable || !onSort) return;
    
    const newDirection = sortColumn === column.key && sortDirection === 'asc' ? 'desc' : 'asc';
    onSort(column.key, newDirection);
  };

  return (
    <div className="flex justify-between items-center px-6 py-5 bg-white shadow-sm border-b border-gray-100 rounded-t-2xl">
      {columns.map((column) => (
        <div
          key={column.key}
          className={`
            flex items-center justify-center text-sm font-normal text-gray-600
            ${column.sortable ? 'cursor-pointer hover:text-gray-800 transition-colors' : ''}
          `}
          style={{ width: column.width }}
          onClick={() => column.sortable && handleSort(column)}
          role={column.sortable ? 'button' : undefined}
          tabIndex={column.sortable ? 0 : undefined}
          aria-label={column.sortable ? `Sort by ${column.label}` : undefined}
        >
          <span>{column.label}</span>
          {column.sortable && sortColumn === column.key && (
            <svg
              className={`ml-1 w-3 h-3 transition-transform ${
                sortDirection === 'desc' ? 'rotate-180' : ''
              }`}
              fill="currentColor"
              viewBox="0 0 20 20"
              aria-hidden="true"
            >
              <path
                fillRule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          )}
        </div>
      ))}
    </div>
  );
});

TableHeader.displayName = 'TableHeader';
