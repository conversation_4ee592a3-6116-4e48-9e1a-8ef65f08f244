import React from "react";

export interface TableFooterProps {
  currentPage: number;
  totalPages: number;
  rowsPerPage: number;
  onPageChange: (page: number) => void;
  onRowsPerPageChange: (rowsPerPage: number) => void;
}

const ChevronLeftIcon = () => (
  <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M5.22708 8.60202L10.8521 2.97702C10.9043 2.92476 10.9664 2.8833 11.0347 2.85502C11.103 2.82674 11.1761 2.81218 11.2501 2.81218C11.324 2.81218 11.3972 2.82674 11.4654 2.85502C11.5337 2.8833 11.5958 2.92476 11.648 2.97702C11.7003 3.02928 11.7417 3.09133 11.77 3.15961C11.7983 3.2279 11.8129 3.30108 11.8129 3.37499C11.8129 3.4489 11.7983 3.52209 11.77 3.59037C11.7417 3.65866 11.7003 3.7207 11.648 3.77296L6.42029 8.99999L11.648 14.227C11.7536 14.3326 11.8129 14.4757 11.8129 14.625C11.8129 14.7743 11.7536 14.9174 11.648 15.023C11.5425 15.1285 11.3993 15.1878 11.2501 15.1878C11.1008 15.1878 10.9576 15.1285 10.8521 15.023L5.22708 9.39796C5.17479 9.34572 5.1333 9.28368 5.10499 9.2154C5.07668 9.14711 5.06211 9.07391 5.06211 8.99999C5.06211 8.92607 5.07668 8.85287 5.10499 8.78459C5.1333 8.7163 5.17479 8.65426 5.22708 8.60202Z" fill="#1A6444"/>
  </svg>
);

const ChevronRightIcon = () => (
  <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M12.7729 9.39798L7.14792 15.023C7.09566 15.0752 7.03361 15.1167 6.96533 15.145C6.89705 15.1733 6.82386 15.1878 6.74995 15.1878C6.67604 15.1878 6.60284 15.1733 6.53456 15.145C6.46628 15.1167 6.40423 15.0752 6.35197 15.023C6.29971 14.9707 6.25826 14.9087 6.22997 14.8404C6.20169 14.7721 6.18712 14.6989 6.18712 14.625C6.18712 14.5511 6.20169 14.4779 6.22997 14.4096C6.25826 14.3413 6.29971 14.2793 6.35197 14.227L11.5797 9L6.35197 3.773C6.24642 3.66745 6.18712 3.52429 6.18712 3.375C6.18712 3.22571 6.24642 3.08255 6.35197 2.977C6.45752 2.87145 6.60068 2.81215 6.74995 2.81215C6.89922 2.81215 7.04238 2.87145 7.14792 2.977L12.7729 8.60204C12.8252 8.65428 12.8667 8.71632 12.895 8.7846C12.9233 8.85289 12.9379 8.92609 12.9379 9.00001C12.9379 9.07393 12.9233 9.14713 12.895 9.21541C12.8667 9.2837 12.8252 9.34574 12.7729 9.39798Z" fill="#1A6444"/>
  </svg>
);

const ChevronsLeftIcon = () => (
  <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M14.4604 14.227C14.5127 14.2793 14.5541 14.3413 14.5824 14.4096C14.6107 14.4779 14.6253 14.5511 14.6253 14.625C14.6253 14.6989 14.6107 14.7721 14.5824 14.8404C14.5541 14.9087 14.5127 14.9707 14.4604 15.023C14.4082 15.0752 14.3461 15.1167 14.2778 15.145C14.2095 15.1733 14.1364 15.1878 14.0625 15.1878C13.9885 15.1878 13.9154 15.1733 13.8471 15.145C13.7788 15.1167 13.7167 15.0752 13.6645 15.023L8.03949 9.39798C7.98719 9.34574 7.9457 9.2837 7.91739 9.21541C7.88908 9.14713 7.87451 9.07393 7.87451 9.00001C7.87451 8.92609 7.88908 8.85289 7.91739 8.7846C7.9457 8.71632 7.98719 8.65428 8.03949 8.60204L13.6645 2.97704C13.77 2.87149 13.9132 2.81219 14.0625 2.81219C14.2117 2.81219 14.3549 2.87149 14.4604 2.97704C14.566 3.08259 14.6253 3.22574 14.6253 3.37501C14.6253 3.52427 14.566 3.66743 14.4604 3.77298L9.23269 9.00001L14.4604 14.227ZM3.60769 9.00001L8.83542 3.77298C8.94097 3.66743 9.00027 3.52427 9.00027 3.37501C9.00027 3.22574 8.94097 3.08259 8.83542 2.97704C8.72988 2.87149 8.58672 2.81219 8.43745 2.81219C8.28819 2.81219 8.14503 2.87149 8.03949 2.97704L2.41449 8.60204C2.36219 8.65428 2.3207 8.71632 2.29239 8.7846C2.26408 8.85289 2.24951 8.92609 2.24951 9.00001C2.24951 9.07393 2.26408 9.14713 2.29239 9.21541C2.3207 9.2837 2.36219 9.34574 2.41449 9.39798L8.03949 15.023C8.09175 15.0752 8.15379 15.1167 8.22207 15.145C8.29036 15.1733 8.36354 15.1878 8.43745 15.1878C8.51136 15.1878 8.58455 15.1733 8.65283 15.145C8.72112 15.1167 8.78316 15.0752 8.83542 15.023C8.88769 14.9707 8.92914 14.9087 8.95743 14.8404C8.98571 14.7721 9.00027 14.6989 9.00027 14.625C9.00027 14.5511 8.98571 14.4779 8.95743 14.4096C8.92914 14.3413 8.88769 14.2793 8.83542 14.227L3.60769 9.00001Z" fill="#1A6444"/>
  </svg>
);

const ChevronsRightIcon = () => (
  <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M3.53958 3.77298C3.43403 3.66743 3.37473 3.52427 3.37473 3.37501C3.37473 3.22574 3.43403 3.08259 3.53958 2.97704C3.64513 2.87149 3.78829 2.81219 3.93755 2.81219C4.08682 2.81219 4.22997 2.87149 4.33552 2.97704L9.96052 8.60204C10.0128 8.65428 10.0543 8.71632 10.0826 8.7846C10.1109 8.85289 10.1255 8.92609 10.1255 9.00001C10.1255 9.07393 10.1109 9.14713 10.0826 9.21541C10.0543 9.2837 10.0128 9.34574 9.96052 9.39798L4.33552 15.023C4.28327 15.0752 4.22123 15.1167 4.15294 15.145C4.08466 15.1733 4.01146 15.1878 3.93755 15.1878C3.86363 15.1878 3.79044 15.1733 3.72215 15.145C3.65387 15.1167 3.59183 15.0752 3.53958 15.023C3.48732 14.9707 3.44583 14.9087 3.41752 14.8404C3.38922 14.7721 3.37464 14.6989 3.37464 14.625C3.37464 14.5511 3.38922 14.4779 3.41752 14.4096C3.44583 14.3413 3.48732 14.2793 3.53958 14.227L8.76731 9.00001L3.53958 3.77298ZM14.3923 9.00001L9.16458 3.77298C9.05903 3.66743 8.99973 3.52427 8.99973 3.37501C8.99973 3.22574 9.05903 3.08259 9.16458 2.97704C9.27013 2.87149 9.41329 2.81219 9.56255 2.81219C9.71182 2.81219 9.85497 2.87149 9.96052 2.97704L15.5855 8.60204C15.6378 8.65428 15.6793 8.71632 15.7076 8.7846C15.7359 8.85289 15.7505 8.92609 15.7505 9.00001C15.7505 9.07393 15.7359 9.14713 15.7076 9.21541C15.6793 9.2837 15.6378 9.34574 15.5855 9.39798L9.96052 15.023C9.90827 15.0752 9.84623 15.1167 9.77794 15.145C9.70966 15.1733 9.63646 15.1878 9.56255 15.1878C9.48863 15.1878 9.41544 15.1733 9.34715 15.145C9.27887 15.1167 9.21683 15.0752 9.16458 15.023C9.11232 14.9707 9.07083 14.9087 9.04252 14.8404C9.01422 14.7721 8.99964 14.6989 8.99964 14.625C8.99964 14.5511 9.01422 14.4779 9.04252 14.4096C9.07083 14.3413 9.11232 14.2793 9.16458 14.227L14.3923 9.00001Z" fill="#1A6444"/>
  </svg>
);

const ChevronSelectIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M11.3538 10.6463C11.4002 10.6927 11.4371 10.7479 11.4623 10.8086C11.4874 10.8693 11.5004 10.9343 11.5004 11C11.5004 11.0657 11.4874 11.1308 11.4623 11.1915C11.4371 11.2522 11.4002 11.3073 11.3538 11.3538L8.35375 14.3538C8.30732 14.4003 8.25217 14.4371 8.19147 14.4623C8.13077 14.4875 8.06571 14.5004 8 14.5004C7.9343 14.5004 7.86923 14.4875 7.80853 14.4623C7.74783 14.4371 7.69269 14.4003 7.64625 14.3538L4.64625 11.3538C4.55243 11.26 4.49973 11.1327 4.49973 11C4.49973 10.8673 4.55243 10.7401 4.64625 10.6463C4.74007 10.5525 4.86732 10.4997 5 10.4997C5.13269 10.4997 5.25993 10.5525 5.35375 10.6463L8 13.2932L10.6463 10.6463C10.6927 10.5998 10.7478 10.5629 10.8085 10.5377C10.8692 10.5126 10.9343 10.4996 11 10.4996C11.0657 10.4996 11.1308 10.5126 11.1915 10.5377C11.2522 10.5629 11.3073 10.5998 11.3538 10.6463ZM5.35375 5.35378L8 2.7069L10.6463 5.35378C10.7401 5.4476 10.8673 5.50031 11 5.50031C11.1327 5.50031 11.2599 5.4476 11.3538 5.35378C11.4476 5.25996 11.5003 5.13271 11.5003 5.00003C11.5003 4.86735 11.4476 4.7401 11.3538 4.64628L8.35375 1.64628C8.30732 1.59979 8.25217 1.56291 8.19147 1.53775C8.13077 1.51258 8.06571 1.49963 8 1.49963C7.9343 1.49963 7.86923 1.51258 7.80853 1.53775C7.74783 1.56291 7.69269 1.59979 7.64625 1.64628L4.64625 4.64628C4.55243 4.7401 4.49973 4.86735 4.49973 5.00003C4.49973 5.13271 4.55243 5.25996 4.64625 5.35378C4.74007 5.4476 4.86732 5.50031 5 5.50031C5.13269 5.50031 5.25993 5.4476 5.35375 5.35378Z" fill="#1A6444"/>
  </svg>
);

/**
 * TableFooter component for pagination controls
 * - Rows per page dropdown
 * - Page information display
 * - Navigation controls (first, previous, next, last)
 */
export default function TableFooter({
  currentPage,
  totalPages,
  rowsPerPage,
  onPageChange,
  onRowsPerPageChange
}: TableFooterProps) {
  const rowsPerPageOptions = [10, 20, 50, 100];

  const handleFirstPage = () => onPageChange(1);
  const handlePreviousPage = () => onPageChange(Math.max(1, currentPage - 1));
  const handleNextPage = () => onPageChange(Math.min(totalPages, currentPage + 1));
  const handleLastPage = () => onPageChange(totalPages);

  return (
    <div className="flex items-center justify-between px-6 pt-5 pb-4 bg-white">
      {/* Left side - Rows per page */}
      <div className="flex items-center gap-2">
        <div className="relative">
          <select
            value={rowsPerPage}
            onChange={(e) => onRowsPerPageChange(Number(e.target.value))}
            className="appearance-none bg-white border border-[#17533b14] rounded-[10px] px-3 py-2 pr-8 text-sm text-[#1A6444] text-end font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent flex items-center justify-center drop-shadow-[0px_1px_1px_#112a241f]"
          >
            {rowsPerPageOptions.map((option) => (
              <option key={option} value={option}>
                {option}
              </option>
            ))}
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
            <ChevronSelectIcon />
          </div>
        </div>
        <span className="text-sm text-[#546661] font-medium">Rows per page</span>
      </div>

      {/* Right side - Navigation controls */}
      <div className="flex items-center gap-3">
        <span className="text-sm text-[#546661] font-medium">
          Page {currentPage} of {totalPages}
        </span>
        
        <div className="flex items-center gap-2">
          <button
            onClick={handleFirstPage}
            disabled={currentPage === 1}
            className="p-2 rounded-[10px] hover:bg-gray-100 border border-[#17533b14] disabled:opacity-50 disabled:cursor-not-allowed text-[#546661] drop-shadow-[0px_1px_1px_#112a241f]"
            aria-label="Go to first page"
          >
            <ChevronsLeftIcon />
          </button>
          
          <button
            onClick={handlePreviousPage}
            disabled={currentPage === 1}
            className="p-2 rounded-[10px] hover:bg-gray-100 border border-[#17533b14] disabled:opacity-50 disabled:cursor-not-allowed text-[#546661] drop-shadow-[0px_1px_1px_#112a241f]"
            aria-label="Go to previous page"
          >
            <ChevronLeftIcon />
          </button>
          
          <button
            onClick={handleNextPage}
            disabled={currentPage === totalPages}
            className="p-2 rounded-[10px] hover:bg-gray-100 border border-[#17533b14] disabled:opacity-50 disabled:cursor-not-allowed text-[#546661] drop-shadow-[0px_1px_1px_#112a241f]"
            aria-label="Go to next page"
          >
            <ChevronRightIcon />
          </button>
          
          <button
            onClick={handleLastPage}
            disabled={currentPage === totalPages}
            className="p-2 rounded-[10px] hover:bg-gray-100 border border-[#17533b14] disabled:opacity-50 disabled:cursor-not-allowed text-[#546661] drop-shadow-[0px_1px_1px_#112a241f]"
            aria-label="Go to last page"
          >
            <ChevronsRightIcon />
          </button>
        </div>
      </div>
    </div>
  );
}
