# PolySans Font Setup

## Current Setup
Configured to use **PolySans OTF files** with the following expected file names:

- `PolySans-Regular.otf` (weight: 400)
- `PolySans-Medium.otf` (weight: 500) 
- `PolySans-SemiBold.otf` (weight: 600)
- `PolySans-Bold.otf` (weight: 700)

## To Use Your PolySans OTF Files

1. **Place your OTF files** in this directory (`/src/fonts/`) with the names above
2. If your files have different names, update the paths in `/src/app/layout.tsx`
3. The setup is ready to use your OTF files!

## Font File Format Notes

### Current: OTF Files
- ✅ **Pros**: High quality, full font features, works with Next.js
- ⚠️ **Cons**: Larger file sizes (slower loading)

### Recommended: Convert to WOFF2
For better web performance, consider converting to WOFF2:

**Online Converters:**
- [Convertio](https://convertio.co/otf-woff2/)
- [CloudConvert](https://cloudconvert.com/otf-to-woff2)
- [Font Squirrel Webfont Generator](https://www.fontsquirrel.com/tools/webfont-generator)

**Command Line (if you have fonttools):**
```bash
pip install fonttools[woff]
pyftsubset PolySans-Regular.otf --output-file=PolySans-Regular.woff2 --flavor=woff2
```

If you convert to WOFF2, update the file extensions in `/src/app/layout.tsx` from `.otf` to `.woff2`.

## Font Usage in Components

The font is available throughout your app via:
- CSS: `font-family: var(--font-poly-sans)`
- Tailwind: `font-sans` (default) or `font-[family-name:var(--font-poly-sans)]`

## Performance Notes

- Using `display: "swap"` for optimal loading performance
- OTF files work but WOFF2 provides ~30% better compression
- Font variables are configured in both CSS custom properties and Tailwind theme
