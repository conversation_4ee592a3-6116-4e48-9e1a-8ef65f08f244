/* Group 1 */

width: 1234px;
height: 812px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table row */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 20px 24px;
gap: 20px;

position: absolute;
width: 1234px;
height: 96px;
left: 0px;
top: 360px;

background: #FFFFFF;
border-bottom: 1px solid rgba(23, 83, 59, 0.04);
box-shadow: 0px 1px 1px rgba(23, 83, 59, 0.12);


/* patient */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 4px;

margin: 0 auto;
width: 72px;
height: 30px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* First Last */

width: 72px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* ID: 11060 */

width: 72px;
height: 12px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 12px;
/* identical to box height */

color: #B3B3B3;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Frame ********** */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 92px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* July 00, 2025 */

width: 92px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* insurance carrier */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 8px;

margin: 0 auto;
width: 121px;
height: 56px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* BCBS OF COLORADO FEP PPO INN */

width: 121px;
height: 24px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 12px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Insurance Type Badge */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 4px;
gap: 10px;

width: 121px;
height: 24px;

background: #EBF9FE;
border-radius: 4px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Text */

width: 121px;
height: 16px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
text-align: center;

color: #23A9EB;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1597881694 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 49px;
height: 12px;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* $00,000 */

width: 49px;
height: 12px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 12px;
/* identical to box height */

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1597881693 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 126px;
height: 12px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* NCOF - RESUBMITTED */

width: 126px;
height: 12px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 12px;
/* identical to box height */

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Last updated */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 8px;

margin: 0 auto;
width: 92px;
height: 33px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* May 28, 2030 */

width: 92px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* 11:36 AM */

width: 92px;
height: 11px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 11px;
line-height: 11px;
/* identical to box height */

color: #74827F;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* user */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 7px 3px;
gap: 10px;

margin: 0 auto;
width: 30px;
height: 28px;

background: #E0FEEF;
border-radius: 14px;

/* Inside auto layout */
flex: none;
order: 6;
flex-grow: 0;


/* AA */

width: 20px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 14px;

color: #196443;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1597881691 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 92px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 7;
flex-grow: 0;


/* Aug 28, 2025 */

width: 92px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame ********** */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 91px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 8;
flex-grow: 0;


/* Aug 28, 2025 */

width: 91px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* PMS sync status */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 4px;

margin: 0 auto;
width: 111px;
height: 44px;


/* Inside auto layout */
flex: none;
order: 9;
flex-grow: 0;


/* PMS Sync Status badge */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 2px 10px 2px 8px;
gap: 6px;

width: 111px;
height: 24px;

background: #EAEAEA;
border-radius: 4px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* WarningCircle

alert, danger, dangerous, caution, errors, round, security &amp; warnings
*/

width: 15px;
height: 15px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

box-sizing: border-box;

position: absolute;
left: 9.38%;
right: 9.37%;
top: 9.38%;
bottom: 9.37%;

background: #838580;
border: 4px solid #838580;


/* Text */

width: 64px;
height: 16px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
text-align: center;

color: #838580;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Status modified today */

width: 111px;
height: 16px;

font-family: 'Inter';
font-style: normal;
font-weight: 500;
font-size: 10px;
line-height: 16px;
/* identical to box height, or 160% */
text-align: center;

color: #546661;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* provider */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 4px;

margin: 0 auto;
width: 91px;
height: 34px;


/* Inside auto layout */
flex: none;
order: 10;
flex-grow: 0;


/* Dr. First Last */

width: 84px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Text */

width: 91px;
height: 16px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
text-align: center;

color: #B3B3B3;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table row */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 20px 24px;
gap: 20px;

position: absolute;
width: 1234px;
height: 96px;
left: 0px;
top: 456px;

background: #FFFFFF;
border-bottom: 1px solid rgba(23, 83, 59, 0.04);
box-shadow: 0px 1px 1px rgba(23, 83, 59, 0.12);


/* patient */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 4px;

margin: 0 auto;
width: 72px;
height: 30px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* First Last */

width: 72px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* ID: 11060 */

width: 72px;
height: 12px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 12px;
/* identical to box height */

color: #B3B3B3;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Frame ********** */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 92px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* July 00, 2025 */

width: 92px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* insurance carrier */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 8px;

margin: 0 auto;
width: 121px;
height: 56px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* BCBS OF COLORADO FEP PPO INN */

width: 121px;
height: 24px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 12px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Insurance Type Badge */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 4px;
gap: 10px;

width: 121px;
height: 24px;

background: #EBF9FE;
border-radius: 4px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Text */

width: 121px;
height: 16px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
text-align: center;

color: #23A9EB;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1597881694 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 49px;
height: 12px;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* $00,000 */

width: 49px;
height: 12px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 12px;
/* identical to box height */

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1597881693 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 126px;
height: 12px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* NCOF - RESUBMITTED */

width: 126px;
height: 12px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 12px;
/* identical to box height */

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Last updated */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 8px;

margin: 0 auto;
width: 92px;
height: 33px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* May 28, 2030 */

width: 92px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* 11:36 AM */

width: 92px;
height: 11px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 11px;
line-height: 11px;
/* identical to box height */

color: #74827F;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* user */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 7px 3px;
gap: 10px;

margin: 0 auto;
width: 30px;
height: 28px;

background: #E0FEEF;
border-radius: 14px;

/* Inside auto layout */
flex: none;
order: 6;
flex-grow: 0;


/* AA */

width: 20px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 14px;

color: #196443;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1597881691 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 92px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 7;
flex-grow: 0;


/* Aug 28, 2025 */

width: 92px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame ********** */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 91px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 8;
flex-grow: 0;


/* Aug 28, 2025 */

width: 91px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* PMS sync status */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 4px;

margin: 0 auto;
width: 111px;
height: 44px;


/* Inside auto layout */
flex: none;
order: 9;
flex-grow: 0;


/* PMS Sync Status badge */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 2px 10px 2px 8px;
gap: 6px;

width: 111px;
height: 24px;

background: #EAEAEA;
border-radius: 4px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* WarningCircle

alert, danger, dangerous, caution, errors, round, security &amp; warnings
*/

width: 15px;
height: 15px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

box-sizing: border-box;

position: absolute;
left: 9.38%;
right: 9.37%;
top: 9.38%;
bottom: 9.37%;

background: #838580;
border: 4px solid #838580;


/* Text */

width: 64px;
height: 16px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
text-align: center;

color: #838580;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Status modified today */

width: 111px;
height: 16px;

font-family: 'Inter';
font-style: normal;
font-weight: 500;
font-size: 10px;
line-height: 16px;
/* identical to box height, or 160% */
text-align: center;

color: #546661;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* provider */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 4px;

margin: 0 auto;
width: 91px;
height: 34px;


/* Inside auto layout */
flex: none;
order: 10;
flex-grow: 0;


/* Dr. First Last */

width: 84px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Text */

width: 91px;
height: 16px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
text-align: center;

color: #B3B3B3;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table row */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 20px 24px;
gap: 20px;

position: absolute;
width: 1234px;
height: 96px;
left: 0px;
top: 552px;

background: #FFFFFF;
border-bottom: 1px solid rgba(23, 83, 59, 0.04);
box-shadow: 0px 1px 1px rgba(23, 83, 59, 0.12);


/* patient */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 4px;

margin: 0 auto;
width: 72px;
height: 30px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* First Last */

width: 72px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* ID: 11060 */

width: 72px;
height: 12px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 12px;
/* identical to box height */

color: #B3B3B3;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Frame ********** */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 92px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* July 00, 2025 */

width: 92px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* insurance carrier */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 8px;

margin: 0 auto;
width: 121px;
height: 56px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* BCBS OF COLORADO FEP PPO INN */

width: 121px;
height: 24px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 12px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Insurance Type Badge */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 4px;
gap: 10px;

width: 121px;
height: 24px;

background: #EBF9FE;
border-radius: 4px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Text */

width: 121px;
height: 16px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
text-align: center;

color: #23A9EB;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1597881694 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 49px;
height: 12px;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* $00,000 */

width: 49px;
height: 12px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 12px;
/* identical to box height */

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1597881693 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 126px;
height: 12px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* NCOF - RESUBMITTED */

width: 126px;
height: 12px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 12px;
/* identical to box height */

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Last updated */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 8px;

margin: 0 auto;
width: 92px;
height: 33px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* May 28, 2030 */

width: 92px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* 11:36 AM */

width: 92px;
height: 11px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 11px;
line-height: 11px;
/* identical to box height */

color: #74827F;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* user */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 7px 3px;
gap: 10px;

margin: 0 auto;
width: 30px;
height: 28px;

background: #E0FEEF;
border-radius: 14px;

/* Inside auto layout */
flex: none;
order: 6;
flex-grow: 0;


/* AA */

width: 20px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 14px;

color: #196443;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1597881691 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 92px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 7;
flex-grow: 0;


/* Aug 28, 2025 */

width: 92px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame ********** */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 91px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 8;
flex-grow: 0;


/* Aug 28, 2025 */

width: 91px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* PMS sync status */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 4px;

margin: 0 auto;
width: 111px;
height: 44px;


/* Inside auto layout */
flex: none;
order: 9;
flex-grow: 0;


/* PMS Sync Status badge */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 2px 10px 2px 8px;
gap: 6px;

width: 111px;
height: 24px;

background: #EAEAEA;
border-radius: 4px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* WarningCircle

alert, danger, dangerous, caution, errors, round, security &amp; warnings
*/

width: 15px;
height: 15px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

box-sizing: border-box;

position: absolute;
left: 9.38%;
right: 9.37%;
top: 9.38%;
bottom: 9.37%;

background: #838580;
border: 4px solid #838580;


/* Text */

width: 64px;
height: 16px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
text-align: center;

color: #838580;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Status modified today */

width: 111px;
height: 16px;

font-family: 'Inter';
font-style: normal;
font-weight: 500;
font-size: 10px;
line-height: 16px;
/* identical to box height, or 160% */
text-align: center;

color: #546661;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* provider */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 4px;

margin: 0 auto;
width: 91px;
height: 34px;


/* Inside auto layout */
flex: none;
order: 10;
flex-grow: 0;


/* Dr. First Last */

width: 84px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Text */

width: 91px;
height: 16px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
text-align: center;

color: #B3B3B3;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table row */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 20px 24px;
gap: 20px;

position: absolute;
width: 1234px;
height: 96px;
left: 0px;
top: 648px;

background: #FFFFFF;
border-bottom: 1px solid rgba(23, 83, 59, 0.04);
box-shadow: 0px 1px 1px rgba(23, 83, 59, 0.12);


/* patient */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 4px;

margin: 0 auto;
width: 72px;
height: 30px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* First Last */

width: 72px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* ID: 11060 */

width: 72px;
height: 12px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 12px;
/* identical to box height */

color: #B3B3B3;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Frame ********** */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 92px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* July 00, 2025 */

width: 92px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* insurance carrier */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 8px;

margin: 0 auto;
width: 121px;
height: 56px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* BCBS OF COLORADO FEP PPO INN */

width: 121px;
height: 24px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 12px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Insurance Type Badge */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 4px;
gap: 10px;

width: 121px;
height: 24px;

background: #EBF9FE;
border-radius: 4px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Text */

width: 121px;
height: 16px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
text-align: center;

color: #23A9EB;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1597881694 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 49px;
height: 12px;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* $00,000 */

width: 49px;
height: 12px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 12px;
/* identical to box height */

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1597881693 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 126px;
height: 12px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* NCOF - RESUBMITTED */

width: 126px;
height: 12px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 12px;
/* identical to box height */

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Last updated */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 8px;

margin: 0 auto;
width: 92px;
height: 33px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* May 28, 2030 */

width: 92px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* 11:36 AM */

width: 92px;
height: 11px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 11px;
line-height: 11px;
/* identical to box height */

color: #74827F;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* user */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 7px 3px;
gap: 10px;

margin: 0 auto;
width: 30px;
height: 28px;

background: #E0FEEF;
border-radius: 14px;

/* Inside auto layout */
flex: none;
order: 6;
flex-grow: 0;


/* AA */

width: 20px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 14px;

color: #196443;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1597881691 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 92px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 7;
flex-grow: 0;


/* Aug 28, 2025 */

width: 92px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame ********** */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 91px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 8;
flex-grow: 0;


/* Aug 28, 2025 */

width: 91px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* PMS sync status */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 4px;

margin: 0 auto;
width: 111px;
height: 44px;


/* Inside auto layout */
flex: none;
order: 9;
flex-grow: 0;


/* PMS Sync Status badge */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 2px 10px 2px 8px;
gap: 6px;

width: 111px;
height: 24px;

background: #EAEAEA;
border-radius: 4px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* WarningCircle

alert, danger, dangerous, caution, errors, round, security &amp; warnings
*/

width: 15px;
height: 15px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

box-sizing: border-box;

position: absolute;
left: 9.38%;
right: 9.37%;
top: 9.38%;
bottom: 9.37%;

background: #838580;
border: 4px solid #838580;


/* Text */

width: 64px;
height: 16px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
text-align: center;

color: #838580;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Status modified today */

width: 111px;
height: 16px;

font-family: 'Inter';
font-style: normal;
font-weight: 500;
font-size: 10px;
line-height: 16px;
/* identical to box height, or 160% */
text-align: center;

color: #546661;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* provider */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 4px;

margin: 0 auto;
width: 91px;
height: 34px;


/* Inside auto layout */
flex: none;
order: 10;
flex-grow: 0;


/* Dr. First Last */

width: 84px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Text */

width: 91px;
height: 16px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
text-align: center;

color: #B3B3B3;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table footer */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: flex-start;
padding: 20px 24px;

position: absolute;
width: 1234px;
height: 68px;
left: 0px;
top: 744px;

background: #FFFFFF;
border-radius: 0px 0px 20px 20px;


/* rows per page */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 9px;

margin: 0 auto;
width: 167px;
height: 34px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1000003133 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 8px 12px;
gap: 10px;

width: 61px;
height: 34px;

border: 1px solid rgba(23, 83, 59, 0.08);
filter: drop-shadow(0px 1px 1px rgba(17, 42, 36, 0.12));
border-radius: 10px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1000003132 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-end;
padding: 0px;
gap: 3px;

width: 37px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* 20 */

width: 18px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #1A6444;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* CaretUpDown

chevron, directional, pointer, pointing, arrowhead, triangle, arrows
*/

width: 16px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Vector */

position: absolute;
left: 28.12%;
right: 28.12%;
top: 9.37%;
bottom: 9.37%;

background: #1A6444;


/* Rows per page */

width: 97px;
height: 17px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 17px;
/* identical to box height */

color: #546661;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* page navigation */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 13px;

margin: 0 auto;
width: 246px;
height: 34px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Page 1 of 8 */

width: 72px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #546661;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1000003139 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 161px;
height: 34px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;


/* Frame 1000003135 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 8px;
gap: 10px;

width: 34px;
height: 34px;

border: 1px solid rgba(23, 83, 59, 0.08);
filter: drop-shadow(0px 1px 1px rgba(17, 42, 36, 0.12));
border-radius: 10px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* CaretDoubleLeft

chevron, directional, pointer, pointing, arrowhead, triangle, arrows
*/

width: 18px;
height: 18px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 12.5%;
right: 18.75%;
top: 15.62%;
bottom: 15.62%;

background: #1A6444;


/* Frame 1000003136 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 6px 8px;
gap: 10px;

width: 34px;
height: 34px;

border: 1px solid rgba(23, 83, 59, 0.08);
filter: drop-shadow(0px 1px 1px rgba(17, 42, 36, 0.12));
border-radius: 10px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* CaretRight

chevron, directional, pointer, pointing, arrowhead, triangle, arrows
*/

width: 18px;
height: 18px;

transform: rotate(-180deg);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 28.12%;
right: 34.37%;
top: 15.62%;
bottom: 15.62%;

background: #1A6444;
transform: rotate(-180deg);


/* Frame 1000003137 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 6px 8px;
gap: 10px;

width: 34px;
height: 34px;

border: 1px solid rgba(23, 83, 59, 0.08);
filter: drop-shadow(0px 1px 1px rgba(17, 42, 36, 0.12));
border-radius: 10px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* CaretRight

chevron, directional, pointer, pointing, arrowhead, triangle, arrows
*/

width: 18px;
height: 18px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 34.37%;
right: 28.12%;
top: 15.62%;
bottom: 15.62%;

background: #1A6444;


/* Frame 1000003138 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 8px;
gap: 10px;

width: 34px;
height: 34px;

border: 1px solid rgba(23, 83, 59, 0.08);
filter: drop-shadow(0px 1px 1px rgba(17, 42, 36, 0.12));
border-radius: 10px;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* CaretDoubleLeft

chevron, directional, pointer, pointing, arrowhead, triangle, arrows
*/

width: 18px;
height: 18px;

transform: rotate(-180deg);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 18.75%;
right: 12.5%;
top: 15.62%;
bottom: 15.62%;

background: #1A6444;
transform: rotate(-180deg);


/* table row */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 20px 24px;
gap: 20px;

position: absolute;
width: 1234px;
height: 96px;
left: 0px;
top: 264px;

background: #FFFFFF;
border-bottom: 1px solid rgba(23, 83, 59, 0.04);
box-shadow: 0px 1px 1px rgba(23, 83, 59, 0.12);


/* patient */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 4px;

margin: 0 auto;
width: 72px;
height: 30px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* First Last */

width: 72px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* ID: 11060 */

width: 72px;
height: 12px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 12px;
/* identical to box height */

color: #B3B3B3;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Frame ********** */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 92px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* July 00, 2025 */

width: 92px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* insurance carrier */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 8px;

margin: 0 auto;
width: 121px;
height: 56px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* BCBS OF COLORADO FEP PPO INN */

width: 121px;
height: 24px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 12px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Insurance Type Badge */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 4px;
gap: 10px;

width: 121px;
height: 24px;

background: #EBF9FE;
border-radius: 4px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Text */

width: 121px;
height: 16px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
text-align: center;

color: #23A9EB;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1597881694 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 49px;
height: 12px;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* $00,000 */

width: 49px;
height: 12px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 12px;
/* identical to box height */

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1597881693 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 126px;
height: 12px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* NCOF - RESUBMITTED */

width: 126px;
height: 12px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 12px;
/* identical to box height */

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Last updated */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 8px;

margin: 0 auto;
width: 92px;
height: 33px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* May 28, 2030 */

width: 92px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* 11:36 AM */

width: 92px;
height: 11px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 11px;
line-height: 11px;
/* identical to box height */

color: #74827F;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* user */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 7px 3px;
gap: 10px;

margin: 0 auto;
width: 30px;
height: 28px;

background: #E0FEEF;
border-radius: 14px;

/* Inside auto layout */
flex: none;
order: 6;
flex-grow: 0;


/* AA */

width: 20px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 14px;

color: #196443;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1597881691 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 92px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 7;
flex-grow: 0;


/* Aug 28, 2025 */

width: 92px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame ********** */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 91px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 8;
flex-grow: 0;


/* Aug 28, 2025 */

width: 91px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* PMS sync status */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 4px;

margin: 0 auto;
width: 111px;
height: 44px;


/* Inside auto layout */
flex: none;
order: 9;
flex-grow: 0;


/* PMS Sync Status badge */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 2px 10px 2px 8px;
gap: 6px;

width: 111px;
height: 24px;

background: #EAEAEA;
border-radius: 4px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* WarningCircle

alert, danger, dangerous, caution, errors, round, security &amp; warnings
*/

width: 15px;
height: 15px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

box-sizing: border-box;

position: absolute;
left: 9.38%;
right: 9.37%;
top: 9.38%;
bottom: 9.37%;

background: #838580;
border: 4px solid #838580;


/* Text */

width: 64px;
height: 16px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
text-align: center;

color: #838580;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Status modified today */

width: 111px;
height: 16px;

font-family: 'Inter';
font-style: normal;
font-weight: 500;
font-size: 10px;
line-height: 16px;
/* identical to box height, or 160% */
text-align: center;

color: #546661;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* provider */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 4px;

margin: 0 auto;
width: 91px;
height: 34px;


/* Inside auto layout */
flex: none;
order: 10;
flex-grow: 0;


/* Dr. First Last */

width: 84px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Text */

width: 91px;
height: 16px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
text-align: center;

color: #B3B3B3;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table headers */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 20px 24px;
gap: 20px;

position: absolute;
width: 1234px;
height: 72px;
left: 0px;
top: 0px;

background: #FFFFFF;
box-shadow: 0px 1px 1px rgba(17, 42, 36, 0.12);
border-radius: 20px 20px 0px 0px;


/* Frame ********** */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 72px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Patient */

width: 72px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #546661;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame ********** */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 92px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Service Date */

width: 92px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #546661;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame ********** */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 121px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Insurance Carrier */

width: 121px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #546661;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1597881696 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 49px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* Amount */

width: 51px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #546661;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1597881697 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 126px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* Status */

width: 120px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #546661;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1597881698 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 92px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* Last Updated */

width: 88px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #546661;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1597881699 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 30px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 6;
flex-grow: 0;


/* User */

width: 30px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #546661;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1597881700 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 92px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 7;
flex-grow: 0;


/* Date Sent */

width: 80px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #546661;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1597881705 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 91px;
height: 28px;


/* Inside auto layout */
flex: none;
order: 8;
flex-grow: 0;


/* Date Sent Orig */

width: 91px;
height: 28px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #546661;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1597881701 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 111px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 9;
flex-grow: 0;


/* PMS Sync Status */

width: 111px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #546661;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame ********** */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 91px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 10;
flex-grow: 0;


/* Provider */

width: 92px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #546661;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table row */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 20px 24px;
gap: 20px;

position: absolute;
width: 1234px;
height: 96px;
left: 0px;
top: 72px;

background: #FFFFFF;
border-bottom: 1px solid rgba(23, 83, 59, 0.04);
box-shadow: 0px 1px 1px rgba(23, 83, 59, 0.12);


/* patient */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 4px;

margin: 0 auto;
width: 72px;
height: 30px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* First Last */

width: 72px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* ID: 11060 */

width: 72px;
height: 12px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 12px;
/* identical to box height */

color: #B3B3B3;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Frame ********** */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 92px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* July 00, 2025 */

width: 92px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* insurance carrier */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 8px;

margin: 0 auto;
width: 121px;
height: 56px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* BCBS OF COLORADO FEP PPO INN */

width: 121px;
height: 24px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 12px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Insurance Type Badge */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 4px;
gap: 10px;

width: 121px;
height: 24px;

background: #EBF9FE;
border-radius: 4px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Text */

width: 121px;
height: 16px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
text-align: center;

color: #23A9EB;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1597881694 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 49px;
height: 12px;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* $00,000 */

width: 49px;
height: 12px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 12px;
/* identical to box height */

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1597881693 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 126px;
height: 12px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* NCOF - RESUBMITTED */

width: 126px;
height: 12px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 12px;
/* identical to box height */

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Last updated */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 8px;

margin: 0 auto;
width: 92px;
height: 33px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* May 28, 2030 */

width: 92px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* 11:36 AM */

width: 92px;
height: 11px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 11px;
line-height: 11px;
/* identical to box height */

color: #74827F;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* user */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 7px 3px;
gap: 10px;

margin: 0 auto;
width: 30px;
height: 28px;

background: #E0FEEF;
border-radius: 14px;

/* Inside auto layout */
flex: none;
order: 6;
flex-grow: 0;


/* AA */

width: 20px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 14px;

color: #196443;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1597881691 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 92px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 7;
flex-grow: 0;


/* Aug 28, 2025 */

width: 92px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame ********** */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 91px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 8;
flex-grow: 0;


/* Aug 28, 2025 */

width: 91px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* PMS sync status */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 4px;

margin: 0 auto;
width: 111px;
height: 44px;


/* Inside auto layout */
flex: none;
order: 9;
flex-grow: 0;


/* PMS Sync Status badge */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 2px 10px 2px 8px;
gap: 6px;

width: 111px;
height: 24px;

background: #EAEAEA;
border-radius: 4px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* WarningCircle

alert, danger, dangerous, caution, errors, round, security &amp; warnings
*/

width: 15px;
height: 15px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

box-sizing: border-box;

position: absolute;
left: 9.38%;
right: 9.37%;
top: 9.38%;
bottom: 9.37%;

background: #838580;
border: 4px solid #838580;


/* Text */

width: 64px;
height: 16px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
text-align: center;

color: #838580;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Status modified today */

width: 111px;
height: 16px;

font-family: 'Inter';
font-style: normal;
font-weight: 500;
font-size: 10px;
line-height: 16px;
/* identical to box height, or 160% */
text-align: center;

color: #546661;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* provider */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 4px;

margin: 0 auto;
width: 91px;
height: 34px;


/* Inside auto layout */
flex: none;
order: 10;
flex-grow: 0;


/* Dr. First Last */

width: 84px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Text */

width: 91px;
height: 16px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
text-align: center;

color: #B3B3B3;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table row */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 20px 24px;
gap: 20px;

position: absolute;
width: 1234px;
height: 96px;
left: 0px;
top: 168px;

background: #FFFFFF;
border-bottom: 1px solid rgba(23, 83, 59, 0.04);
box-shadow: 0px 1px 1px rgba(23, 83, 59, 0.12);


/* patient */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 4px;

margin: 0 auto;
width: 72px;
height: 30px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* First Last */

width: 72px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* ID: 11060 */

width: 72px;
height: 12px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 12px;
/* identical to box height */

color: #B3B3B3;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Frame ********** */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 92px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* July 00, 2025 */

width: 92px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* insurance carrier */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 8px;

margin: 0 auto;
width: 121px;
height: 56px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* BCBS OF COLORADO FEP PPO INN */

width: 121px;
height: 24px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 12px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Insurance Type Badge */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 4px;
gap: 10px;

width: 121px;
height: 24px;

background: #EBF9FE;
border-radius: 4px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Text */

width: 121px;
height: 16px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
text-align: center;

color: #23A9EB;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1597881694 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 49px;
height: 12px;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* $00,000 */

width: 49px;
height: 12px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 12px;
/* identical to box height */

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1597881693 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 126px;
height: 12px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* NCOF - RESUBMITTED */

width: 126px;
height: 12px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 12px;
/* identical to box height */

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Last updated */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 8px;

margin: 0 auto;
width: 92px;
height: 33px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* May 28, 2030 */

width: 92px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* 11:36 AM */

width: 92px;
height: 11px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 11px;
line-height: 11px;
/* identical to box height */

color: #74827F;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* user */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 7px 3px;
gap: 10px;

margin: 0 auto;
width: 30px;
height: 28px;

background: #E0FEEF;
border-radius: 14px;

/* Inside auto layout */
flex: none;
order: 6;
flex-grow: 0;


/* AA */

width: 20px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 14px;

color: #196443;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1597881691 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 92px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 7;
flex-grow: 0;


/* Aug 28, 2025 */

width: 92px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame ********** */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 91px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 8;
flex-grow: 0;


/* Aug 28, 2025 */

width: 91px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* PMS sync status */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 4px;

margin: 0 auto;
width: 111px;
height: 44px;


/* Inside auto layout */
flex: none;
order: 9;
flex-grow: 0;


/* PMS Sync Status badge */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 2px 10px 2px 8px;
gap: 6px;

width: 111px;
height: 24px;

background: #EAEAEA;
border-radius: 4px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* WarningCircle

alert, danger, dangerous, caution, errors, round, security &amp; warnings
*/

width: 15px;
height: 15px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

box-sizing: border-box;

position: absolute;
left: 9.38%;
right: 9.37%;
top: 9.38%;
bottom: 9.37%;

background: #838580;
border: 4px solid #838580;


/* Text */

width: 64px;
height: 16px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
text-align: center;

color: #838580;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Status modified today */

width: 111px;
height: 16px;

font-family: 'Inter';
font-style: normal;
font-weight: 500;
font-size: 10px;
line-height: 16px;
/* identical to box height, or 160% */
text-align: center;

color: #546661;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* provider */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 4px;

margin: 0 auto;
width: 91px;
height: 34px;


/* Inside auto layout */
flex: none;
order: 10;
flex-grow: 0;


/* Dr. First Last */

width: 84px;
height: 14px;

font-family: 'PolySans';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 14px;

color: #112A24;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Text */

width: 91px;
height: 16px;

font-family: 'PolySans';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
text-align: center;

color: #B3B3B3;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
