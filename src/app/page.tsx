import DataTable, { TableColumn } from "../components/DataTable";

export default function Home() {
  // Define columns with exact widths from your design
  const columns: TableColumn[] = [
    { key: 'patient', label: 'Patient', width: '72px' },
    { key: 'serviceDate', label: 'Service Date', width: '92px' },
    { key: 'insuranceCarrier', label: 'Insurance Carrier', width: '121px' },
    { key: 'amount', label: 'Amount', width: '49px' },
    { key: 'status', label: 'Status', width: '126px' },
    { key: 'lastUpdated', label: 'Last Updated', width: '92px' },
    { key: 'user', label: 'User', width: '30px' },
    { key: 'dateSent', label: 'Date Sent', width: '92px' },
    { key: 'dateSentOrig', label: 'Date Sent Orig', width: '91px' },
    { key: 'pmsSyncStatus', label: 'PMS Sync Status', width: '111px' },
    { key: 'provider', label: 'Provider', width: '91px' },
  ];

  // Sample data - matching the attached image format
  const data = [
    {
      patient: 'First Last\nID: 11060',
      serviceDate: 'July 00, 2025',
      insuranceCarrier: 'BCBS OF COLORADO\nFEP PPO INN\nPrimary',
      amount: '$00,000',
      status: 'NCOF - RESUBMITTED',
      lastUpdated: 'May 28, 2030\n11:36 AM',
      user: 'AA',
      dateSent: 'Aug 28, 2025',
      dateSentOrig: 'Aug 28, 2025',
      pmsSyncStatus: '⭕ Not synced\nStatus modified today',
      provider: 'Dr. First Last\nID:56712349911'
    }
  ];

  return (
    <div className="min-h-screen bg-[#1E1E1E] flex items-center justify-center p-4 sm:p-6 lg:p-8">
      <DataTable columns={columns} data={data} />
    </div>
  );
}
