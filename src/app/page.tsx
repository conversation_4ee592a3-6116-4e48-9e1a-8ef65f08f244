import DataTable, { TableColumn } from "../components/DataTable";
import { getAllClaims } from "../data/insurance-data";

export default function Home() {
  // Define columns with exact widths from your design
  const columns: TableColumn[] = [
    { key: 'patient', label: 'Patient', width: '100px' },
    { key: 'serviceDate', label: 'Service Date', width: '92px' },
    { key: 'insuranceCarrier', label: 'Insurance Carrier', width: '141px' },
    { key: 'amount', label: 'Amount', width: '49px' },
    { key: 'status', label: 'Status', width: '126px' },
    { key: 'lastUpdated', label: 'Last Updated', width: '92px' },
    { key: 'user', label: 'User', width: '30px' },
    { key: 'dateSent', label: 'Date Sent', width: '92px' },
    { key: 'dateSentOrig', label: 'Date Sent Orig', width: '91px' },
    { key: 'pmsSyncStatus', label: 'PMS Sync Status', width: '121px' },
    { key: 'provider', label: 'Provider', width: '95px' },
  ];

  // Get all generated claims data (300 records generated at build time)
  const data = getAllClaims();

  return (
    <div className="min-h-screen bg-[#1E1E1E] flex items-center justify-center p-4 sm:p-6 lg:p-8">
      <DataTable columns={columns} data={data} />
    </div>
  );
}
