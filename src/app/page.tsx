import DataTable, { TableColumn } from "../components/DataTable";
import { ClaimRowData } from "../components/ClaimRow";

export default function Home() {
  // Define columns with exact widths from your design
  const columns: TableColumn[] = [
    { key: 'patient', label: 'Patient', width: '72px' },
    { key: 'serviceDate', label: 'Service Date', width: '92px' },
    { key: 'insuranceCarrier', label: 'Insurance Carrier', width: '141px' },
    { key: 'amount', label: 'Amount', width: '49px' },
    { key: 'status', label: 'Status', width: '126px' },
    { key: 'lastUpdated', label: 'Last Updated', width: '92px' },
    { key: 'user', label: 'User', width: '30px' },
    { key: 'dateSent', label: 'Date Sent', width: '92px' },
    { key: 'dateSentOrig', label: 'Date Sent Orig', width: '91px' },
    { key: 'pmsSyncStatus', label: 'PMS Sync Status', width: '121px' },
    { key: 'provider', label: 'Provider', width: '91px' },
  ];

  // Sample data - matching the attached image format
  const data: ClaimRowData[] = [
    {
      patient: 'First Last\nID: 11060',
      serviceDate: 'July 00, 2025',
      insuranceCarrier: 'BCBS OF COLORADO\nFEP PPO INN\nPrimary',
      amount: '$00,000',
      status: 'NCOF - RESUBMITTED',
      lastUpdated: 'May 28, 2030\n11:36 AM',
      user: 'AA',
      dateSent: 'Aug 28, 2025',
      dateSentOrig: 'Aug 28, 2025',
      pmsSyncStatus: '⭕ Not synced\nStatus modified today',
      provider: 'Dr. First Last\nID:56712349911'
    },
    {
      patient: 'Jane Doe\nID: 22051',
      serviceDate: 'Aug 15, 2025',
      insuranceCarrier: 'AETNA\nPPO NETWORK\nPrimary',
      amount: '$150.00',
      status: 'PAID',
      lastUpdated: 'Sep 01, 2025\n02:15 PM',
      user: 'JD',
      dateSent: 'Aug 20, 2025',
      dateSentOrig: 'Aug 20, 2025',
      pmsSyncStatus: '✅ Synced\nLast sync: 2 hours ago',
      provider: 'Dr. Jane Smith\nID:98765432101'
    }
  ];

  return (
    <div className="min-h-screen bg-[#1E1E1E] flex items-center justify-center p-4 sm:p-6 lg:p-8">
      <DataTable columns={columns} data={data} />
    </div>
  );
}
