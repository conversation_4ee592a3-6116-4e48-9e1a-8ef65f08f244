@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Insurance Table Styles */
.insurance-table-container {
  width: 100%;
  max-width: 1234px;
  margin: 0 auto;
  background: #ffffff;
  font-family: 'PolySans', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  overflow-x: auto;
}

/* Table Header */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: #f8f9fa;
  border-bottom: 1px solid rgba(23, 83, 59, 0.12);
  font-weight: 600;
  font-size: 12px;
  color: #74827F;
  text-transform: uppercase;
  min-width: 1200px;
}

.header-cell {
  flex: 1;
  text-align: left;
  min-width: 90px;
}

/* Table Row */
.table-row {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  gap: 20px;
  width: 100%;
  min-width: 1200px;
  height: 96px;
  background: #FFFFFF;
  border-bottom: 1px solid rgba(23, 83, 59, 0.04);
  box-shadow: 0px 1px 1px rgba(23, 83, 59, 0.12);
  transition: background-color 0.2s ease;
}

.table-row:hover {
  background: #f9fafb;
}

/* Patient Column */
.patient {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0px;
  gap: 4px;
  width: 72px;
  height: 30px;
  flex: none;
  order: 0;
  flex-grow: 0;
}

.patient-name {
  width: 72px;
  height: 14px;
  font-family: 'PolySans';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  color: #112A24;
  flex: none;
  order: 0;
  align-self: stretch;
  flex-grow: 0;
}

.patient-id {
  width: 72px;
  height: 12px;
  font-family: 'PolySans';
  font-style: normal;
  font-weight: 600;
  font-size: 12px;
  line-height: 12px;
  color: #B3B3B3;
  flex: none;
  order: 1;
  align-self: stretch;
  flex-grow: 0;
}

/* Service Date Column */
.service-date {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0px;
  gap: 10px;
  width: 92px;
  height: 14px;
  flex: none;
  order: 1;
  flex-grow: 0;
}

.date-text {
  width: 92px;
  height: 14px;
  font-family: 'PolySans';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  color: #112A24;
  flex: none;
  order: 0;
  flex-grow: 0;
}

/* Insurance Carrier Column */
.insurance-carrier {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0px;
  gap: 8px;
  width: 121px;
  height: 56px;
  flex: none;
  order: 2;
  flex-grow: 0;
}

.carrier-name {
  width: 121px;
  height: 24px;
  font-family: 'PolySans';
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 12px;
  color: #112A24;
  flex: none;
  order: 0;
  flex-grow: 0;
}

.insurance-type-badge {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 4px;
  gap: 10px;
  width: 121px;
  height: 24px;
  background: #EBF9FE;
  border-radius: 4px;
  flex: none;
  order: 1;
  flex-grow: 0;
}

.badge-text {
  width: 121px;
  height: 16px;
  font-family: 'PolySans';
  font-style: normal;
  font-weight: 600;
  font-size: 12px;
  line-height: 16px;
  text-align: center;
  color: #23A9EB;
  flex: none;
  order: 0;
  flex-grow: 0;
}

/* Amount Column */
.amount {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0px;
  gap: 10px;
  width: 49px;
  height: 12px;
  flex: none;
  order: 3;
  flex-grow: 0;
}

.amount-text {
  width: 49px;
  height: 12px;
  font-family: 'PolySans';
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 12px;
  color: #112A24;
  flex: none;
  order: 0;
  flex-grow: 0;
}

/* Status Column */
.status {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0px;
  gap: 10px;
  width: 126px;
  height: 12px;
  flex: none;
  order: 4;
  flex-grow: 0;
}

.status-text {
  width: 126px;
  height: 12px;
  font-family: 'PolySans';
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 12px;
  color: #112A24;
  flex: none;
  order: 0;
  flex-grow: 0;
}

/* Last Updated Column */
.last-updated {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0px;
  gap: 8px;
  width: 92px;
  height: 33px;
  flex: none;
  order: 5;
  flex-grow: 0;
}

.updated-date {
  width: 92px;
  height: 14px;
  font-family: 'PolySans';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  color: #112A24;
  flex: none;
  order: 0;
  flex-grow: 0;
}

.updated-time {
  width: 92px;
  height: 11px;
  font-family: 'PolySans';
  font-style: normal;
  font-weight: 600;
  font-size: 11px;
  line-height: 11px;
  color: #74827F;
  flex: none;
  order: 1;
  align-self: stretch;
  flex-grow: 0;
}

/* User Column */
.user {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 7px 3px;
  gap: 10px;
  width: 30px;
  height: 28px;
  background: #E0FEEF;
  border-radius: 14px;
  flex: none;
  order: 6;
  flex-grow: 0;
}

.user-avatar {
  width: 20px;
  height: 14px;
  font-family: 'PolySans';
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 14px;
  color: #196443;
  flex: none;
  order: 0;
  flex-grow: 0;
}

/* Date Sent Column */
.date-sent {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0px;
  gap: 10px;
  width: 92px;
  height: 14px;
  flex: none;
  order: 7;
  flex-grow: 0;
}

.sent-date {
  width: 92px;
  height: 14px;
  font-family: 'PolySans';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  color: #112A24;
  flex: none;
  order: 0;
  flex-grow: 0;
}

/* Date Sent Orig Column */
.date-sent-orig {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0px;
  gap: 10px;
  width: 91px;
  height: 14px;
  flex: none;
  order: 8;
  flex-grow: 0;
}

.sent-orig-date {
  width: 91px;
  height: 14px;
  font-family: 'PolySans';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  color: #112A24;
  flex: none;
  order: 0;
  flex-grow: 0;
}

/* PMS Sync Status Column */
.pms-sync-status {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0px;
  gap: 4px;
  width: 111px;
  height: 44px;
  flex: none;
  order: 9;
  flex-grow: 0;
}

.pms-sync-badge {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 2px 10px 2px 8px;
  gap: 6px;
  width: 111px;
  height: 24px;
  background: #EAEAEA;
  border-radius: 4px;
  flex: none;
  order: 0;
  flex-grow: 0;
}

.sync-status-text {
  width: 64px;
  height: 16px;
  font-family: 'PolySans';
  font-style: normal;
  font-weight: 600;
  font-size: 12px;
  line-height: 16px;
  text-align: center;
  color: #838580;
  flex: none;
  order: 1;
  flex-grow: 0;
}

.sync-status-modified {
  width: 111px;
  height: 16px;
  font-family: 'Inter';
  font-style: normal;
  font-weight: 500;
  font-size: 10px;
  line-height: 16px;
  text-align: center;
  color: #546661;
  flex: none;
  order: 1;
  align-self: stretch;
  flex-grow: 0;
}

/* Provider Column */
.provider {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0px;
  gap: 4px;
  width: 91px;
  height: 34px;
  flex: none;
  order: 10;
  flex-grow: 0;
}

.provider-name {
  width: 84px;
  height: 14px;
  font-family: 'PolySans';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  color: #112A24;
  flex: none;
  order: 0;
  flex-grow: 0;
}

.provider-id {
  width: 91px;
  height: 16px;
  font-family: 'PolySans';
  font-style: normal;
  font-weight: 600;
  font-size: 12px;
  line-height: 16px;
  text-align: center;
  color: #B3B3B3;
  flex: none;
  order: 1;
  flex-grow: 0;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: #ffffff;
  border-top: 1px solid rgba(23, 83, 59, 0.12);
  min-width: 1200px;
}

.rows-per-page {
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: 'PolySans';
  font-size: 12px;
  color: #74827F;
}

.rows-per-page span:first-child {
  font-weight: 600;
  color: #112A24;
}

.page-info {
  font-family: 'PolySans';
  font-size: 12px;
  color: #74827F;
  font-weight: 600;
}

.pagination-controls {
  display: flex;
  gap: 8px;
}

.pagination-controls button {
  width: 32px;
  height: 32px;
  border: 1px solid #d1d5db;
  background: #ffffff;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.pagination-controls button:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.pagination-controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-controls button:disabled:hover {
  background: #ffffff;
  border-color: #d1d5db;
}
