{"name": "dntel-insurance", "version": "0.1.0", "private": true, "scripts": {"prebuild": "node scripts/generate-data.js", "dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint", "generate:data": "node scripts/generate-data.js"}, "dependencies": {"next": "15.5.2", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@faker-js/faker": "^9.9.0", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.2", "tailwindcss": "^4", "tsx": "^4.20.5", "typescript": "^5"}}