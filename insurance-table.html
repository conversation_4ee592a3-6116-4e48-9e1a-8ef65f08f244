<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DNTEL Insurance Management - Claims Table</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap');
        
        /* CSS Custom Properties for Theming */
        :root {
            --color-primary: #112A24;
            --color-secondary: #B3B3B3;
            --color-accent: #23A9EB;
            --color-success: #196443;
            --color-warning: #838580;
            --color-neutral: #74827F;
            --color-background: #FFFFFF;
            --color-border: rgba(23, 83, 59, 0.04);
            --color-shadow: rgba(23, 83, 59, 0.12);
            --color-badge-bg: #EBF9FE;
            --color-user-bg: #E0FEEF;
            --color-sync-bg: #EAEAEA;
            --color-text-muted: #546661;
            
            --font-primary: 'PolySans', 'Inter', system-ui, sans-serif;
            --font-secondary: 'Inter', system-ui, sans-serif;
            
            --spacing-xs: 4px;
            --spacing-sm: 8px;
            --spacing-md: 20px;
            --spacing-lg: 24px;
            
            --border-radius-sm: 4px;
            --border-radius-md: 14px;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-primary);
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }

        /* Table Container */
        .insurance-table-container {
            width: 100%;
            max-width: 1234px;
            margin: 2rem auto;
            background: var(--color-background);
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            overflow: hidden;
        }

        /* Table Header */
        .table-header {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid var(--color-border);
            padding: 16px var(--spacing-lg);
            font-weight: 600;
            font-size: 12px;
            color: var(--color-neutral);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .header-cell {
            flex: 1;
            text-align: left;
            min-width: 90px;
        }

        .header-cell:first-child { min-width: 120px; }
        .header-cell:nth-child(3) { min-width: 150px; }
        .header-cell:nth-child(10) { min-width: 130px; }

        /* Table Row */
        .table-row {
            display: flex;
            align-items: center;
            padding: var(--spacing-md) var(--spacing-lg);
            background: var(--color-background);
            border-bottom: 1px solid var(--color-border);
            box-shadow: 0px 1px 1px var(--color-shadow);
            transition: background-color 0.2s ease;
            min-height: 96px;
        }

        .table-row:hover {
            background: #f9fafb;
        }

        /* Column Styles */
        .patient-column {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);
            flex: 1;
            min-width: 120px;
        }

        .patient-name {
            font-family: var(--font-primary);
            font-weight: 400;
            font-size: 14px;
            line-height: 14px;
            color: var(--color-primary);
        }

        .patient-id {
            font-family: var(--font-primary);
            font-weight: 600;
            font-size: 12px;
            line-height: 12px;
            color: var(--color-secondary);
        }

        .service-date {
            display: flex;
            align-items: center;
            flex: 1;
            min-width: 90px;
        }

        .date-text {
            font-family: var(--font-primary);
            font-weight: 400;
            font-size: 14px;
            line-height: 14px;
            color: var(--color-primary);
        }

        .insurance-carrier {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
            flex: 1;
            min-width: 150px;
        }

        .carrier-name {
            font-family: var(--font-primary);
            font-weight: 400;
            font-size: 12px;
            line-height: 12px;
            color: var(--color-primary);
            margin-bottom: 8px;
        }

        .insurance-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 4px;
            background: var(--color-badge-bg);
            border-radius: var(--border-radius-sm);
            width: fit-content;
            max-width: 121px;
        }

        .badge-text {
            font-family: var(--font-primary);
            font-weight: 600;
            font-size: 12px;
            line-height: 16px;
            text-align: center;
            color: var(--color-accent);
        }

        .amount-column {
            display: flex;
            align-items: center;
            flex: 1;
            min-width: 80px;
        }

        .amount-text {
            font-family: var(--font-primary);
            font-weight: 400;
            font-size: 12px;
            line-height: 12px;
            color: var(--color-primary);
        }

        .status-column {
            display: flex;
            align-items: center;
            flex: 1;
            min-width: 120px;
        }

        .status-text {
            font-family: var(--font-primary);
            font-weight: 400;
            font-size: 12px;
            line-height: 12px;
            color: var(--color-primary);
        }

        .last-updated {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
            flex: 1;
            min-width: 100px;
        }

        .updated-date {
            font-family: var(--font-primary);
            font-weight: 400;
            font-size: 14px;
            line-height: 14px;
            color: var(--color-primary);
        }

        .updated-time {
            font-family: var(--font-primary);
            font-weight: 600;
            font-size: 11px;
            line-height: 11px;
            color: var(--color-neutral);
        }

        .user-column {
            display: flex;
            align-items: center;
            justify-content: center;
            flex: 1;
            min-width: 60px;
        }

        .user-avatar {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 28px;
            background: var(--color-user-bg);
            border-radius: var(--border-radius-md);
            font-family: var(--font-primary);
            font-weight: 600;
            font-size: 14px;
            line-height: 14px;
            color: var(--color-success);
        }

        .date-sent-column {
            display: flex;
            align-items: center;
            flex: 1;
            min-width: 100px;
        }

        .date-sent-orig-column {
            display: flex;
            align-items: center;
            flex: 1;
            min-width: 100px;
        }

        .pms-sync-column {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);
            flex: 1;
            min-width: 130px;
        }

        .pms-sync-badge {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            padding: 2px 10px 2px 8px;
            background: var(--color-sync-bg);
            border-radius: var(--border-radius-sm);
            width: fit-content;
            max-width: 111px;
        }

        .warning-icon {
            width: 15px;
            height: 15px;
            fill: var(--color-warning);
        }

        .sync-status-text {
            font-family: var(--font-primary);
            font-weight: 600;
            font-size: 12px;
            line-height: 16px;
            text-align: center;
            color: var(--color-warning);
        }

        .sync-status-modified {
            font-family: var(--font-secondary);
            font-weight: 500;
            font-size: 10px;
            line-height: 16px;
            text-align: center;
            color: var(--color-text-muted);
            margin-top: 2px;
        }

        .provider-column {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);
            flex: 1;
            min-width: 120px;
        }

        .provider-name {
            font-family: var(--font-primary);
            font-weight: 400;
            font-size: 14px;
            line-height: 14px;
            color: var(--color-primary);
        }

        .provider-id {
            font-family: var(--font-primary);
            font-weight: 600;
            font-size: 12px;
            line-height: 16px;
            color: var(--color-secondary);
        }

        /* Pagination */
        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px var(--spacing-lg);
            background: var(--color-background);
            border-top: 1px solid var(--color-border);
        }

        .rows-per-page {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-family: var(--font-primary);
            font-size: 12px;
            color: var(--color-neutral);
        }

        .rows-per-page span:first-child {
            font-weight: 600;
            color: var(--color-primary);
        }

        .page-info {
            font-family: var(--font-primary);
            font-size: 12px;
            color: var(--color-neutral);
            font-weight: 600;
        }

        .pagination-controls {
            display: flex;
            gap: var(--spacing-sm);
        }

        .pagination-controls button {
            width: 32px;
            height: 32px;
            border: 1px solid #d1d5db;
            background: var(--color-background);
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            color: #6b7280;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .pagination-controls button:hover:not(:disabled) {
            background: #f3f4f6;
            border-color: #9ca3af;
        }

        .pagination-controls button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Responsive Design */
        @media (max-width: 1280px) {
            .insurance-table-container {
                margin: 1rem;
                overflow-x: auto;
            }
            
            .table-header,
            .table-row {
                min-width: 1200px;
            }
        }

        @media (max-width: 768px) {
            .table-header,
            .table-row {
                padding: 12px 16px;
            }
            
            .header-cell,
            .patient-column,
            .service-date,
            .insurance-carrier,
            .amount-column,
            .status-column,
            .last-updated,
            .user-column,
            .date-sent-column,
            .date-sent-orig-column,
            .pms-sync-column,
            .provider-column {
                min-width: 80px;
                font-size: 11px;
            }
        }

        /* Accessibility */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation: none !important;
                transition: none !important;
            }
        }

        /* Focus states for accessibility */
        .table-row:focus {
            outline: 2px solid var(--color-accent);
            outline-offset: -2px;
        }

        .pagination-controls button:focus {
            outline: 2px solid var(--color-accent);
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <div class="min-h-screen bg-gray-50 py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">DNTEL Insurance Management</h1>
                <p class="mt-2 text-gray-600">Manage insurance claims and patient records</p>
            </div>

            <div class="insurance-table-container">
                <!-- Table Header -->
                <div class="table-header">
                    <div class="header-cell">Patient</div>
                    <div class="header-cell">Service Date</div>
                    <div class="header-cell">Insurance Carrier</div>
                    <div class="header-cell">Amount</div>
                    <div class="header-cell">Status</div>
                    <div class="header-cell">Last Updated</div>
                    <div class="header-cell">User</div>
                    <div class="header-cell">Date Sent</div>
                    <div class="header-cell">Date Sent Orig</div>
                    <div class="header-cell">PMS Sync Status</div>
                    <div class="header-cell">Provider</div>
                </div>

                <!-- Table Rows -->
                <div class="table-row" tabindex="0">
                    <div class="patient-column">
                        <div class="patient-name">First Last</div>
                        <div class="patient-id">ID: 11060</div>
                    </div>
                    <div class="service-date">
                        <div class="date-text">July 00, 2025</div>
                    </div>
                    <div class="insurance-carrier">
                        <div class="carrier-name">BCBS OF COLORADO FEP PPO INN</div>
                        <div class="insurance-badge">
                            <span class="badge-text">Primary</span>
                        </div>
                    </div>
                    <div class="amount-column">
                        <div class="amount-text">$00,000</div>
                    </div>
                    <div class="status-column">
                        <div class="status-text">NCOF - RESUBMITTED</div>
                    </div>
                    <div class="last-updated">
                        <div class="updated-date">May 28, 2030</div>
                        <div class="updated-time">11:36 AM</div>
                    </div>
                    <div class="user-column">
                        <div class="user-avatar">AA</div>
                    </div>
                    <div class="date-sent-column">
                        <div class="date-text">Aug 28, 2025</div>
                    </div>
                    <div class="date-sent-orig-column">
                        <div class="date-text">Aug 28, 2025</div>
                    </div>
                    <div class="pms-sync-column">
                        <div class="pms-sync-badge">
                            <svg class="warning-icon" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M7.5 1.5L1.5 13.5H13.5L7.5 1.5Z" stroke="currentColor" stroke-width="1" fill="none"/>
                                <circle cx="7.5" cy="11" r="0.5" fill="currentColor"/>
                                <line x1="7.5" y1="6" x2="7.5" y2="9" stroke="currentColor" stroke-width="1"/>
                            </svg>
                            <span class="sync-status-text">Not synced</span>
                        </div>
                        <div class="sync-status-modified">Status modified today</div>
                    </div>
                    <div class="provider-column">
                        <div class="provider-name">Dr. First Last</div>
                        <div class="provider-id">ID:56712349911</div>
                    </div>
                </div>

                <!-- Repeat the same pattern for other rows -->
                <div class="table-row" tabindex="0">
                    <div class="patient-column">
                        <div class="patient-name">First Last</div>
                        <div class="patient-id">ID: 11060</div>
                    </div>
                    <div class="service-date">
                        <div class="date-text">July 00, 2025</div>
                    </div>
                    <div class="insurance-carrier">
                        <div class="carrier-name">BCBS OF COLORADO FEP PPO INN</div>
                        <div class="insurance-badge">
                            <span class="badge-text">Primary</span>
                        </div>
                    </div>
                    <div class="amount-column">
                        <div class="amount-text">$00,000</div>
                    </div>
                    <div class="status-column">
                        <div class="status-text">NCOF - RESUBMITTED</div>
                    </div>
                    <div class="last-updated">
                        <div class="updated-date">May 28, 2030</div>
                        <div class="updated-time">11:36 AM</div>
                    </div>
                    <div class="user-column">
                        <div class="user-avatar">AA</div>
                    </div>
                    <div class="date-sent-column">
                        <div class="date-text">Aug 28, 2025</div>
                    </div>
                    <div class="date-sent-orig-column">
                        <div class="date-text">Aug 28, 2025</div>
                    </div>
                    <div class="pms-sync-column">
                        <div class="pms-sync-badge">
                            <svg class="warning-icon" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M7.5 1.5L1.5 13.5H13.5L7.5 1.5Z" stroke="currentColor" stroke-width="1" fill="none"/>
                                <circle cx="7.5" cy="11" r="0.5" fill="currentColor"/>
                                <line x1="7.5" y1="6" x2="7.5" y2="9" stroke="currentColor" stroke-width="1"/>
                            </svg>
                            <span class="sync-status-text">Not synced</span>
                        </div>
                        <div class="sync-status-modified">Status modified today</div>
                    </div>
                    <div class="provider-column">
                        <div class="provider-name">Dr. First Last</div>
                        <div class="provider-id">ID:56712349911</div>
                    </div>
                </div>

                <div class="table-row" tabindex="0">
                    <div class="patient-column">
                        <div class="patient-name">First Last</div>
                        <div class="patient-id">ID: 11060</div>
                    </div>
                    <div class="service-date">
                        <div class="date-text">July 00, 2025</div>
                    </div>
                    <div class="insurance-carrier">
                        <div class="carrier-name">BCBS OF COLORADO FEP PPO INN</div>
                        <div class="insurance-badge">
                            <span class="badge-text">Primary</span>
                        </div>
                    </div>
                    <div class="amount-column">
                        <div class="amount-text">$00,000</div>
                    </div>
                    <div class="status-column">
                        <div class="status-text">NCOF - RESUBMITTED</div>
                    </div>
                    <div class="last-updated">
                        <div class="updated-date">May 28, 2030</div>
                        <div class="updated-time">11:36 AM</div>
                    </div>
                    <div class="user-column">
                        <div class="user-avatar">AA</div>
                    </div>
                    <div class="date-sent-column">
                        <div class="date-text">Aug 28, 2025</div>
                    </div>
                    <div class="date-sent-orig-column">
                        <div class="date-text">Aug 28, 2025</div>
                    </div>
                    <div class="pms-sync-column">
                        <div class="pms-sync-badge">
                            <svg class="warning-icon" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M7.5 1.5L1.5 13.5H13.5L7.5 1.5Z" stroke="currentColor" stroke-width="1" fill="none"/>
                                <circle cx="7.5" cy="11" r="0.5" fill="currentColor"/>
                                <line x1="7.5" y1="6" x2="7.5" y2="9" stroke="currentColor" stroke-width="1"/>
                            </svg>
                            <span class="sync-status-text">Not synced</span>
                        </div>
                        <div class="sync-status-modified">Status modified today</div>
                    </div>
                    <div class="provider-column">
                        <div class="provider-name">Dr. First Last</div>
                        <div class="provider-id">ID:56712349911</div>
                    </div>
                </div>

                <div class="table-row" tabindex="0">
                    <div class="patient-column">
                        <div class="patient-name">First Last</div>
                        <div class="patient-id">ID: 11060</div>
                    </div>
                    <div class="service-date">
                        <div class="date-text">July 00, 2025</div>
                    </div>
                    <div class="insurance-carrier">
                        <div class="carrier-name">BCBS OF COLORADO FEP PPO INN</div>
                        <div class="insurance-badge">
                            <span class="badge-text">Primary</span>
                        </div>
                    </div>
                    <div class="amount-column">
                        <div class="amount-text">$00,000</div>
                    </div>
                    <div class="status-column">
                        <div class="status-text">NCOF - RESUBMITTED</div>
                    </div>
                    <div class="last-updated">
                        <div class="updated-date">May 28, 2030</div>
                        <div class="updated-time">11:36 AM</div>
                    </div>
                    <div class="user-column">
                        <div class="user-avatar">AA</div>
                    </div>
                    <div class="date-sent-column">
                        <div class="date-text">Aug 28, 2025</div>
                    </div>
                    <div class="date-sent-orig-column">
                        <div class="date-text">Aug 28, 2025</div>
                    </div>
                    <div class="pms-sync-column">
                        <div class="pms-sync-badge">
                            <svg class="warning-icon" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M7.5 1.5L1.5 13.5H13.5L7.5 1.5Z" stroke="currentColor" stroke-width="1" fill="none"/>
                                <circle cx="7.5" cy="11" r="0.5" fill="currentColor"/>
                                <line x1="7.5" y1="6" x2="7.5" y2="9" stroke="currentColor" stroke-width="1"/>
                            </svg>
                            <span class="sync-status-text">Not synced</span>
                        </div>
                        <div class="sync-status-modified">Status modified today</div>
                    </div>
                    <div class="provider-column">
                        <div class="provider-name">Dr. First Last</div>
                        <div class="provider-id">ID:56712349911</div>
                    </div>
                </div>

                <div class="table-row" tabindex="0">
                    <div class="patient-column">
                        <div class="patient-name">First Last</div>
                        <div class="patient-id">ID: 11060</div>
                    </div>
                    <div class="service-date">
                        <div class="date-text">July 00, 2025</div>
                    </div>
                    <div class="insurance-carrier">
                        <div class="carrier-name">BCBS OF COLORADO FEP PPO INN</div>
                        <div class="insurance-badge">
                            <span class="badge-text">Primary</span>
                        </div>
                    </div>
                    <div class="amount-column">
                        <div class="amount-text">$00,000</div>
                    </div>
                    <div class="status-column">
                        <div class="status-text">NCOF - RESUBMITTED</div>
                    </div>
                    <div class="last-updated">
                        <div class="updated-date">May 28, 2030</div>
                        <div class="updated-time">11:36 AM</div>
                    </div>
                    <div class="user-column">
                        <div class="user-avatar">AA</div>
                    </div>
                    <div class="date-sent-column">
                        <div class="date-text">Aug 28, 2025</div>
                    </div>
                    <div class="date-sent-orig-column">
                        <div class="date-text">Aug 28, 2025</div>
                    </div>
                    <div class="pms-sync-column">
                        <div class="pms-sync-badge">
                            <svg class="warning-icon" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M7.5 1.5L1.5 13.5H13.5L7.5 1.5Z" stroke="currentColor" stroke-width="1" fill="none"/>
                                <circle cx="7.5" cy="11" r="0.5" fill="currentColor"/>
                                <line x1="7.5" y1="6" x2="7.5" y2="9" stroke="currentColor" stroke-width="1"/>
                            </svg>
                            <span class="sync-status-text">Not synced</span>
                        </div>
                        <div class="sync-status-modified">Status modified today</div>
                    </div>
                    <div class="provider-column">
                        <div class="provider-name">Dr. First Last</div>
                        <div class="provider-id">ID:56712349911</div>
                    </div>
                </div>

                <div class="table-row" tabindex="0">
                    <div class="patient-column">
                        <div class="patient-name">First Last</div>
                        <div class="patient-id">ID: 11060</div>
                    </div>
                    <div class="service-date">
                        <div class="date-text">July 00, 2025</div>
                    </div>
                    <div class="insurance-carrier">
                        <div class="carrier-name">BCBS OF COLORADO FEP PPO INN</div>
                        <div class="insurance-badge">
                            <span class="badge-text">Primary</span>
                        </div>
                    </div>
                    <div class="amount-column">
                        <div class="amount-text">$00,000</div>
                    </div>
                    <div class="status-column">
                        <div class="status-text">NCOF - RESUBMITTED</div>
                    </div>
                    <div class="last-updated">
                        <div class="updated-date">May 28, 2030</div>
                        <div class="updated-time">11:36 AM</div>
                    </div>
                    <div class="user-column">
                        <div class="user-avatar">AA</div>
                    </div>
                    <div class="date-sent-column">
                        <div class="date-text">Aug 28, 2025</div>
                    </div>
                    <div class="date-sent-orig-column">
                        <div class="date-text">Aug 28, 2025</div>
                    </div>
                    <div class="pms-sync-column">
                        <div class="pms-sync-badge">
                            <svg class="warning-icon" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M7.5 1.5L1.5 13.5H13.5L7.5 1.5Z" stroke="currentColor" stroke-width="1" fill="none"/>
                                <circle cx="7.5" cy="11" r="0.5" fill="currentColor"/>
                                <line x1="7.5" y1="6" x2="7.5" y2="9" stroke="currentColor" stroke-width="1"/>
                            </svg>
                            <span class="sync-status-text">Not synced</span>
                        </div>
                        <div class="sync-status-modified">Status modified today</div>
                    </div>
                    <div class="provider-column">
                        <div class="provider-name">Dr. First Last</div>
                        <div class="provider-id">ID:56712349911</div>
                    </div>
                </div>

                <div class="table-row" tabindex="0">
                    <div class="patient-column">
                        <div class="patient-name">First Last</div>
                        <div class="patient-id">ID: 11060</div>
                    </div>
                    <div class="service-date">
                        <div class="date-text">July 00, 2025</div>
                    </div>
                    <div class="insurance-carrier">
                        <div class="carrier-name">BCBS OF COLORADO FEP PPO INN</div>
                        <div class="insurance-badge">
                            <span class="badge-text">Primary</span>
                        </div>
                    </div>
                    <div class="amount-column">
                        <div class="amount-text">$00,000</div>
                    </div>
                    <div class="status-column">
                        <div class="status-text">NCOF - RESUBMITTED</div>
                    </div>
                    <div class="last-updated">
                        <div class="updated-date">May 28, 2030</div>
                        <div class="updated-time">11:36 AM</div>
                    </div>
                    <div class="user-column">
                        <div class="user-avatar">AA</div>
                    </div>
                    <div class="date-sent-column">
                        <div class="date-text">Aug 28, 2025</div>
                    </div>
                    <div class="date-sent-orig-column">
                        <div class="date-text">Aug 28, 2025</div>
                    </div>
                    <div class="pms-sync-column">
                        <div class="pms-sync-badge">
                            <svg class="warning-icon" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M7.5 1.5L1.5 13.5H13.5L7.5 1.5Z" stroke="currentColor" stroke-width="1" fill="none"/>
                                <circle cx="7.5" cy="11" r="0.5" fill="currentColor"/>
                                <line x1="7.5" y1="6" x2="7.5" y2="9" stroke="currentColor" stroke-width="1"/>
                            </svg>
                            <span class="sync-status-text">Not synced</span>
                        </div>
                        <div class="sync-status-modified">Status modified today</div>
                    </div>
                    <div class="provider-column">
                        <div class="provider-name">Dr. First Last</div>
                        <div class="provider-id">ID:56712349911</div>
                    </div>
                </div>

                <!-- Pagination -->
                <div class="pagination">
                    <div class="rows-per-page">
                        <span>20</span>
                        <span>Rows per page</span>
                    </div>
                    <div class="page-info">
                        <span>Page 1 of 8</span>
                    </div>
                    <div class="pagination-controls">
                        <button disabled title="First page" aria-label="Go to first page">«</button>
                        <button disabled title="Previous page" aria-label="Go to previous page">‹</button>
                        <button title="Next page" aria-label="Go to next page">›</button>
                        <button title="Last page" aria-label="Go to last page">»</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simple pagination functionality
        document.addEventListener('DOMContentLoaded', function() {
            const paginationButtons = document.querySelectorAll('.pagination-controls button');
            
            paginationButtons.forEach(button => {
                button.addEventListener('click', function() {
                    if (!this.disabled) {
                        console.log('Pagination button clicked:', this.textContent);
                        // Add pagination logic here
                    }
                });
            });

            // Add keyboard navigation for table rows
            const tableRows = document.querySelectorAll('.table-row');
            tableRows.forEach(row => {
                row.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        console.log('Table row selected');
                        // Add row selection logic here
                    }
                });
            });
        });
    </script>
</body>
</html>
