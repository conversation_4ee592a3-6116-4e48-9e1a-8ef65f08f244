# Insurance Claims Table Components

This project implements a modular, reusable component system for displaying insurance claims data. The architecture separates concerns by creating dedicated components for each column type, improving maintainability and testability.

## Component Architecture

### Core Components

#### 1. **DataTable** (`src/components/DataTable.tsx`)
The main container component that handles table layout and structure.
- Uses CSS Grid for responsive layout
- Automatically calculates minimum widths
- Provides type-safe column configuration
- Renders header row and delegates data rendering to ClaimRow

#### 2. **ClaimRow** (`src/components/ClaimRow.tsx`)
Orchestrates the rendering of a single claim record.
- Parses complex data structures (multi-line text, nested information)
- Routes each column to its appropriate specialized component
- Maintains grid layout consistency
- Provides data transformation utilities

### Column Components (`src/components/columns/`)

Each column type has its own dedicated component for maximum reusability:

#### **PatientProviderColumn**
- **Purpose**: Displays patient and provider information
- **Format**: Name on top, ID below in gray
- **Props**: `name: string`, `id: string`
- **Styling**: Main text in black, ID in gray (#546661)

#### **InsuranceCarrierColumn**
- **Purpose**: Shows insurance carrier details
- **Format**: Carrier name, plan type, category (with blue accent)
- **Props**: `carrierName: string`, `planType: string`, `planCategory: string`
- **Styling**: Carrier name in black, plan type in gray, category in blue (#4A9EFF)

#### **LastUpdatedColumn**
- **Purpose**: Displays date and time information
- **Format**: Date on top, time below in gray
- **Props**: `date: string`, `time: string`
- **Styling**: Date in black, time in gray

#### **UserColumn**
- **Purpose**: Shows user identification with avatar
- **Format**: Circular avatar with initials
- **Props**: `initials: string`, `backgroundColor?: string`, `textColor?: string`
- **Styling**: Customizable colors (default: green background, white text)

#### **PmsSyncStatusColumn**
- **Purpose**: Displays sync status with visual indicators
- **Format**: Icon + status text, description below
- **Props**: `status: string`, `description: string`, `icon?: string`, `statusColor?: string`
- **Styling**: Colored status text, gray description

## Data Structure

### ClaimRowData Interface (Updated for Better Data Separation)
```typescript
interface ClaimRowData {
  patient: {
    name: string;
    id: string;
  };
  serviceDate: string;
  insuranceCarrier: {
    carrierName: string;
    planType?: string;
    planCategory: string;
  };
  amount: string;
  status: string;
  lastUpdated: {
    date: string;
    time: string;
  };
  user: string;
  dateSent: string;
  dateSentOrig: string;
  pmsSyncStatus: {
    status: string;
    description: string;
    isSynced: boolean;
  };
  provider: {
    name: string;
    id: string;
  };
}
```

**Key Improvements:**
- ✅ **Structured Data**: No more newline (`\n`) characters in data - fields are properly separated
- ✅ **Type Safety**: Nested objects with specific field types
- ✅ **Better Maintainability**: Clear data structure makes it easier to work with
- ✅ **Vertical Alignment**: All column components use `flex flex-col justify-center min-h-[60px]` for consistent vertical centering

## Usage Example

```tsx
import DataTable, { TableColumn } from "../components/DataTable";
import { ClaimRowData } from "../components/ClaimRow";

const columns: TableColumn[] = [
  { key: 'patient', label: 'Patient', width: '72px' },
  { key: 'serviceDate', label: 'Service Date', width: '92px' },
  // ... other columns
];

const data: ClaimRowData[] = [
  {
    patient: {
      name: 'John Doe',
      id: '12345'
    },
    serviceDate: 'July 15, 2025',
    insuranceCarrier: {
      carrierName: 'BCBS',
      planCategory: 'Primary'
    },
    lastUpdated: {
      date: 'Sep 01, 2025',
      time: '02:15 PM'
    },
    pmsSyncStatus: {
      status: 'Synced',
      description: 'Last sync: 2 hours ago',
      isSynced: true
    },
    provider: {
      name: 'Dr. Jane Smith',
      id: '98765'
    },
    // ... other fields
  }
];

<DataTable columns={columns} data={data} />
```

## Key Benefits

1. **Modularity**: Each column type is a separate, testable component
2. **Reusability**: Column components can be used independently
3. **Type Safety**: Full TypeScript support with proper interfaces
4. **Maintainability**: Changes to specific column types are isolated
5. **Consistency**: Centralized styling and behavior for each column type
6. **Flexibility**: Easy to add new column types or modify existing ones

## Testing Strategy

Each column component can be tested independently:

```tsx
// Example test for PatientProviderColumn
import { render, screen } from '@testing-library/react';
import PatientProviderColumn from './PatientProviderColumn';

test('renders patient name and ID', () => {
  render(<PatientProviderColumn name="John Doe" id="12345" />);
  expect(screen.getByText('John Doe')).toBeInTheDocument();
  expect(screen.getByText('ID: 12345')).toBeInTheDocument();
});
```

## Styling

- Uses Tailwind CSS with custom color values
- Maintains design system consistency
- Responsive layout with CSS Grid
- Custom font (PolySans) integration

## Future Enhancements

- Add sorting capabilities to column headers
- Implement filtering for each column type
- Add pagination support
- Create additional column types as needed
- Add accessibility features (ARIA labels, keyboard navigation)
