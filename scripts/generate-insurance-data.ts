import { faker } from '@faker-js/faker';
import fs from 'fs';
import path from 'path';

/**
 * Generate dummy insurance claims data for the application
 * This script runs at build time to create static data
 */

// Set seed for reproducible data across builds
faker.seed(42);

// Predefined arrays for realistic distributions
const INSURANCE_CARRIERS = [
  'Aetna', 'Anthem', 'Blue Cross Blue Shield', 'Cigna', 'UnitedHealth',
  'Humana', 'Kaiser Permanente', 'MetLife', 'Allstate', 'State Farm',
  'Progressive', 'GEICO', 'Liberty Mutual', 'Nationwide', 'Farmers'
] as const;

const CLAIM_STATUSES = [
  'PAID',
  'NCOF - RESUBMITTED', 
  'PENDING',
  'DENIED',
  'PROCESSING'
] as const;

const SYNC_STATUSES = [
  'Synced',
  'Not synced', 
  'Syncing',
  'Error'
] as const;

const PLAN_CATEGORIES = ['Primary', 'Secondary'] as const;

const MEDICAL_SPECIALTIES = [
  'Primary Care', 'Cardiology', 'Dermatology', 'Orthopedics', 
  'Neurology', 'Oncology', 'Psychiatry', 'Radiology',
  'Emergency Medicine', 'Surgery', 'Pediatrics', 'OB/GYN'
] as const;

// Types matching ClaimRow component
type PlanCategory = 'Primary' | 'Secondary';
type ClaimStatus = 'PAID' | 'NCOF - RESUBMITTED' | 'PENDING' | 'DENIED' | 'PROCESSING';
type SyncStatus = 'Synced' | 'Not synced' | 'Syncing' | 'Error';
type CurrencyAmount = `$${string}`;
type TimeString = `${number}:${number} ${'AM' | 'PM'}`;

interface ClaimRowData {
  id: string;
  patient: {
    name: string;
    id: string;
  };
  serviceDate: string;
  insuranceCarrier: {
    carrierName: string;
    planCategory: PlanCategory;
  };
  amount: CurrencyAmount;
  status: ClaimStatus;
  lastUpdated: {
    date: string;
    time: TimeString;
  };
  user: string;
  dateSent: string;
  dateSentOrig: string;
  pmsSyncStatus: {
    status: SyncStatus;
    description: string;
    isSynced: boolean;
  };
  provider: {
    name: string;
    id: string;
  };
}

interface GeneratedData {
  metadata: {
    generatedAt: string;
    totalRecords: number;
    fakerSeed: number;
    version: string;
  };
  claims: ClaimRowData[];
}

/**
 * Generate a realistic time string in 12-hour format
 */
function generateTimeString(): TimeString {
  const hour = faker.number.int({ min: 1, max: 12 });
  const minute = faker.number.int({ min: 0, max: 59 });
  const period = faker.helpers.arrayElement(['AM', 'PM']);
  const formattedMinute = minute.toString().padStart(2, '0');
  return `${hour}:${formattedMinute} ${period}` as TimeString;
}

/**
 * Generate a realistic currency amount
 */
function generateCurrencyAmount(): CurrencyAmount {
  const amount = faker.number.float({ 
    min: 25.00, 
    max: 15000.00, 
    fractionDigits: 2
  });
  return `$${amount.toFixed(2)}` as CurrencyAmount;
}

/**
 * Generate user initials (2 letters)
 */
function generateUserInitials(): string {
  const firstName = faker.person.firstName();
  const lastName = faker.person.lastName();
  return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
}

/**
 * Generate a realistic date within the last 6 months
 */
function generateRecentDate(): string {
  const date = faker.date.recent({ days: 180 });
  return date.toLocaleDateString('en-US', { 
    month: '2-digit', 
    day: '2-digit', 
    year: 'numeric' 
  });
}

/**
 * Generate sync status description based on status
 */
function generateSyncDescription(status: SyncStatus): string {
  const descriptions: Record<SyncStatus, string> = {
    'Synced': 'Successfully synchronized with PMS',
    'Not synced': 'Pending synchronization with PMS',
    'Syncing': 'Currently synchronizing with PMS',
    'Error': 'Failed to synchronize - manual review required'
  };
  return descriptions[status];
}

/**
 * Generate a single insurance claim record
 */
function generateClaimRecord(index: number): ClaimRowData {
  const syncStatus = faker.helpers.arrayElement(SYNC_STATUSES);
  const serviceDate = generateRecentDate();
  
  return {
    id: `CLAIM-${(index + 1).toString().padStart(6, '0')}`,
    patient: {
      name: faker.person.fullName(),
      id: `PAT-${faker.number.int({ min: 100000, max: 999999 })}`
    },
    serviceDate,
    insuranceCarrier: {
      carrierName: faker.helpers.arrayElement(INSURANCE_CARRIERS),
      planCategory: faker.helpers.arrayElement(PLAN_CATEGORIES)
    },
    amount: generateCurrencyAmount(),
    status: faker.helpers.arrayElement(CLAIM_STATUSES),
    lastUpdated: {
      date: faker.helpers.maybe(() => generateRecentDate(), { probability: 0.8 }) || serviceDate,
      time: generateTimeString()
    },
    user: generateUserInitials(),
    dateSent: faker.helpers.maybe(() => generateRecentDate(), { probability: 0.7 }) || '',
    dateSentOrig: faker.helpers.maybe(() => generateRecentDate(), { probability: 0.6 }) || '',
    pmsSyncStatus: {
      status: syncStatus,
      description: generateSyncDescription(syncStatus),
      isSynced: syncStatus === 'Synced'
    },
    provider: {
      name: `${faker.helpers.arrayElement(MEDICAL_SPECIALTIES)} Associates`,
      id: `PROV-${faker.number.int({ min: 1000, max: 9999 })}`
    }
  };
}

/**
 * Generate the complete dataset
 */
export function generateInsuranceData(): ClaimRowData[] {
  console.log('🏥 Generating insurance claims data...');
  
  const totalRecords = 300;
  const data: ClaimRowData[] = [];
  
  for (let i = 0; i < totalRecords; i++) {
    data.push(generateClaimRecord(i));
    
    // Progress indicator
    if ((i + 1) % 50 === 0) {
      console.log(`✅ Generated ${i + 1}/${totalRecords} records`);
    }
  }
  
  return data;
}

/**
 * Ensure the data directory exists
 */
function ensureDataDirectory(): void {
  const dataDir = path.join(process.cwd(), 'src', 'data');
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
    console.log('📁 Created data directory');
  }
}

/**
 * Write data to JSON file
 */
function writeDataToFile(data: ClaimRowData[]): void {
  const filePath = path.join(process.cwd(), 'src', 'data', 'insurance-claims.json');
  
  const jsonData: GeneratedData = {
    metadata: {
      generatedAt: new Date().toISOString(),
      totalRecords: data.length,
      fakerSeed: 42,
      version: '1.0.0'
    },
    claims: data
  };
  
  fs.writeFileSync(filePath, JSON.stringify(jsonData, null, 2), 'utf8');
  console.log(`💾 Written ${data.length} records to ${filePath}`);
}

/**
 * Main execution function
 */
export function main(): void {
  try {
    console.log('🚀 Starting insurance data generation...');
    console.log('📊 Target: 300 insurance claim records\n');
    
    ensureDataDirectory();
    const data = generateInsuranceData();
    writeDataToFile(data);
    
    console.log('\n✨ Insurance data generation completed successfully!');
    console.log(`📈 Generated ${data.length} unique insurance claim records`);
    console.log('🔄 Data will be regenerated on each build for consistency');
    
  } catch (error) {
    console.error('❌ Error generating insurance data:', error);
    process.exit(1);
  }
}

// Export types for use in components
export type { ClaimRowData, GeneratedData, PlanCategory, ClaimStatus, SyncStatus, CurrencyAmount, TimeString };

// Execute main function if this file is run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
