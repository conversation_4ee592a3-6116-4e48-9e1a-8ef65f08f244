const { faker } = require('@faker-js/faker');
const fs = require('fs');
const path = require('path');

// Set seed for reproducible data
faker.seed(42);

// Constants
const INSURANCE_CARRIERS = [
  '<PERSON><PERSON>na', 'Anthem', 'Blue Cross Blue Shield', 'Cigna', 'UnitedHealth',
  'Humana', 'Kaiser Permanente', 'MetLife', 'Allstate', 'State Farm',
  'Progressive', 'GEICO', 'Liberty Mutual', 'Nationwide', 'Farmers'
];

const CLAIM_STATUSES = ['PAID', 'NCOF - RESUBMITTED', 'PENDING', 'DENIED', 'PROCESSING'];
const SYNC_STATUSES = ['Synced', 'Not synced', 'Syncing', 'Error'];
const PLAN_CATEGORIES = ['Primary', 'Secondary'];
const MEDICAL_SPECIALTIES = [
  'Primary Care', 'Cardiology', 'Dermatology', 'Orthopedics', 
  'Neurology', 'Oncology', 'Psychiatry', 'Radiology',
  'Emergency Medicine', 'Surgery', 'Pediatrics', 'OB/GYN'
];

function generateTimeString() {
  const hour = faker.number.int({ min: 1, max: 12 });
  const minute = faker.number.int({ min: 0, max: 59 });
  const period = faker.helpers.arrayElement(['AM', 'PM']);
  const formattedMinute = minute.toString().padStart(2, '0');
  return `${hour}:${formattedMinute} ${period}`;
}

function generateCurrencyAmount() {
  const amount = faker.number.float({ min: 25.00, max: 15000.00, fractionDigits: 2 });
  return `$${amount.toFixed(2)}`;
}

function generateUserInitials() {
  const firstName = faker.person.firstName();
  const lastName = faker.person.lastName();
  return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
}

function generateRecentDate() {
  const date = faker.date.recent({ days: 180 });
  return date.toLocaleDateString('en-US', { 
    month: '2-digit', 
    day: '2-digit', 
    year: 'numeric' 
  });
}

function generateSyncDescription(status) {
  const descriptions = {
    'Synced': 'Successfully synchronized with PMS',
    'Not synced': 'Pending synchronization with PMS',
    'Syncing': 'Currently synchronizing with PMS',
    'Error': 'Failed to synchronize - manual review required'
  };
  return descriptions[status];
}

function generateClaimRecord(index) {
  const syncStatus = faker.helpers.arrayElement(SYNC_STATUSES);
  const serviceDate = generateRecentDate();
  
  return {
    id: `CLAIM-${(index + 1).toString().padStart(6, '0')}`,
    patient: {
      name: faker.person.fullName(),
      id: `PAT-${faker.number.int({ min: 100000, max: 999999 })}`
    },
    serviceDate,
    insuranceCarrier: {
      carrierName: faker.helpers.arrayElement(INSURANCE_CARRIERS),
      planCategory: faker.helpers.arrayElement(PLAN_CATEGORIES)
    },
    amount: generateCurrencyAmount(),
    status: faker.helpers.arrayElement(CLAIM_STATUSES),
    lastUpdated: {
      date: faker.helpers.maybe(() => generateRecentDate(), { probability: 0.8 }) || serviceDate,
      time: generateTimeString()
    },
    user: generateUserInitials(),
    dateSent: faker.helpers.maybe(() => generateRecentDate(), { probability: 0.7 }) || '',
    dateSentOrig: faker.helpers.maybe(() => generateRecentDate(), { probability: 0.6 }) || '',
    pmsSyncStatus: {
      status: syncStatus,
      description: generateSyncDescription(syncStatus),
      isSynced: syncStatus === 'Synced'
    },
    provider: {
      name: `${faker.helpers.arrayElement(MEDICAL_SPECIALTIES)} Associates`,
      id: `PROV-${faker.number.int({ min: 1000, max: 9999 })}`
    }
  };
}

function main() {
  console.log('🚀 Starting insurance data generation...');
  console.log('📊 Target: 300 insurance claim records\n');
  
  // Ensure data directory exists
  const dataDir = path.join(process.cwd(), 'src', 'data');
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
    console.log('📁 Created data directory');
  }
  
  // Generate data
  const data = [];
  for (let i = 0; i < 300; i++) {
    data.push(generateClaimRecord(i));
    if ((i + 1) % 50 === 0) {
      console.log(`✅ Generated ${i + 1}/300 records`);
    }
  }
  
  // Write to file
  const jsonData = {
    metadata: {
      generatedAt: new Date().toISOString(),
      totalRecords: data.length,
      fakerSeed: 42,
      version: '1.0.0'
    },
    claims: data
  };
  
  const filePath = path.join(dataDir, 'insurance-claims.json');
  fs.writeFileSync(filePath, JSON.stringify(jsonData, null, 2), 'utf8');
  
  console.log(`💾 Written ${data.length} records to ${filePath}`);
  console.log('\n✨ Insurance data generation completed successfully!');
  console.log(`📈 Generated ${data.length} unique insurance claim records`);
}

main();
